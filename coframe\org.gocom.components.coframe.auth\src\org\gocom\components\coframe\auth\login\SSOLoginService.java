package org.gocom.components.coframe.auth.login;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64.Decoder;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.codec.binary.Base64;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.gocom.components.coframe.auth.SysConfig;

import com.alibaba.fastjson.JSONObject;
import com.eos.foundation.database.DatabaseExt;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;

import commonj.sdo.DataObject;

public class SSOLoginService {

	private static Logger logger = TraceLoggerFactory.getLogger(SSOLoginService.class);

	/**
	 * 鑫亿获取登录信息
	 */
	@Bizlet
	public static String XinYiRMSSOLogin(String ticket) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		JSONObject json = null;
		try {
			// 用java JDK自带的URL去请求
			URL url = new URL("http://192.0.2.16/comm/api/ssoLogin?appGroup=SPD&appName=SPD&ticket=" + ticket);
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("GET");
			connection.setRequestProperty("Accept", "*/*");
			connection.connect();
			InputStream is = connection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
			}
			reader.close();
			json = JSONObject.parseObject(sbf.toString());
			JSONObject resp = (JSONObject) json.get("response");
			if (resp.getInteger("flag") == 1 && resp.getInteger("login") == 1) {
				JSONObject result = new JSONObject();
				String pwdBase = (String) resp.get("password");
				byte[] pwdByte = Base64.decodeBase64(pwdBase.getBytes("UTF-8"));
				result.put("userId", resp.get("userId"));
				result.put("password", new String(pwdByte));
				System.out.println("ssoResult=====>" + result.toJSONString());
				return result.toString();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 宜兴人民医院获取登录名信息
	 */
	@Bizlet
	public static String YiXinRMSSOLogin(String portalid) {
		// BASE64解密
		Decoder decoder = java.util.Base64.getDecoder();
		byte[] bytes = decoder.decode(portalid);
		String userId = new String(bytes);
		System.out.println("BASE64解密：" + userId);
		return userId.replace("staff", "");
	}

	/**
	 * 泰康仙林校验用户token
	 */
	@Bizlet
	public static boolean TKXLSSOLogin(String usertoken, String username) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("username", username);
		Object[] objects = DatabaseExt.queryByNamedSql("default",
				"org.gocom.components.coframe.auth.ssoLogin.tkxlssologin", map);
		DataObject object = (DataObject) objects[0];
		String token = object.getString("token");
		String name = object.getString("username");
		if (token != null && name != null) {
			if (token.equals(usertoken) && name.equals(username)) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	@Bizlet
	public static String SanShuiQuRMSSOLogin(String code) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		JSONObject json = null;
		try {
			logger.info("单点登录code ==>" + code);
			// 用java JDK自带的URL去请求
			URL restUrl = new URL("http://192.168.7.150:81/fims/api/rest/tokenValidation");
			HttpURLConnection restConnection = (HttpURLConnection) restUrl.openConnection();
			restConnection.setRequestMethod("POST");
			restConnection.setRequestProperty("code", "wanxuspd");
			restConnection.setRequestProperty("token", code);
			restConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
			restConnection.setRequestProperty("Charset", "UTF-8");
			// 设置超时时间
			restConnection.setConnectTimeout(10 * 1000);
			restConnection.setReadTimeout(30 * 1000);
			restConnection.setDoOutput(true);
			restConnection.setDoInput(true);// 设置是否从HttpURLConnection输入，默认值为 true
			logger.info("准备连接");
			restConnection.connect();
			logger.info("连接成功 ");
			InputStream is = restConnection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
			}
			reader.close();
			json = JSONObject.parseObject(sbf.toString());
			logger.info("单点登录验证返回信息 ==>" + json.toJSONString());
			if ("true".equals(json.getString("success"))) {
				JSONObject info = (JSONObject) json.get("data");
				logger.info("单点登录登录JSON ==>" + info);
				logger.info("单点登录登录用户名为 ==>" + info.getString("userName"));
				return info.getString("userName");
			}
		} catch (Exception e) {
			logger.info("接口请求异常");
			e.printStackTrace();
		}
		logger.info("接口返回null");
		return null;
	}

	// 贵州省人民医院单点登录接口服务验证(md5加密)
	public static String MD5GuiZou(Map<String, String> map)
			throws NoSuchAlgorithmException, UnsupportedEncodingException {

		// base64加密
		java.util.Base64.Encoder encoder = java.util.Base64.getEncoder();
		byte[] textByte = new byte[0];
		textByte = map.get("AppName").getBytes("UTF-8");
		String encodedText = encoder.encodeToString(textByte);
		logger.info("base64加密 ==>" + encodedText);

		// md5加密
		String result = "";
		StringBuffer buf = new StringBuffer("");
		Long time = System.currentTimeMillis() / 1000 + 28800;
		String sign = encodedText + "|" + map.get("AppCode") + "|" + time + "|" + map.get("token");
		logger.info("sign==>" + sign);
		MessageDigest md5 = MessageDigest.getInstance("MD5");
		md5.update((sign).getBytes("UTF-8"));
		byte b[] = md5.digest();
		int i;
		for (int offset = 0; offset < b.length; offset++) {
			i = b[offset];
			if (i < 0) {
				i += 256;
			}
			if (i < 16) {
				buf.append("0");
			}
			buf.append(Integer.toHexString(i));
		}

		result = buf.toString();
		logger.info("md5加密 ==>" + result);
		String authorization = "Basic " + encodedText + "|" + time + "|" + result;
		return authorization;
	}

	/**
	 * 贵州省人民医院单点登录
	 */
	@Bizlet
	public static String guizouLogin(String code) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		JSONObject json = null;
		try {
			logger.info("单点登录code ==>" + code);
			if (code == null) {
				logger.info("请求参数为空==>");
				return null;
			}
			// 接口服务验证方式header
			Map<String, String> md5Map = new HashMap<>();
			md5Map.put("AppName", "SPD");
			md5Map.put("AppCode", "SPD");
			md5Map.put("token", code);
			String authorization = MD5GuiZou(md5Map);
			logger.info("authorization" + authorization);
			// 用java JDK自带的URL去请求
			URL restUrl = new URL("http://10.207.45.213:9011/sso/api/SSOService/GetUser?token=" + code);
			HttpURLConnection restConnection = (HttpURLConnection) restUrl.openConnection();
			restConnection.setRequestMethod("GET");
			restConnection.setRequestProperty("Accept", "*/*");
			restConnection.setRequestProperty("authorization", authorization);
			logger.info("准备连接");
			restConnection.connect();
			logger.info("连接成功 ");
			InputStream is = restConnection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
			}
			reader.close();
			json = JSONObject.parseObject(sbf.toString());
			logger.info("单点登录验证返回信息 ==>" + json.toJSONString());
			if (json.getString("message") == null) {
				String username = json.getString("Identity");
				logger.info("单点登录登录用户名 ==>" + username);
				return username;
			}
		} catch (Exception e) {
			logger.info("接口请求异常");
			e.printStackTrace();
		}
		logger.info("获取登录信息失败==>" + json.getString("message"));
		return null;
	}

	/**
	 * 中江县人民医院
	 */
	@Bizlet
	public static String ZhongJiangRMSSOLogin(String code) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		JSONObject json = null;
		try {

			// 用java JDK自带的URL去请求
			URL url = new URL("http://168.1.200.131:8810/logincenter/oauth?token=" + code);
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("GET");
			connection.setRequestProperty("Accept", "*/*");
			String authCode = java.util.Base64.getEncoder()
					.encodeToString(("42097961984" + ":" + SSOUtil.encode("aAr9MVS9j1")).getBytes("UTF-8"));
			connection.setRequestProperty("Authorization", authCode);
			connection.connect();
			InputStream is = connection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
			}
			reader.close();
			json = JSONObject.parseObject(sbf.toString());
			logger.info("单点登录验证返回信息 ==>" + json.toJSONString());
			if ("0".equals(json.getString("code"))) {
				JSONObject info = (JSONObject) json.get("info");
				logger.info("单点登录登录用户名为 ==>" + json.toJSONString());
				return info.getString("userName");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/*
	 * 贵州人民
	 */
	@Bizlet
	public static String createGZRMXMLParam(String ticket, String url) {
		DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyyMMdd HHmmss");
		String format = dtf2.format(LocalDateTime.now()).replace(" ", "").toString();
		String xmlParam = "<Request><MessageHeader><Sender>HIS</Sender><Receiver>UUM</Receiver><SendTime>" + format
				+ "</SendTime><EventType>SSO_VERIFYST</EventType><MsgId>" + UUID.randomUUID()
				+ "</MsgId></MessageHeader><MessageBody><ST>" + ticket + "</ST><Url>" + url
				+ "</Url></MessageBody></Request>";
		return xmlParam;
	}

	@Bizlet
	public static String parseGZRMXMLResult(String result) {
		logger.info("单点登录接口返回信息：" + result);
		Document doc = null;
		try {
			doc = DocumentHelper.parseText(result);
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		// 获取根节点元素对象
		Element root = doc.getRootElement();
		logger.info("当前节点的名称：" + root.getName());

		Element messageBody = root.element("MessageBody");// 获取子节点
		String data = messageBody.element("Result").element("Code").getText();
		if (data.equals("CA")) {
			return messageBody.elementText("UserId");
		} else {
			return null;
		}
	}

	/*
	 * 泰康同济
	 */
	@Bizlet
	public static String createTKTJXMLParam(String token) {
		String xmlParam = "<REQUEST><SESSION_ID>" + token + "</SESSION_ID><SYSTEM_CODE>HC</SYSTEM_CODE></REQUEST>";
		return xmlParam;
	}

	@Bizlet
	public static String parseTKTJXMLResult(String result) {
		logger.info("单点登录接口返回信息：" + result);
		Document doc = null;
		try {
			doc = DocumentHelper.parseText(result);
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		// 获取根节点元素对象
		Element root = doc.getRootElement();
		logger.info("当前节点的名称：" + root.getName());
		String resultCode = root.element("RESULT_CODE").getText();
		if (resultCode.equals("true")) {
			String ptcode = root.element("RESULT_INFO").element("USER_CODE").getText();
			logger.info("单点登录接口返回的ptcode：" + ptcode);
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("ptcode", ptcode);
			Object[] objects = DatabaseExt.queryByNamedSql("default",
					"org.gocom.components.coframe.auth.ssoLogin.tktjssologin", map);
			DataObject object = (DataObject) objects[0];
			String usercode = object.getString("usercode");
			logger.info("单点登录接口返回的ptcode对应的usercode：" + usercode);
			return usercode;
		} else {
			return null;
		}
	}

	/*
	 * 广东省南方医科大学第七附属医院
	 */
	@Bizlet
	public static String GDNFYKDXFSSSOLogin(String params) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		String result = null;
		try {
			logger.info("单点登录params ==>" + params);
			// 用java JDK自带的URL去请求
			// 测试环境
			// URL restUrl = new URL("http://196.4.22.113:8000/SSO");
			// 生产环境 http://196.4.22.158:8000/SSO
			URL restUrl = new URL("http://196.4.22.158:8000/SSO");
			HttpURLConnection restConnection = (HttpURLConnection) restUrl.openConnection();
			restConnection.setRequestMethod("POST");
			restConnection.setRequestProperty("Content-Type", "application/xml");
			restConnection.setRequestProperty("Charset", "UTF-8");
			// 设置超时时间
			restConnection.setConnectTimeout(10 * 1000);
			restConnection.setReadTimeout(30 * 1000);
			restConnection.setDoOutput(true);
			restConnection.setDoInput(true);// 设置是否从HttpURLConnection输入，默认值为 true
			restConnection.connect();

			String xmlParams = createGDNFYKDXFParam(params);
			logger.info("xmlParams ==> " + xmlParams);
			OutputStream os = restConnection.getOutputStream();
			byte[] input = xmlParams.getBytes("utf-8");
			os.write(input, 0, input.length);

			int responseCode = restConnection.getResponseCode();

			if (responseCode == HttpURLConnection.HTTP_OK) {
				InputStream is = restConnection.getInputStream();
				reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
				String strRead = null;
				while ((strRead = reader.readLine()) != null) {
					sbf.append(strRead);
				}
				reader.close();
				result = parseGDNFYKDXFResult(sbf.toString());
			}
		} catch (Exception e) {
			logger.info("接口请求异常");
			e.printStackTrace();
		}
		logger.info("接口返回 ==> " + result);
		return result;
	}

	@Bizlet
	public static String createGDNFYKDXFParam(String params) {
		String xmlParam = "<DocumentElement><AccessKey>97609a31-fadb-fbaa-9efc-3ffc7861129a</AccessKey><MethodName>EAI_Decrypt</MethodName><DataTable><CipherText>"
				+ params + "</CipherText></DataTable></DocumentElement>";
		return xmlParam;
	}

	@Bizlet
	public static String parseGDNFYKDXFResult(String result) throws Exception {
		logger.info("解密接口返回信息：" + result);
		Document doc = null;
		try {
			doc = DocumentHelper.parseText(result);
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		// 获取根节点元素对象
		Element root = doc.getRootElement();
		logger.info("当前节点的名称：" + root.getName());
		String resultCode = root.element("ResultCode").getText();
		if ("0".equals(resultCode)) {
			String clearText = root.element("DataTable").element("ClearText").getText();
			logger.info("解密接口返回的明文：" + clearText);
			return clearText.split("\\|")[1];
		} else {
			throw new Exception("解密失败");
		}
	}

	@Bizlet
	public static String WeiNingSSOLogin(String ticket) {
		String username = null;
		JSONObject params = new JSONObject();
		params.put("ticket", ticket);
		String baseUrl = SysConfig.queryConfigValue("SSOBASEURL");
		JSONObject result = SSOUtil.doPost(baseUrl + "/casServer/appservice/validateService", params);
		if ("200".equals(result.getString("code"))) {
			username = result.getString("username");
		}
		return username;
	}

	@Bizlet
	public static String parseToken(String token) throws UnsupportedEncodingException {
		byte[] decodeBase64 = Base64.decodeBase64(token.getBytes("UTF-8"));
		String decodedString = new String(decodeBase64);

		// 查找第一个 "hzkj123456" 的位置
		int index = decodedString.indexOf("hzkj123456");
		if (index == -1) {
			// 如果没有找到 "hzkj123456"，返回空字符串或抛出异常
			return "";
		}

		// 提取 "hzkj123456" 前面的用户名称
		String userName = decodedString.substring(0, index);

		return userName;
	}

	/*
	 * 广州惠侨计算机科技有限公司
	 */
	@Bizlet
	public static String GZHQSSOLogin(String url, String params, String accessKey) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		String result = null;
		try {
			logger.info("单点登录params ==>" + params);
			URL restUrl = new URL(url);
			HttpURLConnection restConnection = (HttpURLConnection) restUrl.openConnection();
			restConnection.setRequestMethod("POST");
			restConnection.setRequestProperty("Content-Type", "application/xml");
			restConnection.setRequestProperty("Charset", "UTF-8");
			// 设置超时时间
			restConnection.setConnectTimeout(10 * 1000);
			restConnection.setReadTimeout(30 * 1000);
			restConnection.setDoOutput(true);
			restConnection.setDoInput(true);// 设置是否从HttpURLConnection输入，默认值为 true
			restConnection.connect();

			String xmlParams = createGZHQParam(params, accessKey);
			logger.info("xmlParams ==> " + xmlParams);
			OutputStream os = restConnection.getOutputStream();
			byte[] input = xmlParams.getBytes("utf-8");
			os.write(input, 0, input.length);

			int responseCode = restConnection.getResponseCode();

			if (responseCode == HttpURLConnection.HTTP_OK) {
				InputStream is = restConnection.getInputStream();
				reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
				String strRead = null;
				while ((strRead = reader.readLine()) != null) {
					sbf.append(strRead);
				}
				reader.close();
				result = parseGZHQResult(sbf.toString());
			}
		} catch (Exception e) {
			logger.info("接口请求异常");
			e.printStackTrace();
		}
		logger.info("接口返回 ==> " + result);
		return result;
	}

	@Bizlet
	public static String createGZHQParam(String params, String accessKey) {
		String xmlParam = "<DocumentElement><AccessKey>" + accessKey
				+ "</AccessKey><MethodName>EAI_Decrypt</MethodName><DataTable><CipherText>" + params
				+ "</CipherText></DataTable></DocumentElement>";
		return xmlParam;
	}

	@Bizlet
	public static String parseGZHQResult(String result) throws Exception {
		logger.info("解密接口返回信息：" + result);
		Document doc = null;
		try {
			doc = DocumentHelper.parseText(result);
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		// 获取根节点元素对象
		Element root = doc.getRootElement();
		logger.info("当前节点的名称：" + root.getName());
		String resultCode = root.element("ResultCode").getText();
		if ("0".equals(resultCode)) {
			String clearText = root.element("DataTable").element("ClearText").getText();
			logger.info("解密接口返回的明文：" + clearText);
			return clearText.split("\\|")[1];
		} else {
			throw new Exception("解密失败");
		}
	}

	/**
	 * 浙江和仁科技
	 * 
	 * @param token
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@Bizlet
	public static String ZJHRKJSSOLogin(String token) throws UnsupportedEncodingException {
		String username = null;
		JSONObject params = new JSONObject();
		params.put("token", URLEncoder.encode(token, StandardCharsets.UTF_8.toString()));
		params.put("publicKey",
				"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5j7BJVLxtGTVETJ0P20LC53SxqldpE2cu2OSaB3CJ/Viehf3RimlsWzEUhnXj/hoda/erbp4a4hlxcBRRBK4Q5xWimozaFbwzwe5Bo2exTOMbAByQfJodkC5m1bFL0wltCFu97Q1ddPK1e6SzuisxswDpsbQMsar9x3Q2xTGP6QIDAQAB");
		String baseUrl = SysConfig.queryConfigValue("SSOBASEURL");
		JSONObject result = SSOUtil.doPost(baseUrl + "/cdruser/auth/check/token", params);
		if ("200".equals(result.getString("code"))) {
			username = result.getJSONObject("data").getString("username");
		}
		return username;
	}

	/**
	 * 章丘区中医医院耗材
	 * 
	 * @param params
	 * @param accessKey
	 * @return
	 */
	@Bizlet
	public static String createZQZYHCParam(String token) {
		String xmlParam = "<REQUEST><SESSION_ID>" + token + "</SESSION_ID><SYSTEM_CODE>SPD</SYSTEM_CODE>"
				+ "</REQUEST>";
		return xmlParam;
	}

	@Bizlet
	public static String parseZQZYHCResult(String result) throws Exception {
		logger.info("单点登录接口返回信息：" + result);
		Document doc = null;
		try {
			doc = DocumentHelper.parseText(result);
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		// 获取根节点元素对象
		Element root = doc.getRootElement();
		logger.info("当前节点的名称：" + root.getName());
		String resultCode = root.element("RESULT_CODE").getText();
		if ("true".equals(resultCode)) {
			String userName = root.element("RESULT_INFO").element("USER_LOGIN_NAME").getText();
			logger.info("单点登录接口返回的用户名：" + userName);
			return userName;
		} else {
			throw new Exception("单点登录接口返回失败：" + root.element("RESULT_CONTENT").getText());
		}
	}

	/**
	 * 瑞金医院IVD获取Token
	 * 
	 * @param code
	 * @param state
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@Bizlet
	public static String RJYYSSOGetToken(String code, String state) throws UnsupportedEncodingException {
		String token = null;
		String baseUrl = SysConfig.queryConfigValue("SSOBASEURL");
		JSONObject params = new JSONObject();
		params.put("code", code);
		params.put("state", state);
		JSONObject result = SSOUtil.doPost(baseUrl + "/api/OpenApi/GetTokenByOAuthCode", params, null);
		logger.info("单点登录接口返回的信息：" + result.toJSONString());
		if (result.getString("token") != null) {
			token = result.getString("token");
		}
		logger.info("单点登录接口返回的Token：" + token);
		return token;
	}

	/**
	 * 瑞金医院IVD获取Token
	 * 
	 * @param code
	 * @param state
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@Bizlet
	public static String RJYYSSOGetUserName(String token) throws UnsupportedEncodingException {
		String userName = null;
		String baseUrl = SysConfig.queryConfigValue("SSOBASEURL");
		JSONObject params = new JSONObject();
		params.put("token", token);
		JSONObject result = SSOUtil.doPost(baseUrl + "/api/OpenApi/GetUser", params, null);
		logger.info("单点登录接口返回的信息：" + result.toJSONString());
		if (result.getString("userCode") != null) {
			userName = result.getString("userCode");
		}
		logger.info("单点登录接口返回的userName：" + userName);
		return userName;
	}

	/**
	 * 西北妇幼获取登录信息
	 */
	@Bizlet
	public static String XBFYSSOLogin(String ticket) {
		try {
			String baseUrl = SysConfig.queryConfigValue("SSOBASEURL");
			// 用java JDK自带的URL去请求
			URL url = new URL(baseUrl + "/seeyon/thirdpartyController.do?ticket=" + ticket);
			// 回调A8中获得A8账户（账户双方协定为员工编号）
			URLConnection uc = url.openConnection();
			// 通过获取http header中的获取员工编号信息
			return uc.getHeaderField("loginName");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void main(String[] args) throws Exception {
		System.out.println(URLEncoder.encode(
				"Pc6gWf7Y3Gsn0dTtc1ALNlF0MdO2wjJHLIJC0iS/qvPGKmVzwHpQPHCmGIOrAMS66xGPJtPvXShW3x6r0FHaSfZ1GOgsZ5d8h3hkjq3S8KQLkXKC+kH933NmKpBPp80njynB7Ubp96sz9jfKeoxnhCC/ZQCsnf99BChQmCsgF9k=",
				StandardCharsets.UTF_8.toString()));
	}
}

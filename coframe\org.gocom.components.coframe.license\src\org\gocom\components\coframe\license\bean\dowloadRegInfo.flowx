<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="dowloadRegInfo.flowx" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******" state="stateless">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link2" name="link2" lineType="reference" isDefault="true" type="action" actionName="action0" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
      <process:validateRules onError="default"/>
      <process:inputParameters>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="license" primitiveType="String"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="102" y="181"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" type="end" contextPath="" method="forward" uri="">
    <targetConnections>link4</targetConnections>
    <location x="431" y="181"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="104" y="217"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="433" y="217"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="获取注册信息" displayName="getRequestCode" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>view0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="225" y="181"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.license.component.getRequestCode</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="license" type="query" value="String" valueType="Primitive" pattern="reference">license</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="filePath" type="query" value="String" valueType="Primitive">filePath</process:outputVariable>
        <process:outputVariable id="1" name="fileName" type="query" value="String" valueType="Primitive">fileName</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="202" y="216"/>
    <figSize height="17" width="73"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tView" description="" id="view0" name="视图" displayName="downloadService.jsp" type="view" contextPath="" method="forward" uri="/license/downloadService.jsp">
    <sourceConnections xsi:type="process:tActionLink" id="link4" name="link4" lineType="reference" isDefault="true" type="action" actionName="action1">
      <sourceNode>view0</sourceNode>
      <targetNode>end0</targetNode>
      <process:validateRules/>
      <process:inputParameters/>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="338" y="181"/>
    <size height="28" width="22"/>
    <nodeLabel>view0label</nodeLabel>
    <process:inputParameters>
      <process:parameter description="" historyStateLocation="client" isArray="false" name="fileName" primitiveType="String"/>
      <process:parameter description="" historyStateLocation="client" isArray="false" name="filePath" primitiveType="String"/>
    </process:inputParameters>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="view0label" name="label" nodeType="label">
    <location x="337" y="217"/>
    <figSize height="17" width="25"/>
    <node>view0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="ouwen" createTime="2022-01-27 11:17:26" date="2022-01-27Z" description="" name="dowloadRegInfo" version="*******"/>
  <process:variables/>
</process:tPageFlow>

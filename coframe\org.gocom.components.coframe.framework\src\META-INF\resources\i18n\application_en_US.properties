#Generated by ResourceBundle Editor (http://eclipse-rbe.sourceforge.net)
#\u00E8\u00AF\u00B7\u00E9\u0080\u0089\u00E6\u008B\u00A9\u00E4\u00B8\u0080\u00E8\u00A1\u008C\u00E8\u00AE\u00B0\u00E5\u00BD\u0095\u00EF\u00BC\u0081

#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E4\u00BB\u00A3\u00E7\u00A0\u0081
applicationManager_AcApplication.appcode  = appCode
#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E6\u008F\u008F\u00E8\u00BF\u00B0
applicationManager_AcApplication.appdesc  = appDesc
#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E5\u0090\u008D\u00E7\u00A7\u00B0
applicationManager_AcApplication.appname  = appName
#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E7\u00B1\u00BB\u00E5\u0088\u00AB
applicationManager_AcApplication.apptype  = appType
#IP
applicationManager_AcApplication.ipaddr   = ipAddr
#\u00E7\u00AB\u00AF\u00E5\u008F\u00A3
applicationManager_AcApplication.ipport   = port
#\u00E6\u0098\u00AF\u00E5\u0090\u00A6\u00E5\u00BC\u0080\u00E9\u0080\u009A
applicationManager_AcApplication.isopen   = isOpen
#\u00E5\u00BC\u0080\u00E9\u0080\u009A\u00E6\u0097\u00A5\u00E6\u009C\u009F
applicationManager_AcApplication.opendate = openDate
#\u00E8\u00AE\u00BF\u00E9\u0097\u00AE\u00E5\u009C\u00B0\u00E5\u009D\u0080
applicationManager_AcApplication.url      = contextPath

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00E5\u0090\u008D\u00E7\u00A7\u00B0
applicationManager_AcFuncGroup.funcgroupname = funcgroupname
#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00E5\u00BA\u008F\u00E5\u008F\u00B7
applicationManager_AcFuncGroup.funcgroupseq  = funcgroupSeq
#\u00E8\u008A\u0082\u00E7\u0082\u00B9\u00E5\u00B1\u0082\u00E6\u00AC\u00A1
applicationManager_AcFuncGroup.grouplevel    = groupLevel
#\u00E6\u0098\u00AF\u00E5\u0090\u00A6\u00E5\u008F\u00B6\u00E5\u00AD\u0090\u00E8\u008A\u0082\u00E7\u0082\u00B9
applicationManager_AcFuncGroup.isleaf        = isleaf

#\u00E6\u009E\u0084\u00E4\u00BB\u00B6\u00E5\u008C\u0085\u00E5\u0090\u008D=
applicationManager_AcFuncresource.compackname  = componentName
#\u00E6\u0089\u0080\u00E5\u00B1\u009E\u00E6\u009E\u0084\u00E4\u00BB\u00B6\u00E5\u008C
applicationManager_AcFuncresource.compackname2 = belong
#\u00E6\u0098\u00BE\u00E7\u00A4\u00BA\u00E5\u0090\u008D\u00E7\u00A7\u00B0
applicationManager_AcFuncresource.resname      = resrcName
#\u00E8\u00B5\u0084\u00E6\u00BA\u0090\u00E8\u00B7\u00AF\u00E5\u00BE\u0084
applicationManager_AcFuncresource.respath      = resrcPath
#\u00E8\u00B5\u0084\u00E6\u00BA\u0090\u00E7\u00B1\u00BB\u00E5\u009E\u008B
applicationManager_AcFuncresource.restype      = resrcType

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E8\u00B0\u0083\u00E7\u0094\u00A8\u00E5\u0085\u00A5\u00E5\u008F\u00A3=
applicationManager_AcFunction.funcaction      = funcUrl
#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BC\u0096\u00E7\u00A0\u0081
applicationManager_AcFunction.funccode        = funcCode
#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E6\u008F\u008F\u00E8\u00BF\u00B0
applicationManager_AcFunction.funcdesc        = funcDesc
#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E5\u0090\u008D\u00E7\u00A7\u00B0
applicationManager_AcFunction.funcname        = funcName
#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00B1\u00BB\u00E5\u009E\u008B
applicationManager_AcFunction.functype        = funcType
#\u00E6\u0098\u00AF\u00E5\u0090\u00A6\u00E5\u00AE\u009A\u00E4\u00B9\u0089\u00E4\u00B8\u00BA\u00E8\u008F\u009C\u00E5\u008D\u0095
applicationManager_AcFunction.ismenu          = isMenu
#\u00E8\u00BE\u0093\u00E5\u0085\u00A5\u00E5\u008F\u0082\u00E6\u0095\u00B0=
applicationManager_AcFunction.parainfo        = paramInfo
#\u00E6\u0089\u0080\u00E5\u00B1\u009E\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084
applicationManager_AcFunction.parentFuncGroup = parent funGroup

#\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E5\u00BA\u0094\u00E7\u0094\u00A8
applicationManager_l_appDelete = delete app

#\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E5\u00BA\u0094\u00E7\u0094\u00A8
applicationManager_l_applicationAdd = add app

#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E4\u00BF\u00A1\u00E6\u0081\u00AF
applicationManager_l_applicationInfo = app info

#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E5\u0088\u0097\u00E8\u00A1\u00A8
applicationManager_l_applicationList = app list

#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E6\u009F\u00A5\u00E8\u00AF\u00A2
applicationManager_l_applicationQuery = app query

#\u4FEE\u6539\u5E94\u7528
applicationManager_l_applicationUpdate = update app

#\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084
applicationManager_l_funcGroupAdd = add funcGroup

#\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084
applicationManager_l_funcGroupDelete = delete funcGroup

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00E4\u00BF\u00A1\u00E6\u0081\u00AF
applicationManager_l_funcGroupInfo = funcGroup info

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00E5\u0088\u0097\u00E8\u00A1\u00A8
applicationManager_l_funcGroupList = funcGroup list

#\u00E4\u00BF\u00AE\u00E6\u0094\u00B9\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084
applicationManager_l_funcGroupUpdate = update funcGroup

#\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E8\u00B5\u0084\u00E6\u00BA\u0090
applicationManager_l_funcResourceAdd = add funcResource

#\u00E8\u00B5\u0084\u00E6\u00BA\u0090\u00E5\u0088\u0097\u00E8\u00A1\u00A8
applicationManager_l_funcResourceList = funcResource list

#\u00E4\u00BF\u00AE\u00E6\u0094\u00B9\u00E8\u00B5\u0084\u00E6\u00BA\u0090
applicationManager_l_funcResourceUpdate = update funcResource

#\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E5\u008A\u009F\u00E8\u0083\u00BD
applicationManager_l_functionAdd = add function

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E4\u00BF\u00A1\u00E6\u0081\u00AF
applicationManager_l_functionInfo = function info

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E5\u0088\u0097\u00E8\u00A1\u00A8
applicationManager_l_functionList = function list

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E6\u009F\u00A5\u00E8\u00AF\u00A2
applicationManager_l_functionQuery = function query

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E6\u009F\u00A5\u00E8\u00AF\u00A2\u00E7\u00BB\u0093\u00E6\u009E\u009C
applicationManager_l_functionQueryResult = function list

#\u00E4\u00BF\u00AE\u00E6\u0094\u00B9\u00E5\u008A\u009F\u00E8\u0083\u00BD
applicationManager_l_functionUpdate = update function

#\u00E5\u0088\u00B7\u00E6\u0096\u00B0
applicationManager_l_refresh = refresh

#\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E5\u00AD\u0090\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084
applicationManager_l_subFuncGroupAdd = add subFuncGroup

#\u00E5\u00AD\u0090\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00E5\u0088\u0097\u00E8\u00A1\u00A8
applicationManager_l_subFuncGroup_list = subFuncGroup list

#\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00AE\u00A1\u00E7\u0090\u0086
applicationManager_l_title_appFunc_manager = App&Func Manange

#\u00E5\u00A4\u0084\u00E7\u0090\u0086\u00E5\u00A4\u00B1\u00E8\u00B4\u00A5!
applicationManager_m_addFuncGroup_failure = insert funcGroup failed!

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E6\u0088\u0090\u00E5\u008A\u009F\u00EF\u00BC\u0081
applicationManager_m_addFuncGroup_success = insert funcGroup success!

#\u00E5\u0090\u008C\u00E5\u0090\u008D\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E4\u00BB\u00A3\u00E7\u00A0\u0081\u00E5\u00B7\u00B2\u00E5\u00AD\u0098\u00E5\u009C\u00A8!
applicationManager_m_addUpdateApp_isValid = occupied appcode!

#\u00E5\u0090\u008C\u00E5\u0090\u008D\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BC\u0096\u00E7\u00A0\u0081\u00E5\u00B7\u00B2\u00E5\u00AD\u0098\u00E5\u009C\u00A8\u00EF\u00BC\u0081
applicationManager_m_addUpdateFunc_isValid = occupied funcCode!

#\u00E5\u00A4\u0084\u00E7\u0090\u0086\u00E5\u00A4\u00B1\u00E8\u00B4\u00A5
applicationManager_m_addfuncResource_failure = funcResource add(update) failure !

#\u00E8\u00B5\u0084\u00E6\u00BA\u0090\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E6\u0088\u0090\u00E5\u008A\u009F\u00EF\u00BC\u0081
applicationManager_m_addfuncResource_success = funcResource insert success !

#\u00E5\u00A4\u0084\u00E7\u0090\u0086\u00E5\u00A4\u00B1\u00E8\u00B4\u00A5!
applicationManager_m_addfunc_failure = function insert failure !

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E6\u0096\u00B0\u00E5\u00A2\u009E\u00E6\u0088\u0090\u00E5\u008A\u009F\u00EF\u00BC\u0081
applicationManager_m_addfunc_success = function insert success !

#\u00E5\u00A4\u0084\u00E7\u0090\u0086\u00E5\u00A4\u00B1\u00E8\u00B4\u00A5
applicationManager_m_addsubFuncGroup_failure = funcGroup update failure !

#\u00E6\u0082\u00A8\u00E7\u00A1\u00AE\u00E8\u00AE\u00A4\u00E8\u00A6\u0081\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E9\u0080\u0089\u00E4\u00B8\u00AD\u00E7\u009A\u0084\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00EF\u00BC\u009F\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E5\u00B0\u0086\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E8\u00AF\u00A5\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00E4\u00B8\u008B\u00E7\u009A\u0084\u00E6\u0089\u0080\u00E6\u009C\u0089\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00EF\u00BC\u0081
applicationManager_m_appdelete_confirm = Are you sure to delete the selected app(s)? Its funcGroup(s) will also be deleted!

#\u00E6\u0082\u00A8\u00E7\u00A1\u00AE\u00E8\u00AE\u00A4\u00E8\u00A6\u0081\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E9\u0080\u0089\u00E4\u00B8\u00AD\u00E7\u009A\u0084\u00E5\u00BA\u0094\u00E7\u0094\u00A8\u00EF\u00BC\u009F
applicationManager_m_applicationdelete_confirm = Are you sure to delete the selected app(s)?

#\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E5\u00A4\u00B1\u00E8\u00B4\u00A5\u00EF\u00BC\u0081
applicationManager_m_delete_failure = funcGroup deletion failure!

#\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E6\u0088\u0090\u00E5\u008A\u009F\u00EF\u00BC\u0081
applicationManager_m_delete_success = funcGroup deletion success !

#\u00E6\u0082\u00A8\u00E7\u00A1\u00AE\u00E8\u00AE\u00A4\u00E8\u00A6\u0081\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E9\u0080\u0089\u00E4\u00B8\u00AD\u00E7\u009A\u0084\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00EF\u00BC\u009F
applicationManager_m_funcGroupdelete_confirm = Are you sure to delete the selected funcGroup(s)?

#\u00E6\u0082\u00A8\u00E7\u00A1\u00AE\u00E8\u00AE\u00A4\u00E8\u00A6\u0081\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E9\u0080\u0089\u00E4\u00B8\u00AD\u00E7\u009A\u0084\u00E8\u00B5\u0084\u00E6\u00BA\u0090\u00EF\u00BC\u009F
applicationManager_m_funcResourcedelete_confirm = Are you sure to delete the selected funcResource(s)?

applicationManager_m_functdelete_confirm = confirm to delete function group and all sub ojects?

#\u00E6\u0082\u00A8\u00E7\u00A1\u00AE\u00E8\u00AE\u00A4\u00E8\u00A6\u0081\u00E5\u0088\u00A0\u00E9\u0099\u00A4\u00E9\u0080\u0089\u00E4\u00B8\u00AD\u00E7\u009A\u0084\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00EF\u00BC\u009F
applicationManager_m_functiondelete_confirm = Are you sure to delete the selected function(s)?

#\u00E5\u00A4\u0084\u00E7\u0090\u0086\u00E5\u00A4\u00B1\u00E8\u00B4\u00A5
applicationManager_m_updatefuncGroup_failure = funcGroup update failure !

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E7\u00BB\u0084\u00E4\u00BF\u00AE\u00E6\u0094\u00B9\u00E6\u0088\u0090\u00E5\u008A\u009F\u00EF\u00BC\u0081
applicationManager_m_updatefuncGroup_success = funcGroup update success !

#\u00E8\u00B5\u0084\u00E6\u00BA\u0090\u00E4\u00BF\u00AE\u00E6\u0094\u00B9\u00E6\u0088\u0090\u00E5\u008A\u009F\u00EF\u00BC\u0081
applicationManager_m_updatefuncResource_success = funcResource update success !

#\u00E5\u00A4\u0084\u00E7\u0090\u0086\u00E5\u00A4\u00B1\u00E8\u00B4\u00A5
applicationManager_m_updatefunc_failure = function update failure !

#\u00E5\u008A\u009F\u00E8\u0083\u00BD\u00E4\u00BF\u00AE\u00E6\u0094\u00B9\u00E6\u0088\u0090\u00E5\u008A\u009F\u00EF\u00BC\u0081
applicationManager_m_updatefunc_success = function update success !

contributionMetadata.contributions = contributions
contributionMetadata.displayName   = display name
contributionMetadata.name          = name

#\u00E8\u00AF\u00B7\u00E9\u0080\u0089\u00E6\u008B\u00A9\u00E8\u00AE\u00B0\u00E5\u00BD\u0095\u00EF\u00BC\u0081
m_delete_illegalSelect = Please select the record(s) to delete!

m_illegalIP = please input valid ip address\uFF01

m_illegalPort = please input valid port no( 0 - 65535 )

m_update_illegalSelect = please select one record to update!

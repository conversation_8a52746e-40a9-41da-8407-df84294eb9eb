<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="updateFuncGroupRelation.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="应用功能树拖拽" title="Administrator&#x9;13-3-13 下午8:31">
    <location x="60" y="465"/>
    <size height="100" width="196"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" description="" id="link0" name="link0" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJNE">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">function</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link4</targetConnections>
    <targetConnections>link3</targetConnections>
    <location x="675" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Boolean" name="data" type="query" valueType="Primitive">data</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="677" y="186"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="更新功能组关系" displayName="modifyFuncGroupRelation" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="403" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.modifyFuncGroupRelation</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="funcGroupId" type="query" value="java.lang.String" valueType="Java" pattern="reference">nodeId</process:inputVariable>
      <process:inputVariable id="1" name="targetGroupId" type="query" value="java.lang.String" valueType="Java" pattern="reference">targetNodeId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="375" y="186"/>
    <figSize height="12" width="85"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokeSpring2</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="100" y="261"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">nodeId</process:from>
      <process:to type="query">appfunction/funccode</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="102" y="297"/>
    <figSize height="12" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="更新function" displayName="updateAppFunction" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="677" y="261"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFunctionService.updateAppFunction</process:partner>
      <process:instance instanceName="AppFunctionBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="appFunction" type="query" value="org.gocom.components.coframe.framework.application.AppFunction" valueType="Java" pattern="reference">appfunction</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="655" y="297"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="根据funcid查询function" displayName="getAppFunction" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="210" y="261"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFunctionService.getAppFunction</process:partner>
      <process:instance instanceName="AppFunctionBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="appFunction" type="query" value="org.gocom.components.coframe.framework.application.AppFunction" valueType="Java" pattern="reference">appfunction</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="158" y="297"/>
    <figSize height="12" width="133"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值1" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>invokeSpring3</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="315" y="261"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">targetNodeId</process:from>
      <process:to type="query">appfuncgroup/funcgroupid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="314" y="297"/>
    <figSize height="12" width="31"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="根据funcgroupid查询funcgroup" displayName="getAppFuncgroup" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>assign2</targetNode>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="451" y="261"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.getAppFuncgroup</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="appFuncgroup" type="query" value="org.gocom.components.coframe.framework.application.AppFuncgroup" valueType="Java" pattern="reference">appfuncgroup</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="381" y="297"/>
    <figSize height="12" width="169"/>
    <node>invokeSpring3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值2" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokeSpring1</targetNode>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="588" y="261"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">appfuncgroup</process:from>
      <process:to type="query">appfunction/appFuncgroup</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="587" y="297"/>
    <figSize height="12" width="31"/>
    <node>assign2</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-13 20:31:09" date="2013-03-13Z" description="" name="updateFuncGroupRelation" version="6.3"/>
  <process:variables>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppFunction" description="" historyStateLocation="client" isArray="false" name="appfunction"/>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppFuncgroup" description="" historyStateLocation="client" isArray="false" name="appfuncgroup"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="nodeId" primitiveType="String"/>
    <process:input description="" isArray="false" name="nodeType" primitiveType="String"/>
    <process:input description="" isArray="false" name="targetNodeId" primitiveType="String"/>
    <process:input description="" isArray="false" name="targetNodeType" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="data" primitiveType="Boolean"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

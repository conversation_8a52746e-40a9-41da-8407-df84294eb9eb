package org.gocom.components.coframe.auth.login;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.cert.X509Certificate;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import com.alibaba.fastjson.JSONObject;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;

public class SSOOAuth2Service {

	private static Logger logger = TraceLoggerFactory.getLogger(SSOOAuth2Service.class);

	private static final String ssoServer = "https://172.16.6.79:8002";
	private static final String spdServer = "http://172.16.20.103:8085/vxspd";
	private static final String appId = "y4hvWlmS0584";
	private static final String appSecret = "j528620qNC4Ki7O7";

	// 忽略 SSL 证书验证
	static {
		try {
			TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
				public X509Certificate[] getAcceptedIssuers() {
					return null;
				}

				public void checkClientTrusted(X509Certificate[] certs, String authType) {
				}

				public void checkServerTrusted(X509Certificate[] certs, String authType) {
				}
			} };

			SSLContext sc = SSLContext.getInstance("TLS");
			sc.init(null, trustAllCerts, new java.security.SecureRandom());
			HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

			HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Bizlet
	public static String getToken(String code) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		JSONObject json = null;
		try {
			logger.info("单点登录code ==>" + code);

			// 使用 URL 去请求 token
			URL restUrl = new URL(ssoServer + "/api/oauth2/token");
			HttpURLConnection restConnection = (HttpURLConnection) restUrl.openConnection();
			restConnection.setRequestMethod("POST");

			// 设置请求参数
			StringBuilder params = new StringBuilder();
			params.append("client_id=").append(URLEncoder.encode(appId, "UTF-8"));
			params.append("&client_secret=").append(URLEncoder.encode(appSecret, "UTF-8"));
			params.append("&grant_type=authorization_code"); // 默认类型
			if (code != null && !code.isEmpty()) {
				params.append("&code=").append(URLEncoder.encode(code, "UTF-8"));
			}
			params.append("&redirect_uri=").append(
					URLEncoder.encode(spdServer + "/org.gocom.components.coframe.auth.login.SSOLogin.flow", "UTF-8"));

			// 设置请求头
			restConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			restConnection.setRequestProperty("Charset", "UTF-8");
			restConnection.setDoOutput(true); // 允许输出

			// 写入请求体
			try (OutputStream os = restConnection.getOutputStream()) {
				byte[] input = params.toString().getBytes("utf-8");
				os.write(input, 0, input.length);
			}

			// 设置超时时间
			restConnection.setConnectTimeout(10 * 1000);
			restConnection.setReadTimeout(30 * 1000);
			restConnection.connect();

			// 获取响应
			InputStream is = restConnection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
			}
			reader.close();

			// 解析返回的 JSON
			json = JSONObject.parseObject(sbf.toString());
			logger.info("单点登录 ===> getToken" + json.toJSONString());

			// 检查返回的代码是否为 200
			if ("200".equals(json.getString("code"))) {
				JSONObject data = json.getJSONObject("data");
				logger.info("单点登录登录 ===> getToken ==> data: " + data);
				return data.getString("access_token");
			}
		} catch (Exception e) {
			logger.error("接口请求异常：" + e.getMessage(), e);
		}
		return null;
	}

	@Bizlet
	public static String getUserInfo(String token) {
		BufferedReader reader = null;
		StringBuffer sbf = new StringBuffer();
		JSONObject json = null;
		try {
			logger.info("单点登录token ==>" + token);

			// 使用 URL 去请求用户信息
			URL restUrl = new URL(ssoServer + "/api/security/oauth2/user/info");
			HttpURLConnection restConnection = (HttpURLConnection) restUrl.openConnection();
			restConnection.setRequestMethod("POST");

			// 设置请求头
			restConnection.setRequestProperty("Authorization", "Bearer " + token);
			restConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			restConnection.setRequestProperty("Charset", "UTF-8");
			restConnection.setDoInput(true); // 允许读取输入流

			// 设置超时时间
			restConnection.setConnectTimeout(10 * 1000);
			restConnection.setReadTimeout(30 * 1000);
			restConnection.connect();

			// 获取响应
			InputStream is = restConnection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
			}
			reader.close();

			// 解析返回的 JSON
			json = JSONObject.parseObject(sbf.toString());
			logger.info("单点登录 ===> getUserInfo" + json.toJSONString());

			// 检查返回的代码是否为 200
			if ("200".equals(json.getString("code"))) {
				JSONObject data = json.getJSONObject("data");
				logger.info("单点登录登录 ===> getUserInfo ==> data: " + data);
				return data.getString("accountCode");
			}
		} catch (Exception e) {
			logger.error("接口请求异常：" + e.getMessage(), e);
		}
		return null;
	}
}
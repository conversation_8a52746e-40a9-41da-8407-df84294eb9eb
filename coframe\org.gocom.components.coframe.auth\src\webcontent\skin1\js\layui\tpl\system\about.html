<div class="layui-card-header">更新日志</div>
<ul class="layui-timeline" id="updatemsg" style="margin-top: 5px;">

</ul>
</br>
<div style="display:none;" class="layui-card-body layui-text layadmin-about" id="empty">
		您的系统未被授权，请提供MAC地址并联系  万序健康 021-55155381获取许可证书。
	</div>
<div class="layui-card-body layui-text layadmin-about" id="onempty">
	
    <p>当前版本：SCCP <span id="maxversion">2.0</span> </p>
    <p>基于框架：layui</p>	
		<p id="PROJECTNAME">
			12
		</p>
		<p   style="word-break:break-word;">
			授权码<span id="AUTHCODE"></span>
		</p>
		<p>
			授权时间 <span id="AUTHDATE"></span>
		</p>
		<p>
		到期时间 <span id="AUTHEXPDATE"></span>
		</p>
		<p>
			MAC地址 <span id="MACADDRS"></span>
		</p>

	</div>
<script>
  loadVersionInfo();
  //加载版本信息问题
  function loadVersionInfo() {
    var html="";
    var json = getDataWithDetail({},"com.wx.masterData.indexQueryNamingsql.select_oms_updatemsg_detail");
	$("#maxversion").html(json[0].maxversion);
    $.each(json,function (index,val) {
      html+='<li class="layui-timeline-item">'+
              '<i class="layui-icon layui-timeline-axis">&#xe63f;</i>'+
            '<div class="layui-timeline-content layui-text">'+
                    '<h3 class="layui-timeline-title">'+val.adddates+'</h3>'+
            '<p>'+val.updatemsg+'</p>'+
            '</div>'+
            '</li>';
    });
    $("#updatemsg").html(html);
	
	mini.ajax({
				url: "org.gocom.components.coframe.auth.LoginManager.queryAuthbiz.biz.ext",
				data: {},
				type: "post",
				cache: false,
				async: false,
				success: function (result) {
					var res = result.rs;
					if(res.length > 0 ){
						$('#PROJECTNAME').html(res[0].PROJECTNAME);
						//console.info('zhe'+$('#PROJECTNAME').html());
						$('#AUTHCODE').html('<span>'+res[0].AUTHCODE+'</span>');
						$('#AUTHDATE').html(res[0].AUTHDATE);
						$('#AUTHEXPDATE').html(res[0].AUTHEXPDATE);
						$('#MACADDRS').html(res[0].MACADDRS);
						//$('#empty').hide();
						//$('#empty').show();
						$('#onempty').show();
					}else{
						$('#empty').show();
						$('#onempty').hide();
					}
				
					
					console.log(result)
				}
			});
  }
</script>
<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getLoginUserInfo.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" title="gkm&#x9;15-9-2 下午1:33">
    <location x="72" y="300"/>
    <size height="100" width="142"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link2</targetConnections>
    <location x="525" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.auth.queryentity.QueryLoginUserInfo[]" name="LoginUserInfo" type="query" valueType="DataObject">LoginUserInfo</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="527" y="186"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="执行查询" displayName="queryEntitiesByCriteriaEntity" collapsed="false" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="375" y="150"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">query</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">LoginUserInfo</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="362" y="186"/>
    <figSize height="12" width="49"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="查询条件配置" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="225" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.auth.queryentity.QueryLoginUserInfo</process:from>
      <process:to type="query">query/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">=</process:from>
      <process:to type="query">query/_expr[1]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">loginname</process:from>
      <process:to type="query">query/_expr[1]/_property</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">userId</process:from>
      <process:to type="query">query/_expr[1]/_value</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="203" y="186"/>
    <figSize height="12" width="73"/>
    <node>assign1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="gkm" createTime="2015-09-02 13:33:06" date="2015-09-02Z" description="" name="getLoginUserInfo" version="*******"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="query"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="userId" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="true" modelType="org.gocom.components.coframe.auth.queryentity.QueryLoginUserInfo" name="LoginUserInfo"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

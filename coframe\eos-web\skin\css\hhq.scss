/*
* @Author: <PERSON>
* @Date:   2016-03-29 15:15:25
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-03-29 15:22:59
* @hhq.scss
*/

	@mixin faded($property:all,$time:0.4s) {
		transition:$property $time;
		-o-transition:$property $time;
		-moz-transition:$property $time;
		-webkit-transition:$property $time;
	}
	@mixin distuch($type:none) {
		-webkit-touch-callout: $type;
		-webkit-user-select: $type;
		-khtml-user-select: $type;
		-moz-user-select: $type;
		-ms-user-select: $type;
		user-select: $type;
	}
	@mixin xz($deg) {
		transform:rotate($deg);
		-ms-transform:rotate($deg);
		-moz-transform:rotate($deg);
		-webkit-transform:rotate($deg);
		-o-transform:rotate($deg);
	}
	@mixin blur($px) {
		-webkit-filter: blur($px);
		-moz-filter: blur($px);
		-ms-filter: blur($px);
		filter: blur($px);
	}
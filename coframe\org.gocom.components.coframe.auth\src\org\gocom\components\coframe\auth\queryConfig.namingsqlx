<?xml version="1.0" encoding="UTF-8"?>
<!-- author:Administrator -->
<sqlMap>
       <select id="queryConfig" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select * from TSYS_CONFIG where configcd = '$configname$'</select>
    <select id="queryDeptConfig" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select to_char(tc.DEPTCONFIGTYPE) as DEPTCONFIGTYPE,tc.CONFIGVALUE from tsys_config tc 
				 where tc.configcd = '$configname$'</select>
    <select id="queryDeptConfigdetial" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select tcd.configvalue
				  from tsys_config_dept tcd
				  left join tsys_config tc
				    on tcd.configid = tc.configid
				 where tc.configcd = '$configname$'
				   and tcd.deptid = '$deptid$'</select>
   
</sqlMap>
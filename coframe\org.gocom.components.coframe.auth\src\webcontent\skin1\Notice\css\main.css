@keyframes load {0% {width:0%;} 100% {width:100%;}}
.trent-slider {width:100%;position:relative;overflow:hidden;}
.t-slide {position:absolute;left:0;right:0;bottom:0;top:0;transition:.65s;}
.t-slide img {width:100%;height:100%;}
.t-slider-controls {position:absolute;top:0;bottom:0;left:0;right:0;} 
.t-slider-controls .arrow {min-height:100%;width:50px;position:relative;transition:.25s;}
.t-slider-controls .arrow:hover {cursor:pointer;}
.t-slider-controls .right-arrow {float:right;} .t-slider-controls .left-arrow {float:left;}
.t-slider-controls .arrow .arrow-container {position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:20px;}
.t-slider-controls .arrow .arrow-container .arrow-icon {position:relative;width:42px;height:42px;color:#fafafa;border-radius:50%;}
.t-slider-controls .arrow .arrow-container .arrow-icon i {position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);}
.t-slider-controls .t-load-bar {width:100%;height:5px;}
.t-slider-controls .t-load-bar .inner-load-bar {background:rgba(200,200,200,0.65);height:100%;}
.t-slider-controls .t-dots-container {position:absolute;bottom:0;left:50%;transform:translateX(-50%);height:20px;min-width:350px;}
.t-slider-controls .t-slide-dots-wrap {height:100%;width:100%;position:relative;}
.t-slider-controls .t-slide-dots {position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);display:table;content:"";clear:both;}
.t-slider-controls .t-slide-dots .t-dot {background:#fafafa;width:8px;height:8px;margin:5px;float:left;border-radius:50%;transition:.65s;} 
.t-slider-controls .t-slide-dots .t-dot.current-dot,.t-slider-controls .t-slide-dots .t-dot:hover {transform:scale(1.65);cursor:pointer;}
/*close slider css*/
@media screen and (min-width:768px) {.t-slider-controls .arrow:hover {background:rgba(0,0,0,0.32);}}
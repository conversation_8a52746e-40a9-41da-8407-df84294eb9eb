<%@page pageEncoding="UTF-8"%>
<%@page import="com.primeton.cap.AppUserManager"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge"><!-- 使IE浏览器使用 edge 内核渲染页面 -->
	<meta name="renderer" content="webkit"><!-- 使360等国产浏览器使用 webkit 内核渲染页面 -->
	<meta name="viewport" content="width=device-width, initial-scale=1"><!-- 响应式布局初始化 -->
	<!-- 以上标签必须在最顶端 -->
<html>
<head>
<title><%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%>-登录</title>
<%
   String contextPath = request.getContextPath();
%>

 <script>
	//直接访问login.jsp之后的跳转
	//location.href="<%=contextPath %>" + "/core/login.jsp";
</script>
<!-- <link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/skin1/skin/css/bootstrap/css/bootstrap.min.css" /> -->
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/top.css" />
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/style2.0.css" />
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/iconfont/iconfont.css" />
<script type="text/javascript">
    if(top!=window){
        top.location.href=top.location.href;
        top.location.reload;
    }
  
</script>
</head>
<%
	String original_url=null;
	Object objAttr=request.getAttribute("original_url");
	if(objAttr != null){
		original_url=(String)objAttr;
	}
 %>
 
<!DOCTYPE html>
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta charset="UTF-8">
<title>行业智慧能力平台</title>
<style type="text/css">
	ul li{font-size: 30px;color:#2ec0f6;}
	.tyg-div{z-index:-1000;float:left;position:absolute;left:5%;top:20%;}
	.tyg-p{
		font-size: 14px;
	    font-family: 'microsoft yahei';
	    position: absolute;
	    top: 135px;
	    left: 60px;
	}
	.tyg-div-denglv{
        position:absolute;
        width: 400px;
        text-align: center;
        right:10%;
        top:50%;
        z-index:1000;
        transform: translateY(-64%);
        -webkit-transform: translateY(-64%);
        -moz-transform:  translateY(-64%);
        -ms-transform:  translateY(-64%);
        -o-transform:  translateY(-64%);
	}
	.tyg-div-form{
        position: relative;
		background-color: #23305a;
		padding-bottom: 30px;
		color:#2ec0f6;
	}
	.tyg-div-form  .mini-textbox{
		width: 340px;
	    height: 42px;
	    margin: 25px 10px 0px 0px;
    }
	.tyg-div-form button {
	    cursor: pointer;
	    width: 340px;
	    height: 44px;
	    margin-top: 25px;
	    padding: 0;
	    background: #2ec0f6;
	    -moz-border-radius: 6px;
	    -webkit-border-radius: 6px;
	    border-radius: 6px;
	    border: 1px solid #2ec0f6;
	    -moz-box-shadow:
	        0 15px 30px 0 rgba(255,255,255,.25) inset,
	        0 2px 7px 0 rgba(0,0,0,.2);
	    -webkit-box-shadow:
	        0 15px 30px 0 rgba(255,255,255,.25) inset,
	        0 2px 7px 0 rgba(0,0,0,.2);
	    box-shadow:
	        0 15px 30px 0 rgba(255,255,255,.25) inset,
	        0 2px 7px 0 rgba(0,0,0,.2);
	    font-family: 'PT Sans', Helvetica, Arial, sans-serif;
	    font-size: 14px;
	    font-weight: 700;
	    color: #fff;
	    text-shadow: 0 1px 2px rgba(0,0,0,.1);
	    -o-transition: all .2s;
	    -moz-transition: all .2s;
	    -webkit-transition: all .2s;
	    -ms-transition: all .2s;
}
.foot{
    color:#fff;
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
}
.errorText{
    position: absolute;
    top: 152px;
    left: 26px;
}
.errobottom{
    position: absolute;
    bottom: 92px;
    left: 26px;
}
.title0{
    position: absolute;
    width: 600px;
    height: 83px;
    margin-left: -300px;
    left: 50%;
    background: url(<%=contextPath %>/coframe/auth/login/images/login_T_3.png) no-repeat;
    background-size: cover;
}
</style>
<body>
        <div id="switcher">
                <div class="center">
                    <ul>
                        <!-- <div id="Device">
                            <li class="device-monitor"><a href="javascript:"><div class="icon-monitor"></div></a></li>
                            <li class="device-mobile"><a href="javascript:"><div class="icon-tablet"></div></a></li>
                            <li class="device-mobile"><a href="javascript:"><div class="icon-mobile-1"></div></a></li>
                            <li class="device-mobile-2"><a href="javascript:"><div class="icon-mobile-2"></div></a></li>
                            <li class="device-mobile-3"><a href="javascript:"><div class="icon-mobile-3"></div></a></li>
                        </div> -->
                        <li class="top2">
                            <i class="iconfont icon-android icon-size-lg" style="color: #36ff20;"></i>
                            <div class="vm">
                                <div id="outputAndroid"></div>
                                <p style="color:#808080;margin:10px 0 0 0;">扫一扫，直接在手机上打开</p>
                            </div>
                        </li>
                        <li class="top3">
                            <i class="iconfont icon-ai-ios icon-size-lg"></i>
                            <div class="vm1">
                                <div id="outputIos"></div>
                                <p style="color:#808080;margin:10px 0 0 0;">扫一扫，直接在手机上打开</p>
                            </div>
                        </li>
                        <li class="remove_frame"></li>
                    </ul>
                </div>
            </div>
<!-- <div class="tyg-div">
	<ul>
    	<li>让</li>
    	<li><div style="margin-left:20px;">健</div></li>
    	<li><div style="margin-left:40px;">康</div></li>
    	<li><div style="margin-left:60px;">改</div></li>
    	<li><div style="margin-left:80px;">变</div></li>
    	<li><div style="margin-left:100px;">生</div></li>
    	<li><div style="margin-left:120px;">活</div></li>
    </ul>
</div>  -->
<div id="contPar" class="contPar">
	<div id="page1"  style="z-index:1;">
            <div class="title0"></div>
		<!-- <div class="title0"><%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%></div> -->
		<!-- <div class="title1">健康改变生活</div> -->
		<div class="imgGroug">
			<ul>
				<img alt="" class="img0 png" src="<%=contextPath%>/coframe/auth/login/images/page1_0.png">
				<img alt="" class="img1 png" src="<%=contextPath%>/coframe/auth/login/images/page1_1.png">
				<img alt="" class="img2 png" src="<%=contextPath%>/coframe/auth/login/images/page1_2.png">
			</ul>
		</div>
		<img alt="" class="img3 png" src="<%=contextPath%>/coframe/auth/login/images/page1_3.jpg">
	</div>
</div>
<div class="tyg-div-denglv">
	<div class="tyg-div-form" id="form1">
			<div style="height: 64px;line-height:64px;border-bottom:1px solid #191c2c;font-size:24px;color:#2ec0f6;font-weight: bold;">Login</div>
			<div style="margin:15px 0px;">
                <input type="text" class="mini-textbox" placeholder="请输入账号..."  id="userId" name="userId" placeholder="请输入用户名" onvalidation="onCheckUserId"/>
            </div>
            <div id="userId_error" class="errorText" style="color:#72ffed;"></div>
			<div style="margin:15px 0px;">
				<input type="text" class="mini-password" placeholder="请输入密码..." name="password"  id="userPassword" type="password" placeholder="请输入密码" vtype="minLength:6" minLengthErrorText="密码不能少于6个字符"onenter="keyboardLogin" onvalidation="onCheckPassword"/>
            </div>
            <div id="password_error" class="errobottom" style="color:#72ffed;"></div>
			<button onclick="login();" >登<span style="width:20px;"></span>录</button>
	</div>
</div>
<div class="foot">
    <p><%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "copyright")%><span></span></p>
</div>
<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
<script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/jquery-1.8.0.min.js"></script>
<script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/jquery.qrcode.min.js"></script>
<script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/com.js"></script>
 <script type="text/javascript">
   window.onload = function () {
       $('#userId,#userPassword').attr('style', 'border-width:1px;');
       $('#userId > span > input').attr('placeholder', '请输入用户名');
       $('#userPassword > span > input').attr('placeholder', '请输入密码');
   };
     nui.parse();
    jQuery('#outputAndroid').qrcode({width:150,height: 150,text: "http://***************:9009/wxappql/appUpdate/app-release.apk"});
    jQuery('#outputIos').qrcode({width:150,height: 150,text: "http://***************:9009/wxappql/appUpdate/app-release.apk"});
    $('.remove_frame').click(function(){
        $('#switcher').hide(100);
    });
     $(".mini-textbox-border").css({"width":"340px","height":"42px"})
     $('.mini-textbox-input').css({"width":"340px","height":"42px","line-height":"42px"})
     var form = new nui.Form("#form1");

     $("#userId").focus();

     function login(){


       form.validate();
       if(form.isValid()==false) return;
       var data = form.getData();
       var json = nui.encode(data);

       nui.ajax({
         url:"org.gocom.components.coframe.auth.LoginManager.login.biz.ext",
         type:'post',
         data:json,
         success:function(text){
            var o = nui.decode(text);
            if(o.exception==null){
	           var ret = o.retCode;
	           if(ret==1){
	           	 var a="http://192.168.99.40:8080/login.aspx?username="+data.userId+"&password="+data.password;
	             location.href="<%=request.getContextPath() %>/coframe/auth/login/redirect.jsp?original_url=<%=original_url %>";
	           }else if(ret==0){
	             nui.alert("输入密码错误");
	           }else if(ret==-2){
                nui.alert("用户无权限登录，请联系系统管理员");
	           }else if(ret==-3){
                nui.alert(o.msg);
	           }else{
                nui.alert("用户名不存在");
	           }
            }else{
                nui.alert("登录系统出错");
            }
         }
       });
     }
     function updateError(e) {
        var id = e.sender.name + "_error";
        var el = document.getElementById(id);
        if (el) {
            el.innerHTML = e.errorText;
        }else{
            el.innerHTML = "";
        }
    }

     function onCheckUserId(e){
       
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "用户名不能为空";
           updateError(e);
            e.isValid = false;
         }else{
            updateError(e); 
         }
       }
     }
     function onCheckPassword(e){
       
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "密码不能为空";
           updateError(e);
            e.isValid = false;
         }else{
            updateError(e);
         }
       }
     }
     //获取键盘 Enter 键事件并响应登录
     function keyboardLogin(e){
       login();
     }

     function OrgOfEmp(userid){
     	mini.get("window").show();
		var data={
			"userid":userid
		};
        mini.get("datagrid1").load(data);
     }
     function confirm(){
     }
     function cancel(){
     	mini.get("window").hide();
     }
   </script>

<!--[if IE 6]>
<script language="javascript" type="text/javascript" src="./script/ie6_png.js"></script>
<script language="javascript" type="text/javascript">
DD_belatedPNG.fix(".png");
</script>
<![endif]-->


</body>
</html>
 

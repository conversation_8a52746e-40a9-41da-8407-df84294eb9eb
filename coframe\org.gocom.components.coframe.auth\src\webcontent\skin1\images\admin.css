BODY {
	MARGIN: 0px
}
P {
	MARGIN: 0px
}
BODY {
	COLOR: #000; BACKGROUND-COLOR: #fff
}
BODY {
	FONT-SIZE: 12px; LINE-HEIGHT: 150%; FONT-FAMILY: "<PERSON>erd<PERSON>", "<PERSON><PERSON>", "Helvetica", "sans-serif"
}
TABLE {
	FONT-SIZE: 12px; LINE-HEIGHT: 150%; FONT-FAMILY: "Verdana", "Arial", "Helvetica", "sans-serif"
}
INPUT {
	FONT-SIZE: 12px; FONT-FAMILY: "<PERSON>erd<PERSON>", "<PERSON><PERSON>", "Helvetica", "sans-serif"
}
SELECT {
	FONT-SIZE: 12px; FONT-FAMILY: "Verdana", "Arial", "Helvetica", "sans-serif"
}
TEXTAREA {
	FONT-SIZE: 12px; FONT-FAMILY: "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Helve<PERSON>", "sans-serif"
}
A:link {
	COLOR: #036; TEXT-DECORATION: none
}
A:visited {
	COLOR: #036; TEXT-DECORATION: none
}
A:hover {
	COLOR: #f60; TEXT-DECORATION: underline
}
A.menuChild:link {
	COLOR: #036; TEXT-DECORATION: none
}
A.menuChild:visited {
	COLOR: #036; TEXT-DECORATION: none
}
A.menuChild:hover {
	COLOR: #f60; TEXT-DECORATION: underline
}
A.menuParent:link {
	COLOR: #000; TEXT-DECORATION: none
}
A.menuParent:visited {
	COLOR: #000; TEXT-DECORATION: none
}
A.menuParent:hover {
	COLOR: #f60; TEXT-DECORATION: none
}
TABLE.position {
	WIDTH: 100%
}
TR.position {
	HEIGHT: 25px; BACKGROUND-COLOR: #f4f7fc
}
TD.position {
	BORDER-RIGHT: #adceff 1px solid; PADDING-LEFT: 20px; BORDER-BOTTOM: #adceff 1px solid
}
TABLE.listTable {
	WIDTH: 98%; BACKGROUND-COLOR: #b1ceee
}
TR.listHeaderTr {
	FONT-WEIGHT: bold; HEIGHT: 25px; BACKGROUND-COLOR: #ebf4fd; TEXT-ALIGN: center
}
TR.listTr {
	HEIGHT: 25px; BACKGROUND-COLOR: #fff; TEXT-ALIGN: center
}
TR.listAlternatingTr {
	HEIGHT: 25px; BACKGROUND-COLOR: #fffdf0; TEXT-ALIGN: center
}
TR.listFooterTr {
	HEIGHT: 30px; BACKGROUND-COLOR: #ebf4fd; TEXT-ALIGN: center
}
TABLE.editTable {
	WIDTH: 98%; BACKGROUND-COLOR: #b1ceee
}
TR.editHeaderTr {
	HEIGHT: 25px; BACKGROUND-COLOR: #ebf4fd
}
TD.editHeaderTd {
	PADDING-LEFT: 50px; FONT-WEIGHT: bold
}
TR.editTr {
	HEIGHT: 30px
}
TD.editLeftTd {
	WIDTH: 150px; BACKGROUND-COLOR: #fffdf0; TEXT-ALIGN: center
}
TD.editRightTd {
	PADDING-LEFT: 10px; BACKGROUND-COLOR: #fff
}
TR.editFooterTr {
	HEIGHT: 40px; BACKGROUND-COLOR: #ebf4fd
}
TD.editFooterTd {
	PADDING-LEFT: 150px
}

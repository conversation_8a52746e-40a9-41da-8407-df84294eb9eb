<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<title>授权</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="keywords" content="">
	<meta name="description" content="">
	<!-- link css -->
	<link rel="stylesheet" href="./js/layui/css/layui.css">

</head>

<body>
	<div style="text-align: center;">
		<div class="content" style="padding-top: 10%; padding: 10px">
			<h5 style="line-height: 30px;" id="noAuth">
				您的系统<span style="color: red; font-weight: bold;">未被授权</span>，请提供注册信息并联系
				</br> <span style="font-weight: bold;">万序健康&nbsp;021-55155381</span>
				获取许可证书。
			</h5>
			<div id="isAuth" style="display:none;">
				您的系统<span style="color: red; font-weight: bold;">已授权</span>，如需更改请提供注册信息并联系
				</br> <span style="font-weight: bold;">万序健康&nbsp;021-55155381</span>
				获取许可证书。
			</div>
			<h4 style="line-height: 30px; padding-bottom: 10px;">注册信息:</h4>
			<h5>
				<form class="layui-form" action="">
					<div class="layui-form-item">
						<label class="layui-form-label">项目名称</label>
						<div class="layui-input-block">
							<input type="text" name="projectName" id="projectName" lay-verify="required"
								autocompvare="off" placeholder="请输入项目名称" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">Mac地址</label>
						<div class="layui-input-block">
							<input type="text" name="macAddress" id="macAddress" lay-verify="macAddress"
								autocompvare="off" disabled placeholder="请输入Mac地址" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">机器码</label>
						<div class="layui-input-block">
							<textarea placeholder="请输入请求码" class="layui-textarea" style="min-height: 55px;"
								lay-verify="required" disabled id="machineCode" name="machineCode"></textarea>
						</div>
					</div>

					<div>
						<button type="submit" class="layui-btn" lay-submit lay-filter="generateReq">导出注册信息</button>
						<button type="button" class="layui-btn layui-btn-normal" id="uploadLic"><i
								class="layui-icon"></i>上传lic文件</button>
						<button type="button" class="layui-btn" style="margin-left: 10px;" title="重置项目名称" id="reset"><i
								class="layui-icon layui-icon-refresh"></i></button>
					</div>
				</form>
			</h5>
		</div>
	</div>

	<script src="./js/jquery.min.js"></script>
	<script src="./js/layui/layui.js"></script>

	<script>
		layui.use(['element', 'form', 'upload'], function () {
			var $ = layui.jquery,
				element = layui.element,
				form = layui.form,
				layer = layui.layer,
				upload = layui.upload;

			// 请求信息文件
			form.on('submit(generateReq)', function (data) {
				delete data.field.file;
				data.field.projectName = encodeURIComponent(encodeURIComponent(data.field.projectName));
				var sendObj = JSON.stringify(data.field);
				var uri = "org.gocom.components.coframe.license.bean.dowloadRegInfo.flow?license="+sendObj;
				window.location.href = uri;
				return false;
			});

			// 重置项目名称
			$("#reset").click(function () {
				$('#projectName').val('');
				$("#projectName").attr("disabled", false);
				layer.alert("重置成功，请重新输入并生成注册信息", {
					icon: 1,
					skin: 'layer-ext-demo',
					title: "提示"
				});
			});

			// lic文件上传
			upload.render({
				elem: '#uploadLic',
				url: 'org.gocom.components.coframe.license.bean.uploadLicense.flow',
				accept: 'file',
				exts: 'lic', //只允许上传lic文件
				before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
					layer.load(); //上传loading
				},
				error: function (index, upload) {
					layer.closeAll('loading'); //关闭loading
				},
				done: function (res) {
					layer.closeAll('loading'); //关闭loading
					if (res.flag == 'true') {
						layer.alert("授权成功！请重新登录", {
							icon: 1,
							skin: 'layer-ext-demo',
							title: "提示"
						}, function (index) {
							window.CloseOwnerWindow(1);
							layer.close(index);
						});
					} else if(res.flag == 'expire') {
						layer.alert("lic文件已过期！请重新申请", {
							icon: 2,
							skin: 'layer-ext-demo',
							title: "提示"
						});
					} else {
						layer.alert("授权失败！请检查lic文件", {
							icon: 2,
							skin: 'layer-ext-demo',
							title: "提示"
						});
					}
				}
			});
		})

		$(document).ready(function () {
			init();
		})
		
		function SetData(data) {
			$('#content').hide();
			if(data){
				if(data.ISAUTH=='TRUE'){
					$('#noAuth').hide();
					$('#isAuth').show();
					isUpdate = true;
				}
				$('#content').show();
				$('#macAddress').val(data.MACADDRESS);
				$('#machineCode').val(data.MACHINECODE);
				if (data.PROJECTNAME && data.PROJECTNAME != undefined && data.PROJECTNAME != '') {
					$('#projectName').val(data.PROJECTNAME);
					$("#projectName").attr("disabled", true);
				}
			}
        }

		function init() {
            SetData(GetQueryString("authorg"));
        }

        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

	</script>
</body>

</html>
/*
* @Author: <PERSON>
* @Date:   2016-03-03 11:44:10
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-03-28 17:14:31
*/
.login {
	background: #d5e3ee;
}
.login .wrap {
	width: 100%;
	height: 100%;
	min-width: 1200px;
	min-height: 580px;
}
.login .main {
	width: 1200px;
	height: 580px;
	background: url(../images/bg.jpg) no-repeat;
}
.login-box {
	width: 370px;
	height: 90px;
	opacity: 0.75;
}
#input-text {
	margin: 5px 0;
	width: 280px;
	height: 35px;
	float: left;
}
#input-text em {
	width: 42px;
	border-color: #66afe9;
}
#userId, #userPassword {
	height: 35px;
	border-color: #66afe9;
}
.btn-info {
	margin: 5px 0 5px 10px;
	width: 80px;
	height: 80px;
	font-size: 18px;
	font-weight: 100;
}
.login .foot {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 58px;
}
.login .foot p {
	line-height: 58px;
	text-align: center;
	color: #4b4b4b;
}
.msg-text{
	text-align: center;
}
#input-text > em{
	height:35px;
}
#input-text input{
	height:35px;
}
#input-text > span{
	border: 1px solid #66afe9;
    padding-left:10px;
    height:35px;
}
#input-text > span > span{
	border:0px;
    display: table-cell;
    position: relative;
    z-index: 2;
    float: left;
    width: 238px;
    height:35px;
    margin-bottom: 0;
    display: block;
    width: 100%;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}
#msgbox{
	top: 280px;
}
.modal-title{
	color:#66afe9;
	text-align: center;
}
.modal-footer{
	width: 100%;
}
.modal-footer .btn{
	float: none;
	display:block;
	width: 120px;
	margin:0 auto;
}
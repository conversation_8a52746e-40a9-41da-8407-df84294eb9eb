package org.gocom.components.coframe.flowconfig.authconfig;


import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;

import org.gocom.components.coframe.auth.login.EOSlock;
import org.gocom.components.coframe.auth.login.LoginService;

import com.primeton.bfs.engine.json.JSONException;
import com.primeton.bfs.engine.json.JSONObject;

public class AuthInspect {
	public static LinkedHashMap<String, Long> sessionidmap = new LinkedHashMap<String, Long>();
	public static boolean inspectCode(HttpServletRequest request){
		boolean flag = true;
		try {
			String isauth = (String) request.getSession().getServletContext().getAttribute("ISAUTH");
			JSONObject  auth = new JSONObject(isauth);
			flag =  auth.getBoolean("ISAUTH");
			String sessionid = (String) request.getRequestedSessionId();
			flag = AuthInspect.execbyflag(flag,sessionid);
		} catch (JSONException e) {
			flag = false;
			System.out.println("应用授权校验异常 ！请联系开发人员！");
		}
		return flag;
	}
	public static boolean execbyflag(boolean flag,String sessionid){
		  AuthConfig aut = new AuthConfig();
		  if(flag) flag = aut.checkIndex();
		  if(!flag){
			  try {
				  EOSlock.checkandSetsession();
				  AuthInspect.randomlogout(sessionid);
				  flag = true;
			  } catch (Exception e) {
				flag = false;
			}
		  }
	      return flag;	
	}
	/**
	 * 未授权仅允许?人同时在线
	 * @param sessionid
	 */
	public static void randomlogout(String sessionid){
		Long  time  =  new Date().getTime()+2000;//+30*60*1000;
		sessionidmap.put(sessionid, time);
		if(sessionidmap.size()>2){
			//这是为了踢掉先登陆上来的人，但是老司机要求要踢当前登陆用户
			Long time2 = 0l;
			for (Entry<String, Long> entry : sessionidmap.entrySet()){
				time2 = entry.getValue();
				time  = new Date().getTime();
				if(time2<time){
					sessionid = entry.getKey();
					break;
				}
			}		   
			LoginService login = new LoginService();
			login.logout(sessionid);
			sessionidmap.remove(sessionid);
		} 
	}
}

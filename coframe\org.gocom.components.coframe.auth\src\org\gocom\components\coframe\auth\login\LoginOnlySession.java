package org.gocom.components.coframe.auth.login;

import java.util.HashMap;

import org.gocom.components.coframe.auth.SysConfig;

import com.eos.system.annotation.Bizlet;

public class LoginOnlySession {
	public static HashMap<String, String> sessionMap = new HashMap<String, String>();

	@Bizlet
	public static void setSessionId(String userid, String sessionId) {
		if (userid != null && sessionId != null) {
			sessionMap.put(userid, sessionId);
		}
	}

	public static String getSessionId(String userid) {
		if (userid != null)
			return sessionMap.get(userid);
		return "-1";
	}

	public static boolean isLogin(String userid, String sessionid) {

		String loginSessionId = LoginOnlySession.getSessionId(userid);
		if (loginSessionId == null || "".equals(loginSessionId))
			return true;
		if (sessionid.equals(loginSessionId))
			return true;
		return SysConfig.queryConfig("SYSLOGIN");
	}

	public static boolean loginCheck(String userid, String sessionid) {
		if (userid == null || sessionid == null)
			return true;
		boolean isLogin = LoginOnlySession.isLogin(userid, sessionid);
		if (!isLogin) {
			LoginService login = new LoginService();
			login.logout(sessionid);
		}
		return isLogin;
	}

}

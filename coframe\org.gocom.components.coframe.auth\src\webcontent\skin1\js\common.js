/**
 * Created by wimi on 2019/1/15.
 */
var defaultImgUrl = '../../../masterData/images/500x500.jpg';
var totalDiv;
var showImgURL = '../images/500x500.jpg';
var currentHtmlUrl = (window.location.href).substring(0, (window.location.href).indexOf("htmls")) + 'htmls/';
var locationUrl;
(function () {
    var host = window.location.host;
    var p = window.location.pathname;
    p = p.split('/');
    locationUrl = 'http://' + host + '/' + p[1];
})();
function changeUrl(imageurl) {
    if (imageurl) {
        if (imageurl.indexOf("/") != 0 && imageurl.indexOf("images/500x500.jpg") < 0 && imageurl.indexOf("http://") < 0) {
            imageurl = "/" + imageurl;
        }
    }

    if (!imageurl) {
        imageurl = defaultImgUrl;
    }
    if (imageurl.indexOf("http://") < 0 && imageurl.indexOf(defaultImgUrl) < 0) {
        imageurl = locationUrl + imageurl;
    }
    return imageurl;
}
function getFormData(formID) {
    var data = {};
    var flag = true;
    $(formID).find("input,select,textarea").each(function () {
        if ($(this).hasClass("layui-select")) {
            var name = $(this).attr("name");
            if(name){
                if($(this).attr('ts-selected')){
                    var val = $(this).attr('ts-selected');
                }else{
                    var val = $(this).val();
                }
                var require = $(this).attr("notempty");
                var disable= $(this).prop("disabled");
                // var visiable = $(this).is(":hidden");
                // if(!val&&name==='scompycd'){
                //     data['scompyname'] = val;
                //     val = $("#scompycd_copy").val();
                // }
                if ((require == 'true' || require == true) && !val&&!disable) {
                    bsAlert($(this).attr("errmsg"));
                    flag = false;
                    return false;
                }
                if (flag && name != undefined) {
                    data[name] = val;
                }
            }
        } else {
            var name = $(this).attr("name");
            if(name){
                if($(this).attr('ts-selected')){
                    var val = $(this).attr('ts-selected');
                }else{
                    var val = $(this).val();
                }
                if(val){
                    val= val.trim();
                }

                if(val){
                    val= val.replace(/[\"\'\“\”\‘\’]/g, "");
                }
                var require = $(this).attr("notempty");
                var reg = $(this).attr("reg");
                //	正则验证
                if (reg && val) {
                    reg = new RegExp(reg);
                    if (!reg.test(val)) {
                        bsAlert($(this).attr("errmsg"));
                        flag = false;
                        return false;
                    }
                }
                var disable= $(this).prop("disabled");
                var visiable = $(this).is(":hidden");
                if ((require == 'true' || require == true) && !val&&!disable&&!visiable) {
                    bsAlert($(this).attr("errmsg"));
                    flag = false;
                    return false;
                }
                if (flag && name != undefined) {
                    data[name] = val;
                }
            }

        }


    });
    return {
        datas: data,
        flag: flag
    };
}
function getFormDataNew(formID) {

    var arr=[];
    var flag = true;
    $(formID).find("input,select,textarea").each(function () {
        var data = {};
        if ($(this).hasClass("layui-select")) {
            var name = $(this).attr("name");
            var extcd = $(this).data("extcd");
            var certcd = $(this).data("certcd");
            var certextcd = $(this).data("certextcd");
            var mtcertextrevision = $(this).data("mtcertextrevision");
            var certrevision = $(this).data("certrevision");
            if(!extcd){
                extcd = $(this).parents(".layui-input-block").data("extcd");
                certcd = $(this).parents(".layui-input-block").data("certcd");
                certextcd = $(this).parents(".layui-input-block").data("certextcd");
                mtcertextrevision = $(this).parents(".layui-input-block").data("mtcertextrevision");
                certrevision = $(this).parents(".layui-input-block").data("certrevision");
            }
            if(name){
                if($(this).attr('ts-selected')){
                    var val = $(this).attr('ts-selected');
                }else{
                    var val = $(this).val();
                }
                var require = $(this).attr("notempty");
                var disable= $(this).prop("disabled");
                if ((require == 'true' || require == true) && !val&&!disable) {
                    bsAlert($(this).attr("errmsg"));
                    flag = false;
                    return false;
                }
                if (flag && name != undefined) {
                    data.extvalue = val;
                    data.extname = name;
                    data.extcd = extcd;
                    data.certcd = certcd;
                    data.certextcd = certextcd;
                    data.mtcertextrevision = mtcertextrevision;
                    data.certrevision = certrevision;
                    data._state = certextcd?'MODIFIED':'ADDED';
                    arr.push(data);
                }
            }
        } else {
            var name = $(this).attr("name");
            var extcd = $(this).data("extcd");
            var certcd = $(this).data("certcd");
            var certextcd = $(this).data("certextcd");
            var mtcertextrevision = $(this).data("mtcertextrevision");
            var certrevision = $(this).data("certrevision");
            if(!extcd){
                extcd = $(this).parents(".layui-input-block").data("extcd");
                certcd = $(this).parents(".layui-input-block").data("certcd");
                certextcd = $(this).parents(".layui-input-block").data("certextcd");
                mtcertextrevision = $(this).parents(".layui-input-block").data("mtcertextrevision");
                certrevision = $(this).parents(".layui-input-block").data("certrevision");
            }
            if(name){
                if($(this).attr('ts-selected')){
                    var val = $(this).attr('ts-selected');
                }else{
                    var val = $(this).val();
                }
                if(val){
                    val= val.trim();
                }

                if(val){
                    val= val.replace(/[\"\'\“\”\‘\’]/g, "");
                }
                var require = $(this).attr("notempty");
                var reg = $(this).attr("reg");
                //	正则验证
                if (reg && val) {
                    reg = new RegExp(reg);
                    if (!reg.test(val)) {
                        bsAlert($(this).attr("errmsg"));
                        flag = false;
                        return false;
                    }
                }
                var disable= $(this).prop("disabled");
                var visiable = $(this).is(":hidden");
                if ((require == 'true' || require == true) && !val&&!disable&&!visiable) {
                    bsAlert($(this).attr("errmsg"));
                    flag = false;
                    return false;
                }
                if (flag && name != undefined) {
                    data.extvalue = val;
                    data.extname = name;
                    data.extcd = extcd;
                    data.certcd = certcd;
                    data.certextcd = certextcd;
                    data.mtcertextrevision = mtcertextrevision;
                    data.certrevision = certrevision;
                    data._state = certextcd?'MODIFIED':'ADDED';
                    arr.push(data);
                }
            }

        }


    });
    return {
        datas: arr,
        flag: flag
    };
}
function getDomDatabyName(Dom) {
    var data ={};
    $(Dom).find("p,span,a,h4").each(function () {
        var name = $(this).attr("name");
        var val = $(this).text();
        if(name){
            data[name] = val;
        }
    });
    $(Dom).find("input,textarea").each(function () {
        var name = $(this).attr("name");
        var val = $(this).val();
        if(name){
            data[name] = val;
        }
    });
    return data;
}
//获取详情数据
//获取详情数据
function getDataWithDetail(data, namingId, callback,jspfilename) {
   
    var json =""
	var sendData = {
                "fileName":(jspfilename?jspfilename: getJspFileName()),
                "LibId": namingId,
                "dbName": getDataSource(),
				"param": JSON.stringify(data)
            };
            var resultList = null;
            mini.ajax({
                url: "com.wx.utils.publicMemer.JDBCComponent.dbComboQuery.biz.ext",
                data: sendData,
                type: "post",
                cache: false,
                async: false,
                contentType: 'text/json',
                success: function (result) {
                    if (result.sts == "1") {
                        json = result.resultList;
						if(callback) callback(json);
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR);
                    //mini.alert(jqXHR.responseText+'弹框');
                }
            });
	return json;
}

function seeImgS(data,namingId,flag,cd){
    var json = getDataWithDetail(data,namingId);
    if(flag){
        var photoJson = {
            title: "", //相册标题
            id: "", //相册id
            start: 0, //初始显示的图片序号，默认0
            data: []
        }
        if(json.length){
            layer.open({
                type: 2,
                title:'图片预览',
                area: ['800px', '542px'],
                skin: 'layer-ext-moon', //样式类名
                anim: 2,
                scrollbar:false,
                shadeClose: true, //开启遮罩关闭
                content: locationUrl + '/masterData/htmls/seach_lic_Img.jsp',
                success: function(layero, index){
                    layer.iframeAuto(index);
                    var iframe = window[layero.find('iframe')[0]['name']];
                    //调用子页面的全局函数
                    iframe.childs(data,namingId,flag,cd);
                }
            });
        }else{
            layer.msg('暂无图片');
        }
    }else{
        return json;
    }
}
//发票查看图片
function seeImginvoice(invcd,flag){
    var json = getDataWithDetail({ invcd:invcd},"com.wx.masterData.invoiceQueryNamingsql.select_inv_img");
    console.log(json);
    if(flag){
        var photoJson = {
            title: "", //相册标题
            id: "", //相册id
            start: 0, //初始显示的图片序号，默认0
            data: []
        }
        if(json){
            layer.open({
                type: 2,
                title:'图片预览',
                area: ['500px', '400px'],
                skin: 'layer-ext-moon', //样式类名
                anim: 2,
                scrollbar:false,
                shadeClose: true, //开启遮罩关闭
                content: locationUrl + '/masterData/htmls/seach_lic_Img.jsp',
                success: function(layero, index){
                    layer.iframeAuto(index);
                    var iframe = window[layero.find('iframe')[0]['name']];
                    //调用子页面的全局函数
                    iframe.childInvoice(invcd);
                }
            });

        }
    }else{
        return json;
    }
}
//判断字段值中是否含有',替换逗号为\'
function replaceComma(param) {
    if (param.indexOf("'") >= 0) {
        param = param.replace(/'/g, '`');
    }
    param = param.replace(/"/g, '“');
    return param;
}
function showSQL(id,sqlText,selectId,SQLTXT2,salveId,exportId,params,Excelname) {
    context.init({preventDoubleContext: false});
    context.settings({compress: true});
    var arr =[
        {header: '右键菜单'},
        {text: '查看执行SQL', action: function(e){
            var OpenWindow=window.open("", "查看执行SQL", "height=490, width=810,toolbar=no,menubar=no");
            OpenWindow.document.write("<TITLE>查看执行SQL</TITLE>") ;
            OpenWindow.document.write("<BODY>") ;
            OpenWindow.document.write("<h3>namingSQL:"+selectId+"</h3>") ;
            if(salveId){
                OpenWindow.document.write("<h3>salveId:"+salveId+"</h3>") ;
            }
            OpenWindow.document.write("<h2>执行SQL:</h2>") ;
            OpenWindow.document.write("<textarea style=\"overflow:auto; \" cols=\"95\" rows=\"15\">"+sqlText+"</textarea>");

            if(salveId){
                OpenWindow.document.write("<h2>salveId执行SQL:</h2>") ;
                OpenWindow.document.write("<textarea style=\"overflow:auto; \" cols=\"95\" rows=\"15\">"+SQLTXT2+"</textarea>");

            }

            OpenWindow.document.write("</BODY>") ;
            OpenWindow.document.write("</HTML>") ;
            OpenWindow.document.close();
        }}
    ];
    if(exportId){
        arr.push({text: '导出Excel表格', action: function(e){
            console.log(exportId);
            var exportData={
                "repMap": params,
                "namesql": exportId,
                "dbName": "default",
                "Excelname": Excelname
            };
            window.location.href="com.wx.masterData.ExportExcel2New.flow?_eosFlowAction=myaction&namesql="+exportData.namesql+"&Excelname="+exportData.Excelname+"&repMap="+JSON.stringify(exportData.repMap);
        }});
    }
    context.attach(id,arr);
}
function showGridSQL(id,sqlText,selectId,SQLTXT2,salveId,params,Excelname) {
    id = 'div[lay-id="'+id.replace("#",'')+'"] .layui-table-body';
    context.init({preventDoubleContext: false});
    context.settings({compress: true});
    var arr =[
        {header: '右键菜单'},
        {text: '查看执行SQL', action: function(e){
            var OpenWindow=window.open("", "查看执行SQL", "height=490, width=810,toolbar=no,menubar=no");
            OpenWindow.document.write("<TITLE>查看执行SQL</TITLE>") ;
            OpenWindow.document.write("<BODY>") ;
            OpenWindow.document.write("<h3>入参:</h3>") ;
            OpenWindow.document.write("<textarea style=\"overflow:auto; \" cols=\"95\" rows=\"5\">"+JSON.stringify(params)+"</textarea>");
            OpenWindow.document.write("<h3>namingSQL:"+selectId+"</h3>") ;
            if(salveId){
                OpenWindow.document.write("<h3>salveId:"+salveId+"</h3>") ;
            }
            OpenWindow.document.write("<h2>执行SQL:</h2>") ;
            OpenWindow.document.write("<textarea style=\"overflow:auto; \" cols=\"95\" rows=\"15\">"+sqlText+"</textarea>");

            if(salveId){
                OpenWindow.document.write("<h2>salveId执行SQL:</h2>") ;
                OpenWindow.document.write("<textarea style=\"overflow:auto; \" cols=\"95\" rows=\"15\">"+SQLTXT2+"</textarea>");

            }

            OpenWindow.document.write("</BODY>") ;
            OpenWindow.document.write("</HTML>") ;
            OpenWindow.document.close();
        }}
    ];
    if(Excelname){
        arr.push({text: '导出Excel表格', action: function(e){
            var exportData={
                "repMap": params,
                "namesql": selectId+'_export',
                "dbName": "default",
                "Excelname": Excelname
            };
            window.location.href="com.wx.masterData.ExportExcel2New.flow?_eosFlowAction=myaction&namesql="+exportData.namesql+"&Excelname="+exportData.Excelname+"&repMap="+JSON.stringify(exportData.repMap);
        }});
    }
    context.attach(id,arr);
}

//加载简单表格
function loadSimpleGridData(reqMap,selectId,Dom,cols,gridHeight,callback,Excelname,initHeight,checkedData,checkcd,setChecked,nopage){
    var nopage = (nopage?false:true);
	layui.use(['table','layer'], function(){
        var table = layui.table;
        var $ = layui.jquery;
        var layer = layui.layer;
        var sendData = {
            "reqMap": reqMap,
            "dbName": 'default',
            "LibId": selectId,
            "fileName": getJspFileName()
        };
        var hh = (initHeight?gridHeight:(gridHeight?'full-'+gridHeight:'full'));
        table.render({
            elem: Dom
            ,height: hh
            ,even:true,
            totalRow:true
            ,cellMinWidth: 80
            ,limits:[30,50,100,500,1000]
            ,where:sendData
            ,limit:30
            ,text: {none: '暂无数据'}
            ,url: 'com.wx.utils.publicMemer.JDBCComponent.dbPageQuery.biz.ext' //数据接口
            ,method:'POST'
            ,contentType:'application/json'
            ,page: nopage
            ,request: {
                pageName: 'pageStartIndex' //页码的参数名称，默认：page
                ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            ,parseData:function (res) {
                if(Dom=='#List'){
                    if($("#CNTALL_1").length){
                        $("#CNTALL_1").text(res.totalCount);
                    }
                }
                if(Dom){
                    if(res.hasOwnProperty("ExecSQL")){
                        showGridSQL(Dom,res.ExecSQL,selectId,null,null,reqMap,Excelname,initHeight);
                    }

                }
                var data = {
                    "code":(res.sts==1?'0':'-1'),
                    "msg": (res.msg), //解析提示文本
                    "count": res.totalCount, //解析数据长度
                    "data": res.resultList //解析数据列表
                };
                console.log(data);
                return data;
            }
            ,cols:cols
            ,done:function (res,curr,count) {
                table_data = res.data;
                if(callback){
                    callback(res.data);
                }
                if(checkedData&&checkcd){
                    if((res.data).length&&checkedData.length){
                        for(var i=0;i<(res.data).length;i++){
                            for(var j=0;j<checkedData.length;j++){
                                if((res.data)[i][checkcd]==checkedData[j][checkcd]){
                                    (res.data)[i]['LAY_CHECKED'] = true;
                                    var index= res.data[i]['LAY_TABLE_INDEX'];
                                    res.data[i] = $.extend(res.data[i],checkedData[j]);//合并数据
                                    $('tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true);
                                    $('tr[data-index=' + index + '] input[type="checkbox"]').next().addClass('layui-form-checked');
                                    break;
                                }
                            }
                        }
                    }
                }
                if(setChecked&&checkcd){
                    if((res.data).length){
                        for(var i=0;i<(res.data).length;i++){
                            if(res.data[i][checkcd]){
                                if(typeof checkJson!='undefined'){
                                    checkJson.push(res.data[i]);
                                }
                                (res.data)[i]['LAY_CHECKED'] = true;
                                var index= res.data[i]['LAY_TABLE_INDEX'];
                                $('tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true);
                                $('tr[data-index=' + index + '] input[type="checkbox"]').next().addClass('layui-form-checked');
                                $('tr[data-index=' + index + ']').addClass('layui-table-click');
                            }
                        }
                    }
                }
            }
        });
    });
}

//加载静态表格
function loadGridStaticData(data,Dom,cols,gridHeight,initHeight) {
    layui.use(['table','layer'], function(){
        var table = layui.table;
        var layer = layui.layer;
        var selectCountId;
        var hh = (initHeight?gridHeight:(gridHeight?'full-'+gridHeight:'full'));
        table.render({
            elem: Dom
            ,height: hh
            ,data: data
            ,even:true
            ,cellMinWidth: 80
            ,cols:cols
        });
    });
}
function laodGridData(config) {
    layui.use(['table','layer'], function(){
        var table = layui.table;
        var layer = layui.layer;
        
        var sendData = {
            "reqMap": config.reqMap,
            "callEntry": "main.getDataWithPage",
            "dbName": "default",
            "LibId": config.selectId,
            "fileName": getJspFileName()
        };
        var hh = (config.initHeight?config.gridHeight:(config.gridHeight?'full-'+config.gridHeight:'full'));
        table.render({
            elem: config.Dom
            ,height: hh
            ,even:true,
            totalRow:true
            ,toolbar: config.toolbar
            ,cellMinWidth: 80
            ,limits:[30,50,100,500,1000]
            ,where:sendData
            ,limit:30
            ,text: {none: '暂无数据'}
            ,url: 'com.wx.utils.publicMemer.JDBCComponent.dbPageQuery.biz.ext' //数据接口
            ,method:'POST'
            ,contentType:'application/json'
            ,page: true //开启分页
            ,request: {
                pageName: 'pageStartIndex' //页码的参数名称，默认：page
                ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            ,parseData:function (res) {
                if(!config.noall){
                    if(config.Dom=='#List'){
                        if($("#CNTALL_1").length){
                            $("#CNTALL_1").text(res.totalCount);
                        }
                    }
                }
				if(config.Dom){
                    if(res.hasOwnProperty("ExecSQL")){
                        showGridSQL(config.Dom,res.ExecSQL,config.selectId,null,null,config.reqMap);
                    }

                }
               
                var data = {
                    "code":(res.sts==1?'0':'-1'),
                    "msg": (res.msg), //解析提示文本
                    "count": res.totalCount, //解析数据长度
                    "data": res.resultList //解析数据列表
                };
                console.log(data);
                return data;
            }
            ,cols:config.cols
            ,done:function (res,curr,count) {
                table_data = res.data;
                if(config.callback){
                    config.callback(res.data);
                }
            }
        });
    });
}
//加载下拉表格框
function loadTableSelect(Dom,checkedKey,searchKey,reqMap,selectId,cols,Done,trClick) {
    var tableSelect = layui.tableSelect;
    if(!selectId){
        selectId = $(Dom).attr("selectId");
    }
    var sendData = {
        "reqMap": reqMap,
        "dbName": 'default',
        "LibId": selectId,
        "fileName": getJspFileName()
    };
    console.log(sendData);
    tableSelect.render({
        elem: Dom,	//定义输入框input对象 必填
        checkedKey: checkedKey, //表格的唯一建值，非常重要，影响到选中状态 必填
        searchKey: searchKey,	//搜索输入框的name值 默认keyword
        searchPlaceholder: '关键词搜索',	//搜索输入框的提示文字 默认关键词搜索
        sendData:sendData,//add by lxy
        trClick:trClick,//add by lxy
        table: {	//定义表格参数，与LAYUI的TABLE模块一致，只是无需再定义表格elem
             limits:[30,50,100,500]
            ,where:sendData
            ,limit:30
            ,text: {none: '暂无数据'}
            ,url: 'com.wx.utils.publicMemer.JDBCComponent.dbPageQuery.biz.ext' //数据接口
            ,method:'POST'
            ,contentType:'application/json'
            ,page: true //开启分页
            ,request: {
                pageName: 'pageStartIndex' //页码的参数名称，默认：page
                ,limitName: 'pageSize' //每页数据量的参数名，默认：limit
            }
            ,parseData:function (res) {
                var data = {
                    "code":(res.sts==1?'0':'-1'),
                    "msg": (res.msg), //解析提示文本
                    "count": res.totalCount, //解析数据长度
                    "data": res.resultList //解析数据列表
                };
                return data;
            }
            ,cols:cols
        },
        done: function (elem, data) {
            Done(elem,data);
        }
    })
}

//加载树形table
function loadTreeTable(Dom,reqMap,selectId,iconKey,primarykey,parentkey,cols) {
    var treeTable = layui.treeTable;
    if(Dom&&reqMap){
        reqMap["showsql"] = true;
    }
    var sendData = {
        "reqMap": reqMap,
        "callEntry": "main.getDataWithDetail",
        "dbName": "default",
        "namingId": selectId
    };

    treeTable.render({
        elem: Dom,
        url: 'com.wx.utils.publicMemer.component.queryNamedSqlMaptoEosMapbiz.biz.ext',
        icon_key: iconKey,
        sendData:sendData,
        primary_key: primarykey,
        parent_key: parentkey,
        icon: {
            open: 'layui-icon layui-icon-triangle-d',
            close: 'layui-icon layui-icon-triangle-r',
            left: 16,
        },
        cols: cols
    });
}
//加载不分页表格
function loadNoPageGridData(reqMap,selectId,Dom,cols,gridHeight,Excelname,initHeight,chooseFirst,callback) {
    layui.use(['table','layer'], function(){
        var table = layui.table;
        var layer = layui.layer;
        var selectCountId;
        if(Dom&&reqMap){
            reqMap["showsql"] = true;
        }
        var sendData = {
            "reqMap": reqMap,
            "callEntry": "main.getDataWithDetail",
            "dbName": "default",
            "namingId": selectId
        };
        var hh = (initHeight?gridHeight:(gridHeight?'full-'+gridHeight:'full'));
        table.render({
            elem: Dom
            ,height: hh
            ,even:true
            ,cellMinWidth: 80
            ,where:sendData
            ,text: {none: '暂无数据'}
            ,url: 'com.wx.utils.publicMemer.component.queryNamedSqlMaptoEosMapbiz.biz.ext' //数据接口
            ,method:'POST'
            ,contentType:'application/json'
            ,parseData:function (res) {
                if(chooseFirst){
                    res.repMap[0].LAY_CHECKED=true;
                }
                if(Dom){
                    if(res.hasOwnProperty("SQLTXT")){
                        showGridSQL(Dom,res.SQLTXT,selectId,null,null,reqMap,Excelname);
                    }
                }
                var data = {
                    "code":(res.exception?'-1':'0'),
                    "msg": (res.exception?res.exception.message:''), //解析提示文本
                    "data": res.repMap //解析数据列表
                };
                return data;
            }
            ,cols:cols,
            done:function (data) {
                callback(data)
            }
        });
    });
}
layui.use(['laydate','jquery'],function () {
    var $ = layui.jquery;
    var laydate = layui.laydate;
    $('.laydate:not(.month)').each(function () {
        laydate.render({
            elem: this,
            type: 'date',
            format:'yyyy-MM-dd'
        });
    });
    $('.laydate.month').each(function () {
        laydate.render({
            elem: this,
            type: 'month'
        });
    });

    $('.searchWrap input[type="text"]').bind('keyup', function (event) {
        if (event.keyCode == "13") {
            //回车执行查询
            // var btn = $(".search-top,.searchWrap").find("button");
            var btn = $(".searchArea .searchBtn");
            var funcs = btn.attr("onclick");
            if (funcs) {
                funcs = funcs.replace("this", "");
                eval(funcs);
            } else {
                btn.click();
            }
        }
    });
});

function initEnter(dom,funcs) {
    $(dom).bind('keyup', function (event) {
        if (event.keyCode == "13") {
            //回车执行查询
            if (funcs) {
                funcs = funcs.replace("this", "");
                eval(funcs);
            }
        }
    });
}
//序号一样的排列在一起
function ResortTogether(data, samecd) {
    console.log(data);
    if (typeof (data) === 'Array' || typeof (data) === 'object') {
        if (data.length > 1) {
            for (var i = 0; i < data.length; i++) {
                //将序号一致的排列一起
                findSame(data, samecd, data[i]);
            }
        }
    }
    return data;
}
function findSame(data, samecd, val) {
    for (var i = 0; i < data.length; i++) {
        if (data[i][samecd] == val[samecd]) {
            change_b_after_a(data, val, data[i]);
        }
    }
    return data;
}
//数组中，将b插到a的后面
function change_b_after_a(arr, a, b) {
    if (arr.length > 1) {
        //先找到a后面一位的位置
        var index = arr.indexOf(a) + 1;
        //记录b的位置
        var indexB = arr.indexOf(b);
        if (index < indexB) {
            //删除b
            arr.splice(indexB, 1);
            index = arr.indexOf(a) + 1;
            arr.splice(index, 0, b);

        } else {
            //a后面加入b
            arr.splice(index, 0, b);
            indexB = arr.indexOf(b);
            //删除b
            arr.splice(indexB, 1);
        }

    }
    return arr;
}

//排序数组对象，根据某个字段排序
function sortByDesc(data, bycd) {
    if (data) {
        if (data.length) {
            data.sort(function (a, b) {
                return b[bycd] - a[bycd];
            });
            //	默认选择第一个作为最大的
            // 	var max = data[0];
            // 	for (var i = 0; i < data.length; i++) {
            // 		//如果当前大于最大值，这这两个数组更换位置
            // 		if (data[i][bycd] > max[bycd]) {
            // 			change_b_after_a(data, data[i], max);
            // 			max = data[i];
            // 		}
            // 	}
        }
    }

    return data;
}
function showTips(t) {
    var row=$(t).attr('data-tips');
    layer.tips(row,t,{tips:[1,'#3595CC']})
}
function closeTips() {
    layer.closeAll('tips');
}
//获取当前时间年月日
function getTodayDate() {
    var d = new Date();
    var str = d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate();
    str = new Date(str.replace(/-/g, "/"));
    return str
}

function getToday() {
    return GetDateStr(0);
}

//获取当前时间年月日时分秒
function getCurrentTime() {
    var date = new Date();
    var d = GetDateStr(0)+' '+date.getHours()+':'+date.getMinutes()+':'+date.getSeconds();
    return d;
}

function getPastOneMonth() {
    return GetDateStr(-30);
}

function getYesterday() {
    return GetDateStr(-1);
}

function GetDateStr(AddDayCount) {
    var dd = new Date();
    dd.setDate(dd.getDate() + AddDayCount);//获取AddDayCount天后的日期
    var y = dd.getFullYear();
    var m = (dd.getMonth() + 1) < 10 ? "0" + (dd.getMonth() + 1) : (dd.getMonth() + 1);//获取当前月份的日期，不足10补0
    var d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate();//获取当前几号，不足10补0
    return y + "-" + m + "-" + d;
}
function GetMonthStr(AddDayCount) {
    var dd = new Date();
    dd.setDate(dd.getDate() + AddDayCount);//获取AddDayCount天后的日期
    var y = dd.getFullYear();
    var m = (dd.getMonth() + 1) < 10 ? "0" + (dd.getMonth() + 1) : (dd.getMonth() + 1);//获取当前月份的日期，不足10补0
    return y + "-" + m;
}

// Resort(data,"deptcd","higherdeptcd");
function Resort(data, child, parent) {
    //超过两个数据进行排序，否则直接返回
    if (typeof (data) === 'Array' || typeof (data) === 'object') {
        if (data.length > 1) {
            for (var i = 0; i < data.length; i++) {
                //将子排在直接父级后面
                findChild(data, child, parent, data[i]);
            }
        }
    }
    return data;
}
function doAction(sendData, EXECSQL, tagkey, type, b) {
    var a = false;
    var mainResult;
    //默认调用callSysRoute2,type=2是调用callSysRoute3
    if (type == 3) {
        mainResult = callSysRoute3(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    } else if (type == 4) {
        mainResult = callSysRoute4(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    }
    else if (type == 5) {
        mainResult = callSysRoute5(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    }
    else {
        mainResult = callSysRoute2(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    }
    if (mainResult.exception != null) {
        bsAlert(mainResult.exception.message);
    } else {
        if (mainResult.result.RETN_CODE == 1) {
            a = b ? mainResult : true;
        } else {
            bsAlert(parseMessage(mainResult.result.message).mainText);
        }
    }
    return a;
}
function doAction2(sendData, EXECSQL, tagkey, type) {
    var a = false;
    var mainResult;
    //默认调用callSysRoute2,type=2是调用callSysRoute3
    if (type == 3) {
        mainResult = callSysRoute3(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    } else if (type == 4) {
        mainResult = callSysRoute4(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    }
    else if (type == 5) {
        mainResult = callSysRoute5(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    }
    else {
        mainResult = callSysRoute2(sendData, { 'EXECSQL': EXECSQL }, {}, [], tagkey, "default");
    }
    return mainResult;
}

function doActionAsync(options) {
    // datas, json_args, json_argsdetail, json_columns, tagkey, dbName
    var rs = null;
    var index = layer.load(1, {
        shade: [0.3,'#000']
    });
    var sendData = {
        "datas": options.datas,
        "json_args": { 'EXECSQL': options.pkg },
        "json_argsdetail": {},
        "json_columns": [],
        "tagkey": options.tagkey,
        "dbName": 'default'
    };
    $.ajax({
        url: 'com.wx.sysroute.SysRoute.callSysRoute' + (options.datas.length ? '4' : '') + '.biz.ext',
        data: JSON.stringify(sendData),
        type: 'post',
        cache: false,
        contentType: 'application/json;charset=UTF-8',
        success: options.success,
        error: options.error
    });
}

function callSysRoute(datas,json_args,json_argsdetail,json_columns,tagkey,dbName){
	var res={};
	var sendData={"datas":datas,"json_args":json_args,"json_argsdetail":json_argsdetail,"json_columns":json_columns,"tagkey":tagkey,"dbName":dbName};
	mini.ajax({
    	url: "com.wx.utils.publicMemer.SysRoute.callSysRoute.biz.ext",
        data: sendData,
        type: "post",
        cache:false,
	    async:false,
	    contentType:'text/json',
        success: function (result) {
    		var re=result.result;
    		var message	= result.message;
    		if(re.RETN_CODE==1){
    			res.message=re.message;
    			res.flag=true;
    		}else if(re.RETN_CODE==-1){
    			res.message=re.message;
    			res.flag=false;
    		}else{
    			res.message=message;
    			res.flag=false;
    		}
        },
        error: function (jqXHR, textStatus, errorThrown) {
        	mini.alert(jqXHR.responseText);
        }
    });
	return res;
}

function callSysRoute2(datas,json_args,json_argsdetail,json_columns,tagkey,dbName){
	var rs = null;    
	var sendData={"datas":datas,"json_args":json_args,"json_argsdetail":json_argsdetail,"json_columns":json_columns,"tagkey":tagkey,"dbName":dbName};
	$.ajax({
    	url: "com.wx.utils.publicMemer.SysRoute.callSysRoute.biz.ext",
        data: JSON.stringify(sendData),
        type: "post", 
        cache:false,
	    async:false,
	    contentType:'application/json;charset=UTF-8',
        success: function (result) {
    		rs=result;
        },
        error: function (jqXHR, textStatus, errorThrown) {
        	mini.alert(jqXHR.responseText);
        }
    });
	return rs;
}
// 截取错误消息字符串 返回 obj.mainText
function parseMessage(msg) {
    if (typeof (msg) == 'object') {
        if (msg.hasOwnProperty('result')) {
            msg = msg.result.message;
        }
        if (msg.hasOwnProperty('mainText')) {
            msg = msg.mainText;
        }

    }
    var jsonmsg = { title: "提示" };
    jsonmsg.lestText = msg;
    if (msg) {
        if (msg.indexOf("|~~") == -1) {
            jsonmsg.mainText = msg;
        } else {
            var substr = msg.match(/\|~~.*?\|~~/g);
            var temp = "";
            for (var i = 0; i < substr.length; i++) {
                temp += substr[i].trim().substring(3, substr[i].length - 3) + "\n"
            }
            jsonmsg.mainText = temp;
        }
    }

    return jsonmsg;
}
function gettableHeight()
{
    var top = $(".layui-tab-item").offset();
    if(top){
        top = top.top;
    }
    var height =  (top?top+25:0);
    return height;

};
$('.searchArea input').focus(function () {
    $(this).data("placeholder",$(this).attr("placeholder"));
    $(this).attr("placeholder","");
    $(this).select();
});
$('.searchArea input').blur(function () {
    $(this).attr("placeholder",$(this).data("placeholder"));
});
//type==1 表示disable 的也清除
function clearFormDatasById(FormId,type) {
    var p = $(FormId);
    p.find("input").each(function () {
        var c = $(this);
        if (c.prop("disabled") != true||type==1) {
            c.val("");
        }
        if (c.attr("id") == "_state" || c.attr("id") == '_STATE') {
            c.val("ADDED");
        }
    });
    p.find("span,p,a,div").each(function () {
        var c = $(this);
        if(c.attr("name")){
            if (c.prop("disabled") != true||type==1) {
                c.html("");
            }
        }
    });
    p.find("select").each(function () {
        var c = $(this);
        if (c.hasClass("select2")) {
            c.select2().val(null).trigger("change");
        } else {
            if (c.prop("disabled") != true||type==1) {
                c.val("");
            }

        }
    });
    p.find("textarea").each(function () {
        var c = $(this);
        c.val("");
    });
}
function getWidthOfText(txt, fontname, fontsize){
    if(getWidthOfText.c === undefined){
        getWidthOfText.c=document.createElement('canvas');
        getWidthOfText.ctx=getWidthOfText.c.getContext('2d');
    }
    getWidthOfText.ctx.font = fontsize + ' ' + fontname;
    return getWidthOfText.ctx.measureText(txt).width;
}
// 取url传递的值 转json
function geturl() {
    var string_a = window.location.search;
    if (string_a.substr(0, 1) == '?') string_a = string_a.substr(1);
    var string = string_a.split('&');
    var res = {};
    for (var i = 0; i < string.length; i++) {
        var str = string[i].split('=');
        res[str[0]] = decodeURI(str[1]);
    }
    // 返回json对象
    return $.parseJSON(JSON.stringify(res));
};
function formatNumber(num,cent,isThousand) {
    if(num){
        num = num.toString().replace(/\$|\,/g,'');
        // 检查传入数值为数值类型
        if(isNaN(num))
            num = "0";

        // 获取符号(正/负数)
        var sign = (num == (num = Math.abs(num)));

        num = Math.floor(num*Math.pow(10,cent)+0.50000000001);  // 把指定的小数位先转换成整数.多余的小数位四舍五入
        var cents = num%Math.pow(10,cent);              // 求出小数位数值
        num = Math.floor(num/Math.pow(10,cent)).toString();   // 求出整数位数值
        cents = cents.toString();               // 把小数位转换成字符串,以便求小数位长度

        // 补足小数位到指定的位数
        while(cents.length<cent)
            cents = "0" + cents;
        if(isThousand) {
            // 对整数部分进行千分位格式化.
            for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++)
                num = num.substring(0,num.length-(4*i+3))+','+ num.substring(num.length-(4*i+3));
        }
        if (cent > 0)
            return (((sign)?'':'-') + num + '.' + cents);
        else
            return (((sign)?'':'-') + num);
    }else{
        return num;
    }

}
//判断数组中是否存在某个元素值
//存在则返回true,不存在返回false
function isInArray(arr, x, cd) {
    var flag = false;
    if (arr) {
        if (arr.length) {
            for (var i = 0; i < arr.length; i++) {
                if (arr[i][cd] === x[cd]) {
                    flag = true;
                }
            }
        }
    }
    return flag;
}
//查询数组的父类是否存在
function isInArryByVal(arr,val,cd,pcd) {
    var flag = false;
    if (arr) {
        if (arr.length) {
            for (var i = 0; i < arr.length; i++) {
                var index = arr.indexOf(val);
                if(i!=index){
                    if (arr[i][cd] === val[pcd]) {
                        flag = true;
                    }
                }
            }
        }
    }
    return flag;
}

//获取不重复的随机数
function getTimestamp() {
    return (new Date()).valueOf()+getRandom(2);
}
//生成随机数
function getRandom(n) {
    var Num="";
    for(var i=0;i<n;i++)
    {
        Num+=Math.floor(Math.random()*10);
    }
    return Num;
}
//只能输入正整数
function onlyInputInt(obj) {

    var minValue = $(obj).data("minvalue");
    var maxValue = $(obj).data("maxvalue");
    if(minValue||minValue==0){
        obj.value = obj.value.replace(/[^\d]/g, ""); //清除"数字"以外的字符
        //不能0开头
        if (obj.value == 0 || obj.value == "0"||obj.value=='') {
            obj.value = minValue;
        }
        if(maxValue||maxValue==0){
            if(obj.value>maxValue){
                obj.value=maxValue
            }
        }else{
            //不能超过10个
            if (obj.value.length > 10) {
                obj.value = obj.value.replace(obj.value, obj.value.substring(0, 10));
            }
        }

    }else{
        obj.value = obj.value.replace(/[^\d]/g, ""); //清除"数字"以外的字符
        //不能0开头
        if (obj.value == 0 || obj.value == "0") {
            obj.value = "";
        }
        if(maxValue||maxValue==0){
            if(obj.value>maxValue){
                obj.value=maxValue
            }
        }else{
            //不能超过10个
            if (obj.value.length > 10) {
                obj.value = obj.value.replace(obj.value, obj.value.substring(0, 10));
            }
        }
    }

}
function validPrice(obj) {
    obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
    obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字
    obj.value = obj.value.replace(/^00+/,obj.value.substring(0,1));//校验0000这种情况
    obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个, 清除多余的
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d\d).*$/, '$1$2.$3'); //只能输入四位小数
    if (obj.value.length > 10) {
        obj.value = obj.value.replace(obj.value, obj.value.substring(0, 10));
    }
}
//获取table 数组
function getTableArr(id) {
    var arr=[];
    if($(id).find("tr").length){
        $(id).find("tr").each(function (index,el) {
            var re ={};
            if($(el).data("_state")){
                re._state = $(el).data("_state");
            }
            if($(el).data("revision")||$(el).data("revision")==0){
                re.revision = $(el).data("revision");
            }
            if($(el).data("cd")&&$(el).data("cdname")){
                re[$(el).data("cdname")] = $(el).data("cd");
            }
            $(el).find("td").each(function (index,el2) {
                if (!$(el2).hasClass('btns')) {
                    var name = $(el2).data("name");
                    var val = $(el2).text();
                    if(val){
                        val = val.trim();
                    }
                    re[name] = val;
                }
            });
            arr.push(re);
        })
    }
    return arr;
}
//检查数据中的某个元素是否是相同的，不同的返回false
function isSameInArray(arr, cd) {
    var flag =true;
    if(arr.length){
        for (var i=0;i<arr.length;i++){
            for (var j=0;j<arr.length;j++){
                if(arr[i][cd]!=arr[j][cd]){
                    flag = false;
                    break;
                }
            }
            if(!flag){
                break;
            }
        }
    }
    return flag;
}
function bsAlert(msg,time,fun) {
    layer.msg(msg,
    {
        // time:(time?time:1500),
        shade:'#000',
        shadeClose:true,
        time:0,
        anim: 6
    });
}

function loadCheckbox(dom,callback) {
    $(dom).parents(".layui-layer").find("input[type='checkbox']").change(function () {
        var name = $(this).attr("name");
        var value = $(this).is(':checked');
        if($(this).hasClass("all")){
            $(dom).parents(".layui-layer").find("input[type='checkbox'][name="+name+"]:enabled").prop('checked',value);
        }
        if(name=='checkThisAll'){
            $(dom).parents(".layui-layer").find("input[type='checkbox']:enabled").prop('checked',value);
        }
        if(callback){
            callback();
        }
    });

}
function getImglen(dom) {
    var arr = [];
    $(dom).each(function (index, el) {
        var cd = $(el).data('cd');
        var imgcd = $(el).data('imgcd');
        var v = $(el).data('v');
        var width = $(el).data('width');
        var height = $(el).data('height');
        var state = $(el).data('state');
        var imgurl = $(el).find('img').data('src');
        var json = {};
        if (imgurl&&imgurl!='../../images/500x500.jpg') {
            arr.push(json);
        }
    });
    return arr.length;
}
function getTableInputArr(id) {
    var arr=[];
    if($(id).find("tr").length){
        $(id).find("tr").each(function (index,el) {
            var re ={};
            if($(el).data("_state")){
                re._state = $(el).data("_state");
            }
            if($(el).data("revision")||$(el).data("revision")==0){
                re.revision = $(el).data("revision");
            }
            if($(el).data("cd")&&$(el).data("cdname")){
                re[$(el).data("cdname")] = $(el).data("cd");
            }
            $(el).find("td").each(function (index,el2) {
                if (!$(el2).hasClass('btns')) {
                    var name = $(el2).data("name");
                    if(name){
                        var val="";
                        if($(el2).find("select").length){
                            val = $(el2).find("select").val();
                        }
                        if($(el2).find("input").length){
                            val = $(el2).find("input").val();
                        }

                        if(val){
                            val = val.trim();
                        }
                        if(val){
                            re[name] = val;
                        }
                    }
                }
            });
            arr.push(re);
        })
    }
    return arr;
}
function getImgData(dom,relascd,imgtype,cdval,cdname) {
    var arr = [];
    $(dom).each(function (index, el) {
        var cd = $(el).data('cd');
        var imgcd = $(el).data('imgcd');
        var v = $(el).data('v');
        var width = $(el).data('width');
        var height = $(el).data('height');
        var state = $(el).data('state');
        var imgurl = $(el).find('img').data('src');
        var json = {};
        if (imgurl) {
            if(state!='ADDED'){
                json[relascd] = cd;
                json.imgcd = imgcd;
                json.revision = v;
            }
            json._state = state;
            if(cdname){
                json[cdname] = cdval;
            }
            if(state!='DELETE'){
                json.imagetype = imgtype?imgtype:9;
                json.imageurl = imgurl;
                json.imgwidth = width;
                json.imgheight = height;
                json.RESOLUTION = width + '*' + height;
            }
            if (state) {
                if(state==="DELETE"&&!cd){

                }else{
                    arr.push(json);
                }

            }
        }
    });
    return arr;
}

function removeImg(el) {
    var li = $(el).parent();
    var index = layer.confirm('确认删除本张图片吗？',{title:'提示'}, function(){
        li.attr('data-state', 'DELETE');
        li.data('state', 'DELETE');
        li.addClass('hide');
        layer.close(index);
    });
}
var uploadImage = new Object();
// 上传图片
uploadImage.openUpload = function (a,type,id) {
    var url = 'fileUpload.jsp?uploadType=image';
    if (a) {
        url = locationUrl + '/masterData/htmls/fileUpload.jsp?uploadType=image';
    }
    var uploadIndex = layer.open({
        type:2,
        area: ['80%', '95%'],
        maxmin:true,
        id:'modal-upload',
        title:'上传图片',
        content:url,
    });
    $('#modal-upload').data("uploadIndex",uploadIndex);
    if(type){
        $('#modal-upload').data("type",type);
    }
    if(id){
        $('#modal-upload').data("type",id);
    }
};
function openCallback(result) {
    if (result.status == 1) {
        console.log(result);
        var type = $('#modal-upload').data("type");
        var id = $('#modal-upload').data("id");
        var uploadIndex = $('#modal-upload').data("uploadIndex");
        if(type){
            uploadImageFn(result,type);
        }
        else if(id){
            uploadImageFn(result,id);
        }else{
            uploadImageFn(result);
        }
        layer.close(uploadIndex);
    } else {
        alert(result.message);
    }
}
// 图片预览
function showImg(src_a) {
    // console.log(src_a);
    if (src_a) {
        //图片分成两种情况，1：上传的图片，2默认的图片
        //1.默认图片  locationUrl + '/masterData/htmls/showImg.jsp'
        var src = '';

        if (src_a.indexOf("uploadFile") < 0 && src_a.indexOf("images/500x500.jpg") > 0) {
            // src = src_a.replace("../../", "../");
            src = src_a;
        } else {
            src = src_a;
        }
        //在判断是否含有http://
        if (src.indexOf("http://") < 0 && src.indexOf("images/500x500.jpg") < 0) {
            src = locationUrl + src;
        }
        console.log(src)
        showImgURL = src;
        layer.open({
            type: 1,
            title:'图片预览',
            area: ['500px', '500px'],
            skin: 'layer-ext-moon', //样式类名
            anim: 2,
            shadeClose: true, //开启遮罩关闭
            content: '<img style="width:100%;" src="'+src+'"/>'
        });
    }
}

//获取数据with slaveId
function getDataWithSlaveId(pageIndex, reqMap, selectId, selectCountId, slaveSqlId, p, noInput,Dom) {
    if (!pageIndex) {
        pageIndex = 0;
    }
    if(Dom&&reqMap){
        reqMap["showsql"] = true;
    }
    var pageSize;
    if (p) {
        pageSize = p.getPageSize();
    } else {
        pageSize = 9999;
    }
    var json = "";
    var sendData = {
        "reqMap": reqMap,
        "pageSize": pageSize,
        "pageStartIndex": pageIndex,
        "callEntry": "main.getDataWithSlaveId",
        "dbName": "default",
        "selectId": selectId,
        "selectCountId": selectCountId,
        "slaveSqlId": slaveSqlId
    };
    $.ajax({
        url: "com.wx.utils.publicMemer.component.queryMasterSlavePageNamingsql.biz.ext",
        data: JSON.stringify(sendData),
        type: "post",
        cache: false,
        async: false,
        contentType: 'application/json;charset=UTF-8',
        success: function (result) {
            if(Dom){
                if(result.hasOwnProperty("SQLTXT")){
                    showSQL(Dom,result.SQLTXT,selectId,result.SQLTXT2,slaveSqlId);
                }
            }
            if (result.exception != null) {
                bsAlert(result.exception.message);
            } else {
                json = result.repMap;
                if (p) {
                    p.showPage(pageIndex, result.totalCount, noInput);
                }
                if (typeof pagenum != undefined) {
                    pagenum = pageIndex;
                }
            }
        }
    });
    return json;
}


//只能输入数字或者小数
function onlyInputNumber(obj) {
    obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
    obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字
    obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个, 清除多余的
    obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入个小数
    if (obj.value.length > 10) {
        obj.value = obj.value.replace(obj.value, obj.value.substring(0, 10));
    }

}

function getDispTableArr(id) {
    var arr=[];
    if($(id).find("tr").length){
        $(id).find("tr").each(function (index,el) {
            if($(el).is(':visible')){
                var re ={};
                if($(el).data("_state")){
                    re._state = $(el).data("_state");
                }
                if($(el).data("revision")||$(el).data("revision")==0){
                    re.revision = $(el).data("revision");
                }
                if($(el).data("cd")&&$(el).data("cdname")){
                    re[$(el).data("cdname")] = $(el).data("cd");
                }
                $(el).find("td").each(function (index,el2) {
                    if (!$(el2).hasClass('btns')) {
                        var name = $(el2).data("name");
                        var val = $(el2).text();
                        if(val){
                            val = val.trim();
                        }
                        if(name){
                            re[name] = val;
                        }
                    }
                });
                arr.push(re);
            }

        })
    }
    return arr;
}

function getValidTableInputArr(id) {
    var flag = true;
    var arr=[];
    if($(id).find("tr").length){
        $(id).find("tr").each(function (index,el) {
            var re ={};
            var json = $(el).data("json");
            if (typeof (json) == "string") {
                json = json.replace(/'/g, '"');
                json = JSON.parse(json);
            }
            if($(el).data("_state")){
                re._state = $(el).data("_state");
            }
            if($(el).data("revision")||$(el).data("revision")==0){
                re.revision = $(el).data("revision");
            }
            if($(el).data("cd")&&$(el).data("cdname")){
                re[$(el).data("cdname")] = $(el).data("cd");
            }
            $(el).find("td").each(function (index,el2) {
                if (!$(el2).hasClass('btns')) {
                    var name = $(el2).data("name");
                    if(name){
                        var val = $(el2).val();
                        if($(el2).find("select").length){
                            val = $(el2).find("select").val();
                        }
                        if($(el2).find("input").length){
                            val = $(el2).find("input").val();
                        }
                        if(val){
                            val= val.trim();
                        }
                        if(val){
                            val= val.replace(/[\"\'\“\”\‘\’]/g, "");
                        }

                        var require = $(el2).attr("notempty");
                        var reg = $(el2).attr("reg");
                        //	正则验证
                        if (reg && val) {
                            reg = new RegExp(reg);
                            if (!reg.test(val)) {
                                bsAlert($(el2).attr("errmsg"));
                                flag = false;
                                return false;
                            }
                        }
                        var disable= $(el2).prop("disabled");
                        var visiable = $(el2).is(":hidden");
                        if ((require == 'true' || require == true) && !val&&!disable&&!visiable) {
                            bsAlert($(this).attr("errmsg"));
                            flag = false;
                            return false;
                        }
                        if (flag && name != undefined) {
                            re[name] = val;
                        }
                    }
                }
            });
            if(json){
                re = $.extend(json,re);
            }
            arr.push(re);
        })
    }
    return {
        arr: arr,
        flag: flag
    };
}
function getValidTableInputArrNew(id) {
    var flag = true;
    var arr=[];
    if($(id).find("tr").length){
        $(id).find("tr").each(function (index,el) {
            var re ={};
            var json = $(el).data("json");
            if (typeof (json) == "string") {
                json = json.replace(/'/g, '"');
                json = JSON.parse(json);
            }
            if($(el).data("_state")){
                re._state = $(el).data("_state");
            }
            if($(el).data("revision")||$(el).data("revision")==0){
                re.revision = $(el).data("revision");
            }
            if($(el).data("cd")&&$(el).data("cdname")){
                re[$(el).data("cdname")] = $(el).data("cd");
            }
            $(el).find("td").each(function (index,el2) {
                if (!$(el2).hasClass('btns')) {
                    var name = $(el2).data("name");
                    if(name){
                        var val = ($(el2).val()?$(el2).val():$(el2).html());
                        if($(el2).find("select").length){
                            val = $(el2).find("select").val();
                        }
                        if($(el2).find("input").length){
                            val = $(el2).find("input").val();
                        }
                        if(val){
                            val= val.trim();
                        }
                        if(val){
                            val= val.replace(/[\"\'\“\”\‘\’]/g, "");
                        }

                        var require = $(el2).attr("notempty");
                        var reg = $(el2).attr("reg");
                        //	正则验证
                        if (reg && val) {
                            reg = new RegExp(reg);
                            if (!reg.test(val)) {
                                bsAlert($(el2).attr("errmsg"));
                                flag = false;
                                return false;
                            }
                        }
                        var disable= $(el2).prop("disabled");
                        var visiable = $(el2).is(":hidden");
                        if ((require == 'true' || require == true) && !val&&!disable&&!visiable) {
                            bsAlert($(this).attr("errmsg"));
                            flag = false;
                            return false;
                        }
                        if (flag && name != undefined) {
                            re[name] = val;
                        }
                    }
                }
            });
            if(json){
                re = $.extend(json,re);
            }
            arr.push(re);
        })
    }
    return {
        arr: arr,
        flag: flag
    };
}

function openIframe(title,url){
    layer.open({
        type: 2,
        title: title,
        maxmin: true, //开启最大化最小化按钮
        area: ['600px','500px'],
        content: url
    })
}
//只能输入小于某个数的正整数，不包含0
function OnlyInputNumgtX(el, X) {
    el.value = el.value.replace(/[^\d]/g, ""); //清除"数字"以外的字符
    //清除“0”开头的字符
    if (el.value.indexOf("0") == 0) {
        el.value = el.value.replace(el.value.charAt(0), "");
    }
    if (el.value > X) {
        if (el.value > 9) {
            el.value = el.value.toString().substring(0, 1);//只保留第一个, 清除多余的
            el.value = parseInt(el.value);
        } else {
            el.value = 1;
        }

    }
}
//
function setFormData(formID, datas,disabled) {
    for (var i in datas) {
        if (i === "invtype") {
            if (datas[i] == 1) {
                datas[i] = '收货发票';
            }
            if (datas[i] == 2) {
                datas[i] = '退货发票';
            }
            if (datas[i] == 3) {
                datas[i] = '结算发票';
            }
        }
        if (i === "imageurl") {
            datas[i] = changeUrl(datas[i]);
            $("img[name='imageurl']").attr("src", datas[i]);//src(datas[i]);
        }
        if (i === 'invsts') {
            if (datas[i] == 0) {
                datas[i] = '初始状态';
            }
            if (datas[i] == 1) {
                datas[i] = '发票已核销';
            }
            if (datas[i] == 2) {
                datas[i] = '财务核销';
            }
        }
        if (i === 'invamt') {
            if (datas[i]) {
                datas[i] = parseFloat(datas[i]).toFixed(2);
            }
        }
        $(formID).find("input[name='" + i + "'][type='text'],input[name='" + i + "'][type='hidden'],select[name='" + i + "'],textarea[name='" + i + "']").val(datas[i]);
        if(disabled){
            $(formID).find("input[name='" + i + "'],select[name='" + i + "'],textarea[name='" + i + "']").prop("disabled",true);
        }
        if(!$(formID).find(".select2[name='" + i + "']").attr("selectId")){
            if($(formID).find(".select2[name='" + i + "']").length){
                $(formID).find(".select2[name='" + i + "']").select2().val(datas[i]).trigger("change");
            }
        }
        $(formID).find("h3[name='" + i + "'],div[name='" + i + "'],p[name='" + i + "'],span[name='" + i + "'],a[name='" + i + "']").html(datas[i]);
        $(formID).find("img[name='"+i+"']").attr("src",datas[i]);
		$(formID).find("input[type='radio'][name='"+i+"'][value='"+datas[i]+"']").prop('checked',true);
		$(formID).find("input[type='checkbox'][name='"+i+"'][value='"+datas[i]+"']").prop('checked',true);
		if($(formID).find("input[name='"+i+"']").attr("selectid") || $(formID).find("input[name='"+i+"']").attr("ts-selected")){
			$(formID).find("input[name='"+i+"']").attr("ts-selected",datas[i]);
			$(formID).find("input[name='"+i+"']").val(datas[i]);
			
		}
		
    }
}

function showRightProofInfo(nodes,data) {
	showRightProofInfoNew(nodes,data);
	return;
    var type;

    if(nodes){
        if(nodes.level==2){
            nodes = zTree.getNodeByParam("id",nodes.pid);
        }
        if(nodes.typename){
            if((nodes.typename).indexOf("营业执照")>=0){
                type=1;
            }
            if((nodes.typename).indexOf("经营许可证")>=0){
                type=2;
            }
            if((nodes.typename).indexOf("第一类医疗器械生产备案凭证")>=0||(nodes.typename).indexOf("第二类医疗器械经营备案凭证")>=0){
                type=3;
            }
            if((nodes.typename).indexOf("年度信用报告")>=0){
                type=4;
            }
            if((nodes.typename).indexOf("法人授权委托书")>=0){
                type=5;
            }
            if((nodes.typename).indexOf("注册证")>=0||(nodes.typename).indexOf("中华人民共和国医疗器械注册证")>=0){
                type=6;
            }if((nodes.typename).indexOf("消毒产品检测报告")>=0||(nodes.typename).indexOf("消毒产品(消杀类)检测报告")>=0){
                type=7;
            }if((nodes.typename).indexOf("商品备案凭证/备案信息表")>=0){
                type=8;
            }if((nodes.typename).indexOf("生产厂家证照")>=0){
                type=9;
            }if((nodes.typename).indexOf("代理企业证照")>=0){
                type=10;
            }if((nodes.typename).indexOf("产品授权代理书")>=0||(nodes.typename).indexOf("授权书")>=0){
                type=11;
            }if((nodes.typename).indexOf("产品说明书")>=0){
                type=12;
            }
            if((nodes.typename).indexOf("生产许可证")>=0){
                type=13;
            }
            if((nodes.typename).indexOf("消毒产品卫生安全评价报告")>=0||(nodes.typename).indexOf("卫生安全评价报告(消杀类)")>=0){
                type=14;
            }
            if((nodes.typename).indexOf("进口产品中英文授权代理书")>=0){
                type=15;
            }
            if((nodes.typename).indexOf("消毒产品卫生许可证")>=0||(nodes.typename).indexOf("消毒产品(消杀类)卫生许可证")>=0){
                type=16;
            }

            if((nodes.typename).indexOf("危险化学品经营许可证")>=0){
                type=17;
            }
            if((nodes.typename).indexOf("非药品类易制毒化学品经营备案证明")>=0){
                type=18;
            }
            if((nodes.typename).indexOf("全国工业产品生产许可证")>=0){
                type=19;
            }
            if((nodes.typename).indexOf("安全生产许可证")>=0){
                type=20;
            }
            if((nodes.typename).indexOf("医疗器械经营许可证")>=0){
                type=21;
            }
        }
    }
    var ScopeNums;
    if(nodes.highertypename=='供应商证照'&&type==21&&data){
        if(data.certscope){
            var sJson={};
            sJson.scompycd = data.scompycd;
            sJson.catgcodes = CertScopeAnalyse(data.certscope);
            if(sJson.catgcodes){
                ScopeNums = getDataWithDetail(sJson,'com.wx.masterData.masterQueryNamingsql.select_mtgoods_count');
                ScopeNums = ScopeNums[0].totalcount;
            }

        }

    }
    var shtml = '';
    shtml += (nodes.isupload == 0 ? '<span class="layui-badge layui-bg-yellow">待提交</span>' : '<span class="layui-badge layui-bg-green">已提交</span>');
    shtml += (nodes.status == 0 ? '<span class="layui-badge layui-bg-yellow">待审核</span>' : (nodes.status == 1 ? '<span class="layui-badge layui-bg-green">已通过</span>' : '<span class="layui-badge layui-bg-red" title="' + nodes.message + '">不通过</span> <span style="margin-left: 5px;">理由：' + nodes.message + '</span>'));
    var html='<input type="hidden" id="scompycd_copy" name="scompycd_copy">'+
        '<input type="hidden" id="certcd" name="certcd">'+
        '<input type="hidden" id="revision" name="revision">'+
        '<input type="hidden" id="imageurl" name="imageurl">'+
        '<input type="hidden" id="certrange" name="certrange">'+
        '<input type="hidden" id="typecd" name="typecd">'+
        '<input type="hidden" id="typename" name="typename">'+
        '<input type="hidden" id="certnogoodscd" name="certnogoodscd">' +
        '<input type="hidden" id="companyname" name="companyname">' +
        '<tr>'+
        '<th>状态：</th>'+
        '<td><span class="status">'+shtml+'</span>'+((nodes.highertypename=='供应商证照'&&type==21)?'<a '+(ScopeNums?' data-json="'+JSON.stringify(data).replace(/\"/g, "'")+'" onclick="loadScopeGoods(this)"':'')+' style="margin-left: 5px;" class="layui-btn layui-btn-xs layui-btn-default">范围关联商品数:(<span class="ScopeNums">'+ScopeNums+'</span>)</a>':'')+'</td>'+
        '<th>有效性：</th>'+
        '<td ><p name="yxx" ></p></td>'+
        '</tr>';
    if(type==1){
        html+='<tr><th>供应商</th>' +
            '<td><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>统一社会信用代码:</th>' +
            '<td><p name="certno"></p></td>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>类型:</th>' +
            '<td><p name="compytype"></p></td>' +
            '<th>住所:</th>' +
            '<td><p name="dwaddr"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>法定代表人:</th>' +
            '<td><p name="corporation"></p></td>' +
            '<th>注册资本:</th>' +
            '<td><p name="regcapital"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>成立日期:</th>' +
            '<td><p name="estdate"></p></td>' +
            '<th>营业期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>登记机关:</th>' +
            '<td><p name="lssuioffice"></p></td>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +

            '';
    }
    if(type==2){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>许可证编号:</th>' +
            '<td><p name="certno"></p></td>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>法定代表人:</th>' +
            '<td><p name="corporation"></p></td>' +
            '<th>经营方式:</th>' +
            '<td><p name="busstype"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业负责人:</th>' +
            '<td><p name="enterprin"></p></td>' +
            '<th>住所:</th>' +
            '<td><p name="dwaddr"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '<th>经营场所:</th>' +
            '<td><p name="bussspace"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>库房地址:</th>' +
            '<td><p name="whaddr"></p></td>' +
            '<th>发证部门:</th>' +
            '<td><p name="lssuioffice"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>有效期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            ''

    }
    if(type==3){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备案编号:</th>' +
            '<td><p name="certno"></p></td>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>法定代表人:</th>' +
            '<td><p name="corporation"></p></td>' +
            '<th>企业负责人:</th>' +
            '<td><p name="enterprin"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营方式:</th>' +
            '<td><p name="busstype"></p></td>' +
            '<th>住所:</th>' +
            '<td><p name="dwaddr"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营场所:</th>' +
            '<td><p name="bussspace"></p></td>' +
            '<th>库房地址:</th>' +
            '<td><p name="whaddr"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '<th>备案部门:</th>' +
            '<td><p name="regoffice"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备案日期:</th>' +
            '<td><p name="regdate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==4){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==5){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>被授权人名称:</th>' +
            '<td><p name="authorizeuser"></p></td>' +
            '<th>被授权人联系电话:</th>' +
            '<td><p name="authorizetel"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业固定电话:</th>' +
            '<td><p name="companytel"></p></td>' +
            '<th>授权期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            ''
    }
    if(type==6){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>注册证编号:</th>' +
            '<td><p name="certno"></p></td>' +
            '<th>注册人名称:</th>' +
            '<td><p name="corporation"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>注册人住所:</th>' +
            '<td><p name="dwaddr"></p></td>' +
            '<th>生产地址:</th>' +
            '<td><p name="proaddr"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>代理人名称:</th>' +
            '<td><p name="authorizeuser"></p></td>' +
            '<th>代理人住所:</th>' +
            '<td><p name="authorizeaddr"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname"></p></td>' +
            '<th>型号规格:</th>' +
            '<td><p name="prospec" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>结构及组成:</th>' +
            '<td><p name="structureform" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;" ></p></td>' +
            '<th>适合范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>附件:</th>' +
            '<td><p name="attachment" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '<th>其他内容:</th>' +
            '<td><p name="others" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>审批部门:</th>' +
            '<td><p name="lssuioffice"></p></td>' +
            '<th>批准日期:</th>' +
            '<td><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>有效期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '<th>备注:</th>' +
            '<td><p name="remarks"></p></td>' +
            '</tr>' +
            ''
    }
    if(type==7){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>样品名称:</th>' +
            '<td><p name="flname"></p></td>' +
            '<th>检测日期:</th>' +
            '<td><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==8){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>商品名称:</th>' +
            '<td><p name="flname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备案号:</th>' +
            '<td><p name="certno"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>发证机关:</th>' +
            '<td><p name="lssuioffice"></p></td>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==9){
        html+='<tr>' +
            '<th  style="width: 120px;">供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 120px;">生产厂商名称:</th>' +
            '<td colspan="3"><p name="companyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==10){
        html+='<tr>' +
            '<th  style="width: 120px;">供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">代理企业名称:</th>' +
            '<td colspan="3"><p name="companyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==11){
        html+='<tr>' +
            '<th  style="width: 120px;">供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 120px;">被授权企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '<th style="width: 120px;">授权企业名称:</th>' +
            '<td><p name="authorizedcompy"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 150px;">被授权企业授权书等级:</th>' +
            '<td><p name="authorizelevel"></p></td>' +
            '<th>授权期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>授权产品范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '<th>备注:</th>' +
            '<td ><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            ''
    }
    if(type==12){
        html+='<tr>' +
            '<th  style="width: 120px;">供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">产品名称:</th>' +
            '<td colspan="3"><p name="flname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==13){
        html+='<tr>' +
            '<th  style="width: 120px;">供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">生产厂商名称:</th>' +
            '<td colspan="3"><p name="companyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 120px;">生产范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '<tr>' +
            '<th style="width: 120px;">备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height: 200px;overflow-y: scroll;word-wrap:break-word;"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==14){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>样品名称:</th>' +
            '<td><p name="flname"></p></td>' +
            '<th>检测日期:</th>' +
            '<td><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==15){
        html+='<tr>' +
            '<th  style="width: 120px;">供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 120px;">被授权企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '<th style="width: 120px;">授权企业名称:</th>' +
            '<td><p name="authorizedcompy"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 150px;">被授权企业授权书等级:</th>' +
            '<td><p name="authorizelevel"></p></td>' +
            '<th>授权期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>授权产品范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '<th>备注:</th>' +
            '<td ><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            ''
    }
    if(type==16){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>生产企业单位名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '<th>生产类别:</th>' +
            '<td><p name="compytype"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==17){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>许可范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==18){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>主要品种、销售量:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>主要流向:</th>' +
            '<td colspan="3"><p name="others"></p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==19){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td><p name="scompyname"></p></td>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>生产厂商名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==20){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td><p name="scompyname"></p></td>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>生产企业名称:</th>' +
            '<td><p name="companyname"></p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;"></p> ~ <p name="certterm" style="display: inline-block;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>许可范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '';
    }
    if(type==21){
        html+='<tr>'+
            '<th>供应商:</th>' +
            '<td><p name="scompyname"></p></td>' +
            '<th>注册人名称:</th>' +
            '<td><p name="corporation"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>注册人住所:</th>' +
            '<td><p name="dwaddr"></p></td>' +
            '<th>生产地址:</th>' +
            '<td><p ></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>代理人名称:</th>' +
            '<td><p name="authorizeuser"></p></td>' +
            '<th>代理人住所:</th>' +
            '<td><p name="authorizeaddr"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname"></p></td>' +
            '<th>型号规格:</th>' +
            '<td><p name="prospec" style="max-width:220px;max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th width="94">结构及组成:</th>' +
            '<td colspan="3"><p name="structureform" style="max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;" ></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>适合范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>附件:</th>' +
            '<td><p name="attachment" style="max-height:200px;overflow-y:auto;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>' +
            '<th>其他内容:</th>' +
            '<td><p name="others" style="max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td><p name="remarks"></p></td>' +
            '</tr>' +
            ''
    }
    if(!type){
        html+='<tr>' +
            '<th  style="width: 120px;">供应商:</th>' +
            '<td colspan="3"><p name="scompyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">企业名称:</th>' +
            '<td colspan="3"><p name="companyname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">证照编码:</th>' +
            '<td colspan="3"><p name="certno"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">证照名称:</th>' +
            '<td colspan="3"><p name="certname"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证机关:</th>' +
            '<td colspan="3"><p name="lssuioffice"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate"></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  style="width: 120px;">证照期限:</th>' +
            '<td> <p><span name="certtermstart"></span>  ~~  <span name="certterm"></span></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;"></p></td>'+
            '</tr>' +
            '';
    }
    html+='<tr>' +
        '<th>扩展属性：</th>'+
        '<td colspan="3"><p id="extInfo"></p></td>'+
        '</tr>';
    if($("#proofForms").length){
        $("#proofForms").html(html);
    }else{
        $("#proofForm").html(html);
    }

}


//证照需要，根据类型展示页面
//type==1营业执照，type==2 医疗器械经营许可证,type==3第二类医疗器械经营备案凭证
//type==4 年度信用报告,type==5 法人授权委托书（包含身份证复印件）
//type==6 中华人民共和国医疗器械注册证,type==7 消毒产品检测报告
//type==8 备案凭证/备案信息表,
//type==9 生产厂家证照
//type==10 代理企业证照
//type==11 授权书
//type==12 产品说明书
//type==13 医药批准文书
//type==14 药品经营许可证
//type==15 药品GSP证书
function showProof(nodes) {
	showProofNew(nodes);
    return;
    var type;
    var html='';
    if(nodes){
        if(nodes.level==2){
            nodes = zTree.getNodeByParam("id",nodes.pid);
        }
        if(nodes.typename){
            if((nodes.typename).indexOf("营业执照")>=0){
                type=1;
            }
            if((nodes.typename).indexOf("经营许可证")>=0){
                type=2;
            }
            if((nodes.typename).indexOf("第一类医疗器械生产备案凭证")>=0||(nodes.typename).indexOf("第二类医疗器械经营备案凭证")>=0){
                type=3;
            }
            if((nodes.typename).indexOf("年度信用报告")>=0){
                type=4;
            }
            if((nodes.typename).indexOf("法人授权委托书")>=0){
                type=5;
            }
            if((nodes.typename).indexOf("注册证")>=0||(nodes.typename).indexOf("中华人民共和国医疗器械注册证")>=0){
                type=6;
            }if((nodes.typename).indexOf("消毒产品检测报告")>=0||(nodes.typename).indexOf("消毒产品(消杀类)检测报告")>=0){
                type=7;
            }if((nodes.typename).indexOf("商品备案凭证/备案信息表")>=0){
                type=8;
            }if((nodes.typename).indexOf("生产厂家证照")>=0){
                type=9;
            }if((nodes.typename).indexOf("代理企业证照")>=0){
                type=10;
            }if((nodes.typename).indexOf("产品授权代理书")>=0||(nodes.typename).indexOf("授权书")>=0){
                type=11;
            }if((nodes.typename).indexOf("产品说明书")>=0){
                type=12;
            }
            if((nodes.typename).indexOf("生产许可证")>=0){
                type=13;
            }
            if((nodes.typename).indexOf("消毒产品卫生安全评价报告")>=0||(nodes.typename).indexOf("卫生安全评价报告(消杀类)")>=0){
                type=14;
            }
            if((nodes.typename).indexOf("进口产品中英文授权代理书")>=0){
                type=15;
            }
            if((nodes.typename).indexOf("消毒产品卫生许可证")>=0||(nodes.typename).indexOf("消毒产品(消杀类)卫生许可证")>=0){
                type=16;
            }
            if((nodes.typename).indexOf("危险化学品经营许可证")>=0){
                type=17;
            }
            if((nodes.typename).indexOf("非药品类易制毒化学品经营备案证明")>=0){
                type=18;
            }
            if((nodes.typename).indexOf("全国工业产品生产许可证")>=0){
                type=19;
            }
            if((nodes.typename).indexOf("安全生产许可证")>=0){
                type=20;
            }
            if((nodes.typename).indexOf("医疗器械经营许可证")>=0){
                type=21;
            }
        }
    }
    if(type==1){
        html+='<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','统一社会信用代码')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','企业名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('compytype','类型')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('dwaddr','住所')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('corporation','法定代表人')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('regcapital','注册资本')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('estdate','成立日期','laydate',null,'estdate')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','营业期限','laydate',null,null,'empty')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" >' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：营业期限不填为长期有效</span>' +
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 5px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuioffice','登记机关')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','经营范围') +
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>';
    }
    if(type==2){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','许可证编号')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','企业名称')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('corporation','法定代表人')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('busstype','经营方式')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('enterprin','企业负责人')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('dwaddr','住所')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','经营范围') +
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('bussspace','经营场所')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('whaddr','库房地址')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuioffice','发证部门')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            // getInputHtml('certterm','有效期至','laydate')+
            getInputRangeHtml('certtermstart','certterm','有效期限','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>'+
            '';

    }
    if(type==3){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','备案编号')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','企业名称')+
            '</div>'+
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('corporation','法定代表人')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('enterprin','企业负责人')+
            '</div>'+
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('busstype','经营方式')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('dwaddr','住所')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('bussspace','经营场所')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('whaddr','库房地址')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','经营范围')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('regoffice','备案部门')+
            '</div>'+
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('regdate','备案日期','laydate')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>'+
            '';
    }
    if(type==4){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs12">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>';
    }
    if(type==5){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('authorizeuser','被授权人姓名')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('authorizetel','被授权人电话')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companytel','企业固定电话')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','授权期限','laydate',null,null,'empty')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>'
    }
    if(type==6){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','注册证编号')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('corporation','注册人名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('dwaddr','注册人住所')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('proaddr','生产地址')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('authorizeuser','代理人名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('authorizeaddr','代理人住所')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('flname','产品名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('prospec','型号规格')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('structureform','结构及组成')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','适合范围')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('attachment','附件',null,null,'请输入附件名字并上传附件图片')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('others','其他内容')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuioffice','审批部门')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','批准日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certterm','有效期至','laydate')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty')+
            '</div>' +
            '</div>' +
            ''
    }
    if(type==7){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('flname','样品名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','检测日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    if(type==8){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('flname','商品名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','备案号')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuioffice','发证机关')+
            '</div>' +

            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    if(type==9){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','生产厂商名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','效期','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：效期期限不填为长期有效</span>' +
            '</div>' +
            '<div class="layui-col-xs6">' +

            '</div>' +
            '</div>' +
            '</div>' +

            '<div class="layui-row" style="margin-top: 10px;">' +

            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    if(type==10){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','代理企业名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    if(type==11){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','被授权企业名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('authorizedcompy','授权企业名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            '<div class="layui-form-item">' +
            '<span class="layui-form-label"><span class="text-red">*</span>被授权企业授权书等级</span>' +
            '<div class="layui-input-block">' +
            '<select name="authorizelevel" class="layui-select" notempty="true" errmsg="请选择授权等级" >' +
            '<option value="">--选择授权等级--</option>' +
            '<option value="1">1级授权</option>' +
            '<option value="2">2级授权</option>' +
            '<option value="3">3级授权</option>' +
            '</select>' +
            '</div>' +
            '</div>'+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','授权期限','laydate',null,null,'empty')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','授权产品范围')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +

            '</div>' +
            ''
    }
    if(type==12){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('flname','产品名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    if(type==13){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','生产厂商名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','效期','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：效期期限不填为长期有效</span>' +
            '</div>' +
            '<div class="layui-col-xs6">' +

            '</div>' +
            '</div>' +
            '</div>' +

            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','生产范围')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    if(type==14){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>'+
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('flname','样品名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','检测日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    if(type==15){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','被授权企业名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('authorizedcompy','授权企业名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            '<div class="layui-form-item">' +
            '<span class="layui-form-label" style="width: 150px;"><span class="text-red">*</span>被授权企业授权书等级</span>' +
            '<div class="layui-input-block" style="margin-left: 150px;">' +
            '<select name="authorizelevel" class="layui-select" notempty="true" errmsg="请选择授权等级" >' +
            '<option value="">--选择授权等级--</option>' +
            '<option value="1">1级授权</option>' +
            '<option value="2">2级授权</option>' +
            '<option value="3">3级授权</option>' +
            '</select>' +
            '</div>' +
            '</div>'+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','授权期限','laydate',null,null,'empty')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','授权产品范围')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +

            '</div>' +
            ''
    }
    if(type==16){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','生产企业单位名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('compytype','生产类别')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','效期','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +

            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            ''
    }
    if(type==17){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','企业名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','证书编号')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','效期','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +

            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','许可范围') +
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            ''
    }
    if(type==18){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','企业名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','证书编号')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','效期','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +

            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','经营品种、销售量') +
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('others','主要流向') +
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            ''
    }
    if(type==19){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('flname','产品名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','生产厂商名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','证书编号')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','效期','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +

            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +

            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            ''
    }
    if(type==20){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('flname','产品名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('companyname','生产企业名称')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','证书编号')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','效期','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row">' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：授权期限不填为长期有效</span>' +
            '</div>' +
            '<div class="layui-col-xs6" style="height: 1px;">' +
            '</div>' +

            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','许可范围') +
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            ''
    }
    if(type==21){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
        '<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs6">' +
        getInputHtml('corporation','注册人名称')+
        '</div>' +
        '<div class="layui-col-xs6">' +
        getInputHtml('dwaddr','注册人住所')+
        '</div>' +
        '</div>' +
        '<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs12">' +
        getInputHtml('proaddr','生产地址')+
        '</div>' +
        '</div>' +
        '<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs6">' +
        getInputHtml('authorizeuser','代理人名称')+
        '</div>' +
        '<div class="layui-col-xs6">' +
        getInputHtml('authorizeaddr','代理人住所')+
        '</div>' +
        '</div>' +
        '<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs6">' +
        getInputHtml('flname','产品名称')+
        '</div>' +
        '<div class="layui-col-xs6">' +
        getInputHtml('prospec','型号规格')+
        '</div>' +
        '</div>' +
        '<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs6">' +
        getTextareaHtml('structureform','结构及组成')+
        '</div>' +
        '<div class="layui-col-xs6">' +
        getTextareaHtml('certscope','适用范围')+
        '</div>' +
        '</div>' +
        '<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs6">' +
        getTextareaHtml('attachment','附件',null,null,'请输入附件名字并上传附件图片')+
        '</div>' +
        '<div class="layui-col-xs6">' +
        getTextareaHtml('others','其他内容')+
        '</div>' +
        '</div>' +
       
        '<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs12">' +
        getTextareaHtml('remarks','备注',null,'empty')+
        '</div>' +
        '</div>' +
        ''
    }
    if(!type){
        html+='<div class="layui-row">' +
            '<div class="layui-col-xs12">' +
            getSelectHtml('scompycd','供应商','com.wx.masterData.companyQueryNamingsql.select_companyCom_page','changeScompy(this)','scompycd')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certno','证照编码',null,null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('certname','证照名称')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px">' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuioffice','发证机关')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            getInputHtml('lssuidate','发证日期','laydate')+
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px">' +
            '<div class="layui-col-xs6">' +
            getInputRangeHtml('certtermstart','certterm','证照期限','laydate',null,null,'empty')+
            '</div>' +
            '<div class="layui-col-xs6">' +
            '<span class="text-red">注：证照期限不填为长期有效</span>' +
            '</div>' +
            '</div>' +
            '<div class="layui-row" style="margin-top: 10px;">' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('certscope','经营范围',null,'empty') +
            '</div>' +
            '<div class="layui-col-xs6">' +
            getTextareaHtml('remarks','备注',null,'empty') +
            '</div>' +
            '</div>' +
            '';
    }
    $("#proof_special").html(html);
}



function getInputHtml(name,txt,addClass,disabled,id,empty) {
    var html='';
    // if(txt!=='代理企业名称'&&txt!='被授权企业名称'&&txt!='生产企业单位名称'&&txt!='生产厂商名称'&&txt!='生产企业名称'){
    //     html+='<div class="layui-form-item">' +
    //         '<span class="layui-form-label" style="width: 150px;">'+(empty?'':'<span class="text-red">*</span>')+txt+':</span>' +
    //         '<div class="layui-input-block" style="margin-left: 150px;">' +
    //         '<input data-txt="'+txt+'" '+(id?' id="'+id+'"':'')+' type="text" '+(disabled?'disabled="disabled"':'')+' class="layui-input '+(addClass?addClass:'')+'" placeholder="请输入'+txt+'" name="'+name+'" notempty="true" errmsg="'+txt+'不能为空">' +
    //         (name=='companyname'?'<span style="position: absolute;right: 0;top: 1px;padding: 0 10px;border-left: 1px solid #d2d6de;border-right: 1px solid #d2d6de;background-color: #fff" class="input-group-addon" onclick="ChooseCompy()">选择</span>':'')+
    //         '</div>' +
    //         '</div>';
    // }else{
        html+='<div class="layui-form-item" >' +
            '<span class="layui-form-label" style="width: 150px;">'+(empty?'':'<span class="text-red">*</span>')+txt+':</span>' +
            '<div class="layui-input-block" style="margin-left: 150px;">' +
            '<input data-txt="'+txt+'" '+(id?' id="'+id+'"':'')+' type="text" '+(disabled?'disabled="disabled"':'')+' class="layui-input '+(addClass?addClass:'')+'" placeholder="请输入'+txt+'" name="'+name+'" '+(empty?'':' notempty="true" errmsg="'+txt+'不能为空"')+'>' +
            '</div>' +
            '</div>';
    // }

    return html;
}

function getSelectHtml(name,txt,selectId,onchangeFn,id) {
    var html='';
    html+='<div class="layui-form-item">' +
        '<span class="layui-form-label" style="width: 150px;"><span class="text-red">*</span>'+txt+':</span>' +
        '<div class="layui-input-block" style="margin-left: 150px;">' +
        '<input data-txt="'+txt+'" '+(selectId?' selectId="'+selectId+'"':'')+(onchangeFn?' onchange="'+onchangeFn+'"':'')+(id?' id="'+id+'"':'')+' class="layui-input" placeholder="请选择'+txt+'" readonly="readonly" name="'+name+'">' +
        '</div>' +
        '</div>';
    return html;
}

function getInputRangeHtml(name1,name2,txt,addClass,id1,id2,empty) {
    var html=''
    html+='<div class="layui-form-item">' +
        '<div class="layui-inline">' +
        '<span class="layui-form-label">'+(empty?'':'<span class="text-red">*</span>')+txt+':</span>' +
        '<div class="layui-input-inline" style="width: 150px;">' +
        '<input autocomplete="off" data-txt="'+txt+'" '+(id1?' id="'+id1+'"':'')+' type="text" class="layui-input '+(addClass?addClass:'')+'" placeholder="请输入'+txt+'起始" name="'+name1+'" '+(empty?'':' notempty="true" errmsg="'+txt+'不能为空"')+'>' +
        '</div>' +
        '<div class="layui-form-mid" style="margin-left: 5px;">-</div>' +
        '<div class="layui-input-inline" style="width: 150px;">' +
        '<input autocomplete="off" data-txt="'+txt+'" '+(id2?' id="'+id2+'"':'')+' type="text" class="layui-input '+(addClass?addClass:'')+'" placeholder="请输入'+txt+'截止" name="'+name2+'" '+(empty?'':' notempty="true" errmsg="'+txt+'不能为空"')+'>' +
        '</div>' +
        '</div>' +
        '</div>';
    return html;
}
function getTextareaHtml(name,txt,addClass,empty,placeholder) {
    var html=''
    html+='<div class="layui-form-item layui-form-text">' +
        '<span class="layui-form-label" >'+(empty?'':'<span class="text-red">*</span>')+''+txt+':</span>' +
        '<div class="layui-input-block">' +
        '<textarea data-txt="'+txt+'" type="text" class="layui-textarea '+(addClass?addClass:'')+'" placeholder="'+(placeholder?placeholder:'请输入'+txt+'')+'" name="'+name+'" '+(empty?'':' notempty="true" errmsg="'+txt+'不能为空"')+'></textarea>' +
        '</div>' +
        '</div>';
    return html;
}

function seeImg(certcd,flag){
    var json = getDataWithDetail({ certcd:certcd},"select_cert_imgs",null,'/OnLineDevelopProFiles/vsspd_hc/proof/proof_search_goods1');
    console.log(json);
    if(flag){
        var photoJson = {
            title: "", //相册标题
            id: "", //相册id
            start: 0, //初始显示的图片序号，默认0
            data: []
        }
        if(json){
            layer.open({
                type: 2,
                title:'图片预览',
                area: ['500px', '400px'],
                skin: 'layer-ext-moon', //样式类名
                anim: 2,
                scrollbar:false,
                shadeClose: true, //开启遮罩关闭
                content: locationUrl + '/OnLineDevelopProFiles/vsspd_hc/proof/seach_lic_Img.jsp',
                success: function(layero, index){
                    layer.iframeAuto(index);
                    var iframe = window[layero.find('iframe')[0]['name']];
                    //调用子页面的全局函数
                    iframe.child(certcd);
                }
            });

        }
    }else{
        return json;
    }
}


//获取记录数
function getDatacount(reqMap, selectId, selectCountId) {
    var count = 0;
    var sendData = {
        "reqMap": reqMap,
        "pageSize": 1,
        "pageStartIndex": 0,
        "callEntry": "main.getDataWithPage",
        "dbName": "default",
        "selectId": selectId,
        "selectCountId": selectCountId
    };
    $.ajax({
        url: "com.wx.utils.publicMemer.component.queryNamedPageToEosMapbiz.biz.ext",
        data: JSON.stringify(sendData),
        type: "post",
        cache: false,
        async: false,
        contentType: 'application/json;charset=UTF-8',
        success: function (result) {
            if (result.exception != null) {
                bsAlert(result.exception.message);
            } else {
                count = result.totalCount;
            }
        }
    });
    return count;
}
//验证邮政编码是否合法
function isPostCode(postcode) {
    var a = true;
    if(postcode){
        var testUrl = /^[1-9]\d{5}$/;
        if (!testUrl.test(postcode)) {
            a = false;
        }
    }
    return a;
}
//验证联系电话是否合法
function isTel(tel) {
    var a = true;
    if(tel){
        var testUrl = /(^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+([0-9]{1,4})?$)|(^(1[3587]\d{9})$)/;
        if (!testUrl.test(tel)) {
            a = false;
        }
    }
    return a;
}
function addOrg(sendData) {
    var flag = false;

    $.ajax({
        url: "org.gocom.components.coframe.org.organization.addOrg.biz.ext",
        type: 'POST',
        data: JSON.stringify(sendData),
        cache: false,
        async: false,
        contentType: 'application/json;charset=UTF-8',
        success: function(text) {
            if (text.exception != null) {
                deAlert(text.exception.message);
            } else {
                var response = text.response || {};
                if (response) {
                    flag = response.flag;
                    if (response.flag == true) {

                    } else {
                        bsAlert(response.message);
                    }
                } else {
                    bsAlert("添加机构失败，请联系管理员");
                }
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            bsAlert(jqXHR.responseText);
        }
    });
    return flag;
}
function updateOrg(sendData){
    var flag = false;
    $.ajax({
        url: "org.gocom.components.coframe.org.organization.updateOrg.biz.ext",
        type: 'POST',
        data: JSON.stringify(sendData),
        cache: false,
        async:false,
        contentType:'application/json;charset=UTF-8',
        success: function (text) {
            if (text.exception != null) {
                deAlert(text.exception.message);
            } else{
                var response = text.response;
                flag = response.flag;
                if(response.flag==true){

                }else{
                    bsAlert(response.message);
                }
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            bsAlert(jqXHR.responseText);
        }
    });
    return flag;
}
function hideInputBox(str) {
    var arr =[];
    if(str){
        arr = str.split(',');
    }
    if(arr.length){
        $.each(arr,function (index,val) {
            $(".searchArea,.searchWrap").find('input[name='+val+'],select[name='+val+']').parent().parent().hide();
        })
    }
}
function showInputBox(str) {
    var arr =[];
    if(str){
        arr = str.split(',');
    }
    if(arr.length){
        $.each(arr,function (index,val) {
            $(".searchArea,.searchWrap").find('input[name='+val+'],select[name='+val+']').parent().parent().show();
        })
    }
}

function clearInputBox(str) {
    var arr =[];
    if(str){
        arr = str.split(',');
    }
    if(arr.length){
        $.each(arr,function (index,val) {
            $(".searchArea,.searchWrap").find('input[name='+val+'],select[name='+val+']').val("");
        })
    }
}
//加载供应商下拉框
function loadSelectScompy(ID) {
    // $(".select2").select2();
    var html = "<option value=''>--选择供应商--</option>";
    var json = getDataWithDetail({}, 'com.wx.masterData.masterQueryNamingsql.select_order_scompy');
    if (json) {
        if (json.length) {
            for (var i = 0; i < json.length; i++) {
                html += '<option data-companycd="'+json[i].companycd+'" value="' + json[i].scompyname + '">' + json[i].scompyname + '</option>';
            }
        }
    }
    $(ID).html(html);
}
//加载医院下拉框
function loadSelectCcompy(ID) {
    // $(".select2").select2();
    var html = "<option value=''>--选择医院--</option>";
    var json = getDataWithDetail({}, 'com.wx.masterData.masterQueryNamingsql.select_order_ccompy');
    if (json) {
        if (json.length) {
            for (var i = 0; i < json.length; i++) {
                html += '<option data-companycd="'+json[i].companycd+'" value="' + json[i].ccompyname + '">' + json[i].ccompyname + '</option>';
            }
        }
    }
    $(ID).html(html);
}
//医院加载医院列表
function loadSelectHospital(ID,companycds){
	// $(".select2").select2();
	var html = "<option value=''>选择医院</option>";
	var json = getDataWithDetail({companycd:companycds}, 'com.wx.masterData.setQueryNamingsql.select_hospital_list');
	if (json) {
		if (json.length) {
			for (var i = 0; i < json.length; i++) {
				html += '<option data-companycd="'+json[i].companycd+'" value="' + json[i].companyname + '">' + json[i].companyname + '</option>';
			}
		}
	}
	$(ID).html(html);
}
//供应商加载全部医院列表
function loadSelectHospitalS(ID,companycds){
	// $(".select2").select2();
	var html = "<option value=''>选择医院</option>";
	var json = getDataWithDetail({companycd:companycds}, 'com.wx.masterData.setQueryNamingsql.select_shospital_list');
	if (json) {
		if (json.length) {
			for (var i = 0; i < json.length; i++) {
				html += '<option data-companycd="'+json[i].companycd+'" value="' + json[i].companyname + '">' + json[i].companyname + '</option>';
			}
		}
	}
	$(ID).html(html);
}
//运营商加载全部医院列表
function loadSelectHospitalY(ID){
	// $(".select2").select2();
	var html = "<option value=''>选择医院</option>";
	var json = getDataWithDetail({}, 'com.wx.masterData.setQueryNamingsql.select_yhospital_list');
	if (json) {
		if (json.length) {
			for (var i = 0; i < json.length; i++) {
				html += '<option data-companycd="'+json[i].companycd+'" value="' + json[i].companyname + '">' + json[i].companyname + '</option>';
			}
		}
	}
	$(ID).html(html);
}
function lookGoods(goodscd) {
    if(goodscd){
        mini.open({
        url:contextPath+ "/OnLineDevelopProFiles/" + getProjectName()  + "/ins/order/l_images_list.jsp",
        showMaxButton: false,
        showModal: true,
        title: "商品图片",
        width: "970px",
        height: "560px",
        onload: function () {
            var iframe = this.getIFrameEl();
            iframe.contentWindow.setData({artiid:goodscd});
        },
        ondestroy: function (action) {                    
            if(action=="ok"){
                var iframe = this.getIFrameEl();
            }
            if(action=="close"){
            }
        }
    });
    }
}

function lookCompy(companycd) {
    if(companycd){
        var index = layer.open({
            type:2,
            title:'企业详情',
            maxmin:true,
            area:['820px','500px'],
            content:locationUrl+'/masterData/htmls/company/company_show.html?companycd='+companycd,
            btn:['确定','取消'],
            yes:function (index,el) {
                layer.close(index);
            }
        });
    }
}


// 复制一个对象
function cloneObj(obj) {
    function Clone() { }
    Clone.prototype = obj;
    var o = new Clone();
    for (var a in o) {
        if (typeof o[a] == "object") {
            o[a] = cloneObj(o[a]);
        }
    }
    return o;
}

function getImgWord(imgname,callback) {
    var str="";
    var sendData = {
        imgname:imgname
    };
    var index = layer.load(0, {shade: false});
    $.ajax({
        url: "com.wx.baidu.newcomponent.img2strbiz.biz.ext",
        data: JSON.stringify(sendData),
        type: "post",
        async: callback ? true : false,
        contentType: 'application/json;charset=UTF-8',
        success: function (result) {
            layer.close(index);
			console.log(result);
            result.str = (result.str).replace(/\n/g,'<br/>');
            str = result.str;
            if (callback) callback(result.str)
        }
    });
    return str;
}
function getImgWords(imgname,callback) {
    var str="";
    var params = {
        imgname:imgname,
        path:locationUrl
    };
    console.log(params);
    var index = layer.load(0, {shade: false});
    $.ajax({
        url:"http://118.25.143.88/wxwechate/baidu/dojsonp.jsp",
        dataType:"jsonp",
        jsonp:"callback",
        data:{biz:"com.wx.baidu.newcomponent.ims2strbiz",param:JSON.stringify(params)},
        async: callback ? true : false,
        success: function (result) {
            layer.close(index);
            console.log(result);
            result.dadtas.data = (result.dadtas.data).replace(/\n/g,'<br/>');
            str = result.dadtas.data;
            if (callback) callback(result.dadtas.data)
        }
    });
    return str;
}
function ReportServer(cptname,data) {
    if(cptname){
        var url =locationUrl+'/ReportServer?reportlet='+cptname+'.cpt';
        if(data){
            $.each(data,function (index,val) {
                val = val?$.trim(val):val;
                if(val){
                    url=url+'&'+index+'='+val;
                }
            })
        }

        layer.open({
            type: 2,
            title: '报表预览',
            shadeClose: true,
            shade: 0.8,
            maxmin: true, //开启最大化最小化按钮
            area: ['90%', '90%'],
            content: url
        });
        // var OpenWindow=window.open(url, "报表预览", 'menubar=no,fullscreen=1,toolbar=no,resizable=no,location=no,status=no');
        // OpenWindow.document.close();
    }
}

function showProofNew(nodes) {
    $("#proof_special").html("");
	 var html='<div class="layui-row" style="margin-top: 10px;">' +
        '<div class="layui-col-xs12"><div class="layui-form-item"><span class="layui-form-label" style="width: 150px;"><span class="text-red">*</span>供应商:</span><div class="layui-input-block" style="margin-left: 150px;"><input data-txt="供应商" type="text" selectid="select_sup" onchange="changeScompy(this)" id="scompycd" class="layui-input" placeholder="请选择供应商" readonly="readonly" name="scompycd"></div></div></div>' +
        '</div><div class="layui-row" >';
    var json=getDataWithDetail(nodes,'select_typeext_lists',null,'/OnLineDevelopProFiles/vsspd_hc/proof/goods_proof');

	$.each(json,function (index,val) {
		console.log(val)
        var h="";
        if(val.inputtype==='文本框'){
            h='<div class="layui-form-item"><span class="layui-form-label" >'+((val.isrequired=='1')?'<span class="text-red">*</span>':'')+val.displayname+'</span>' +
            '<div class="layui-input-block">' +
            '<input  data-txt="'+val.displayname+'" autocomplete="off" placeholder="请输入'+val.displayname+'" name="'+val.fieldname+'" value="'+(val.defaultvalue||'')+'" type="text" class="layui-input '+(val.classname||'')+'" '+
                ((val.isrequired=='1')?'notempty="true" errmsg="'+val.displayname+'不能为空" ':'')
                +'/></div></div>';
        }
        else if(val.inputtype==='文本域'){
        h='<div class="layui-form-item layui-form-text">' +
            '<span class="layui-form-label">'+((val.isrequired=='1')?'<span class="text-red">*</span>':'')+val.displayname+'</span>' +
            '<div class="layui-input-block">' +
            '<textarea  data-txt="'+val.displayname+'" autocomplete="off" placeholder="请输入'+val.displayname+'"   class="layui-textarea '+(val.classname||'')+'" name="'+val.fieldname+'" '+((val.isrequired=='1')?'notempty="true" errmsg="'+val.displayname+'不能为空" ':'')+'></textarea>'+
            '</div></div>';
        }
        else if(val.inputtype==='下拉框'){
            h='<div class="layui-form-item"><label class="layui-form-label">'+((val.isrequired=='1')?'<span class="text-red">*</span>':'')+val.displayname+'</label>' +
                '<div class="layui-input-block">' +
                '<select  data-txt="'+val.fieldname+'" class="layui-select '+val.classname+'" name="'+val.fieldname+'" '+((val.isrequired=='1')?'notempty="true" errmsg="'+val.displayname+'不能为空" ':'')+'>'+val.remarks+'</select>'+
            '</div></div>';
        }
        else if(val.inputtype==='html自定义'){
            h=val.remarks;
        }else if(val.inputtype ==='日期范围'){
			h = getInputRangeHtml(val.fieldname,val.fieldname2,val.displayname,'laydate',null,null,(val.isrequired=='0'?'empty':null))
		}
	
	   if(val.ocrow===1||val.ocrow === '1'){
		   html+='<div class="layui-col-xs12"  style="margin-top: 10px;">'+h+'</div>'
	   }else{
		   html+='<div class="layui-col-xs6"  style="margin-top: 10px;">' +
			   h+
			   '</div>';
		  
		
	   }
	   	if(val.tips){
			html+='<div   class="layui-col-'+((val.ocrow=='1')?'xs12':'xs6')+'" '+(val.ocrow=='1'?'':" style='height:31px;margin-top:10px;'")+'><span class="text-red" '+(val.direction=='靠左'?" style='float:left'":" style='float:right'")+'>'+val.tips+'</span></div>'
			
			
		}

       
    });
    html+='</div>';
	
    
    
    $("#proof_special").html(html);
}

function showRightProofInfoNew(nodes) {
    if($("#proofForms").length){
        $("#proofForms").html("");
    }else{
        $("#proofForm").html("");
    }
    var html="";
    var json=getDataWithDetail(nodes,'select_typeext_lists',null,'/OnLineDevelopProFiles/vsspd_hc/proof/goods_proof');
    var shtml = '';
    shtml += (nodes.isupload == 0 ? '<span class="layui-badge layui-bg-yellow">待提交</span>' : '<span class="layui-badge layui-bg-green">已提交</span>');
    shtml += (nodes.status == 0 ? '<span class="layui-badge layui-bg-yellow">待审核</span>' : (nodes.status == 1 ? '<span class="layui-badge layui-bg-green">已通过</span>' : '<span class="layui-badge layui-bg-red" title="' + nodes.message + '">不通过</span> <span style="margin-left: 5px;">理由：' + nodes.message + '</span>'));
    html+='<tr>'+
        '<th>状态：</th>'+
        '<td><p class="status">'+shtml+'</p></td>'+
        '<th>有效性：</th>'+
        '<td ><p name="yxx"></p></td>'+
        '</tr>'+
		'<tr><th>供应商:</th><td><p name="scompyname"></p></td></tr>';
    html+='<tr>';
    $.each(json,function (index,val) {
        if(val.fieldname=='scompycd'){
            val.extvalue = getDataWithDetail({companycd:val.extvalue},'select_compyname_bycd',null,'/OnLineDevelopProFiles/vsspd_hc/proof/goods_proof')[0].supplyname;
        }
		console.log(val)
        html+='<th>'+val.displayname+':</th><td><p name='+val.fieldname+'>'+val.extvalue+'</p>'+(val.fieldname2?'<p name='+val.fieldname2+'>'+val.extvalue+'</p>':'')+'</td>';
        if((index+1)%2==0){
            html+='</tr><tr>'
        }
    });
    html+='</tr>';
    if($("#proofForms").length){
        $("#proofForms").html(html);
    }else{
        $("#proofForm").html(html);
    }

}

function initLaydate () {
    layui.use(['laydate','jquery'],function () {
        var $ = layui.jquery;
        var laydate = layui.laydate;
        //生产日期
        $(".laydate.mfdate").each(function () {
            laydate.render({
                elem: this,
                type: 'date',
                format:'yyyy-MM-dd',
                max:GetDateStr(-1),
                trigger: 'click'
            });
        });
        $(".laydate.expire").each(function () {
            laydate.render({
                elem: this,
                type: 'date',
                format:'yyyy-MM-dd',
                min:GetDateStr(0),
                trigger: 'click'
            });
        });
        $(".laydate:not(.mfdate)").each(function () {
            laydate.render({
                elem: this,
                type: 'date',
                format:'yyyy-MM-dd',
                trigger: 'click'
            });
        });
        $(".laydate:not(.expire)").each(function () {
            laydate.render({
                elem: this,
                type: 'date',
                format:'yyyy-MM-dd',
                trigger: 'click'
            });
        });
    });
}
/*
 *   功能:实现VBScript的DateAdd功能.
 *   参数:interval,字符串表达式，表示要添加的时间间隔.
 *   参数:number,数值表达式，表示要添加的时间间隔的个数.
 *   参数:date,时间对象.
 *   返回:新的时间对象.
 *   var now = new Date();
 *   var newDate = DateAdd( "d", 5, now);
 *---------------   DateAdd(interval,number,date)   -----------------
 */
//没有穿日期以当前日期
function DateAdd(interval, number, date,minus1) {
    if(!date){
        date = new Date();
    }
    if(date&&typeof(date)==="string"){
        date = new Date(Date.parse(date.replace(/-/g, '/')));
    }
    if(minus1!=null||minus1!=undefined){
        date.setDate(date.getDate() + minus1);
    }
    switch (interval) {
        case "y": {
            date.setFullYear(date.getFullYear() + number);
            return dateToString(date);
            break;
        }
        case "q": {
            date.setMonth(date.getMonth() + number * 3);
            return date;
            break;
        }
        case "m": {
            date.setMonth(date.getMonth() + number);
            return date;
            break;
        }
        case "w": {
            date.setDate(date.getDate() + number * 7);
            return date;
            break;
        }
        case "d": {
            date.setDate(date.getDate() + number);
            return date;
            break;
        }
        case "h": {
            date.setHours(date.getHours() + number);
            return date;
            break;
        }
        case "min": {
            date.setMinutes(date.getMinutes() + number);
            return date;
            break;
        }
        case "s": {
            date.setSeconds(date.getSeconds() + number);
            return date;
            break;
        }
        default: {
            date.setDate(date.getDate() + number);
            return date;
            break;
        }
    }
}

//日期转字符串
function dateToString(date) {
    var str="";
    var d = new Date(date);
    var y = d.getFullYear();
    var m = (d.getMonth() + 1) < 10 ? "0" + (d.getMonth() + 1) : (d.getMonth() + 1);//获取当前月份的日期，不足10补0
    var dd = d.getDate() < 10 ? "0" + d.getDate() : d.getDate();//获取当前几号，不足10补0
    return y + "-" + m + "-" + dd;
}

function StringToDate(str) {
    return new Date(str.replace("-", "/").replace("-", "/"));
}
//日期比较，后者大于前者返回1，else0
function compareDate(d1,d2) {
    if(typeof(d1)&&d1){
        d1 = StringToDate(d1);
    }
    if(typeof(d2)&&d2){
        d2 = StringToDate(d2);
    }
    if(d2>=d1){
        return 1;
    }else{
        return 0;
    }
}

function getProofInfo(nodes) {
	return getProofInfoNew(nodes);
    var type;
    if(nodes){
        if(nodes.typename){
            if((nodes.typename).indexOf("营业执照")>=0){
                type=1;
            }
            if((nodes.typename).indexOf("经营许可证")>=0){
                type=2;
            }
            if((nodes.typename).indexOf("第一类医疗器械生产备案凭证")>=0||(nodes.typename).indexOf("第二类医疗器械经营备案凭证")>=0){
                type=3;
            }
            if((nodes.typename).indexOf("年度信用报告")>=0){
                type=4;
            }
            if((nodes.typename).indexOf("法人授权委托书")>=0){
                type=5;
            }
            if((nodes.typename).indexOf("注册证")>=0||(nodes.typename).indexOf("中华人民共和国医疗器械注册证")>=0){
                type=6;
            }if((nodes.typename).indexOf("消毒产品检测报告")>=0||(nodes.typename).indexOf("消毒产品(消杀类)检测报告")>=0){
                type=7;
            }if((nodes.typename).indexOf("商品备案凭证/备案信息表")>=0){
                type=8;
            }if((nodes.typename).indexOf("生产厂家证照")>=0){
                type=9;
            }if((nodes.typename).indexOf("代理企业证照")>=0){
                type=10;
            }if((nodes.typename).indexOf("产品授权代理书")>=0||(nodes.typename).indexOf("授权书")>=0){
                type=11;
            }if((nodes.typename).indexOf("产品说明书")>=0){
                type=12;
            }
            if((nodes.typename).indexOf("生产许可证")>=0){
                type=13;
            }
            if((nodes.typename).indexOf("消毒产品卫生安全评价报告")>=0||(nodes.typename).indexOf("卫生安全评价报告(消杀类)")>=0){
                type=14;
            }
            if((nodes.typename).indexOf("进口产品中英文授权代理书")>=0){
                type=15;
            }
            if((nodes.typename).indexOf("消毒产品卫生许可证")>=0||(nodes.typename).indexOf("消毒产品(消杀类)卫生许可证")>=0){
                type=16;
            }

            if((nodes.typename).indexOf("危险化学品经营许可证")>=0){
                type=17;
            }
            if((nodes.typename).indexOf("非药品类易制毒化学品经营备案证明")>=0){
                type=18;
            }
            if((nodes.typename).indexOf("全国工业产品生产许可证")>=0){
                type=19;
            }
            if((nodes.typename).indexOf("安全生产许可证")>=0){
                type=20;
            }
            if((nodes.typename).indexOf("医疗器械经营许可证")>=0){
                type=21;
            }
        }
    }
    var shtml = '';
    shtml += (nodes.isupload == 0 ? '<span class="layui-badge layui-bg-yellow">待提交</span>' : '<span class="layui-badge layui-bg-green">已提交</span>');
    shtml += (nodes.status == 0 ? '<span class="layui-badge layui-bg-yellow">待审核</span>' : (nodes.status == 1 ? '<span class="layui-badge layui-bg-green">已审核</span>' : '<span class="layui-badge layui-bg-red" title="' + nodes.message + '">不通过</span> <span style="margin-left: 5px;">理由：' + nodes.message + '</span>'));
    var html='<table class="layui-table certtable" style="width: 100%;"><tr>'+
        '<th>状态：</th>'+
        '<td><p class="status">'+shtml+'</p></td>'+
        '<th>有效性：</th>'+
        '<td ><p name="yxx">'+nodes.yxx+'</p></td>' +
        '<td><a onclick="seeImg('+nodes.certcd+',true)" class="layui-btn layui-btn-danger layui-btn-sm">查看文件</a></td>'+
        '</tr>';
    if(type==1){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>统一社会信用代码:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>类型:</th>' +
            '<td><p name="compytype">'+nodes.compytype+'</p></td>' +
            '<th>住所:</th>' +
            '<td><p name="dwaddr">'+nodes.dwaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>法定代表人:</th>' +
            '<td><p name="corporation">'+nodes.corporation+'</p></td>' +
            '<th>注册资本:</th>' +
            '<td><p name="regcapital">'+nodes.regcapital+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>成立日期:</th>' +
            '<td><p name="estdate">'+nodes.estdate+'</p></td>' +
            '<th>营业期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>登记机关:</th>' +
            '<td><p name="lssuioffice">'+nodes.lssuioffice+'</p></td>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +

            '';
    }
    if(type==2){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>许可证编号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>法定代表人:</th>' +
            '<td><p name="corporation">'+nodes.corporation+'</p></td>' +
            '<th>经营方式:</th>' +
            '<td><p name="busstype">'+nodes.busstype+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业负责人:</th>' +
            '<td><p name="enterprin">'+nodes.enterprin+'</p></td>' +
            '<th>住所:</th>' +
            '<td><p name="dwaddr">'+nodes.dwaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>' +
            '<th>经营场所:</th>' +
            '<td><p name="bussspace">'+nodes.bussspace+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>库房地址:</th>' +
            '<td><p name="whaddr">'+nodes.whaddr+'</p></td>' +
            '<th>发证部门:</th>' +
            '<td><p name="lssuioffice">'+nodes.lssuioffice+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>有效期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.yxx+'</p></td>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            ''

    }
    if(type==3){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备案编号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>法定代表人:</th>' +
            '<td><p name="corporation">'+nodes.corporation+'</p></td>' +
            '<th>企业负责人:</th>' +
            '<td><p name="enterprin">'+nodes.enterprin+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营方式:</th>' +
            '<td><p name="busstype">'+nodes.busstype+'</p></td>' +
            '<th>住所:</th>' +
            '<td><p name="dwaddr">'+nodes.dwaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营场所:</th>' +
            '<td><p name="bussspace">'+nodes.bussspace+'</p></td>' +
            '<th>库房地址:</th>' +
            '<td><p name="whaddr">'+nodes.whaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>' +
            '<th>备案部门:</th>' +
            '<td><p name="regoffice">'+nodes.regoffice+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备案日期:</th>' +
            '<td><p name="regdate">'+nodes.regdate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==4){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==5){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>被授权人名称:</th>' +
            '<td><p name="authorizeuser">'+nodes.authorizeuser+'</p></td>' +
            '<th>被授权人联系电话:</th>' +
            '<td><p name="authorizetel">'+nodes.authorizetel+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业固定电话:</th>' +
            '<td><p name="companytel">'+nodes.companytel+'</p></td>' +
            '<th>授权期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            ''
    }
    if(type==6){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>注册证编号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '<th>注册人名称:</th>' +
            '<td><p name="corporation">'+nodes.corporation+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>注册人住所:</th>' +
            '<td><p name="dwaddr">'+nodes.dwaddr+'</p></td>' +
            '<th>生产地址:</th>' +
            '<td><p name="proaddr">'+nodes.proaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>代理人名称:</th>' +
            '<td><p name="authorizeuser">'+nodes.authorizeuser+'</p></td>' +
            '<th>代理人住所:</th>' +
            '<td><p name="authorizeaddr">'+nodes.authorizeaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname">'+nodes.flname+'</p></td>' +
            '<th>型号规格:</th>' +
            '<td><p name="prospec" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.prospec+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>结构及组成:</th>' +
            '<td><p name="structureform" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;" >'+nodes.structureform+'</p></td>' +
            '<th>适合范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>附件:</th>' +
            '<td><p name="attachment" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.attachment+'</p></td>' +
            '<th>其他内容:</th>' +
            '<td><p name="others" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.others+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>审批部门:</th>' +
            '<td><p name="lssuioffice">'+nodes.lssuioffice+'</p></td>' +
            '<th>批准日期:</th>' +
            '<td><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>有效期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '<th>备注:</th>' +
            '<td><p name="remarks">'+nodes.remarks+'</p></td>' +
            '</tr>' +
            ''
    }
    if(type==7){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>样品名称:</th>' +
            '<td><p name="flname">'+nodes.flname+'</p></td>' +
            '<th>检测日期:</th>' +
            '<td><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==8){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>商品名称:</th>' +
            '<td><p name="flname">'+nodes.flname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备案号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>发证机关:</th>' +
            '<td><p name="lssuioffice">'+nodes.lssuioffice+'</p></td>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==9){
        html+='<tr>' +
            '<th  >供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th >生产厂商名称:</th>' +
            '<td colspan="3"><p name="companyname">'+nodes.companyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>发证日期:</th>' +
            '<td><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==10){
        html+='<tr>' +
            '<th  >供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >代理企业名称:</th>' +
            '<td colspan="3"><p name="companyname">'+nodes.companyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==11){
        html+='<tr>' +
            '<th  >供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th >被授权企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '<th >授权企业名称:</th>' +
            '<td><p name="authorizedcompy">'+nodes.authorizedcompy+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 150px;">被授权企业授权书等级:</th>' +
            '<td><p name="authorizelevel">'+nodes.authorizelevel+'</p></td>' +
            '<th>授权期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>授权产品范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>' +
            '<th>备注:</th>' +
            '<td ><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            ''
    }
    if(type==12){
        html+='<tr>' +
            '<th  >供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >产品名称:</th>' +
            '<td colspan="3"><p name="flname">'+nodes.flname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==13){
        html+='<tr>' +
            '<th  >供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >生产厂商名称:</th>' +
            '<td colspan="3"><p name="companyname">'+nodes.companyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th >生产范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>'+
            '</tr>' +
            '<tr>' +
            '<th >备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height: 200px;overflow-y: scroll;word-wrap:break-word;">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==14){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>样品名称:</th>' +
            '<td><p name="flname">'+nodes.flname+'</p></td>' +
            '<th>检测日期:</th>' +
            '<td><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==15){
        html+='<tr>' +
            '<th  >供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th >被授权企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '<th >授权企业名称:</th>' +
            '<td><p name="authorizedcompy">'+nodes.authorizedcompy+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th style="width: 150px;">被授权企业授权书等级:</th>' +
            '<td><p name="authorizelevel">'+nodes.authorizelevel+'</p></td>' +
            '<th>授权期限:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>授权产品范围:</th>' +
            '<td><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>' +
            '<th>备注:</th>' +
            '<td ><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            ''
    }
    if(type==16){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>生产企业单位名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '<th>生产类别:</th>' +
            '<td><p name="compytype">'+nodes.compytype+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==17){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>许可范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==18){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>主要品种、销售量:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>主要流向:</th>' +
            '<td colspan="3"><p name="others">'+nodes.others+'</p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==19){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname">'+nodes.flname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>生产厂商名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==20){
        html+='<tr>' +
            '<th>供应商:</th>' +
            '<td><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname">'+nodes.flname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>生产企业名称:</th>' +
            '<td><p name="companyname">'+nodes.companyname+'</p></td>' +
            '<th>证书编号:</th>' +
            '<td><p name="certno">'+nodes.certno+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>效期:</th>' +
            '<td><p name="certtermstart" style="display: inline-block;">'+nodes.certtermstart+'</p> ~ <p name="certterm" style="display: inline-block;">'+nodes.certterm+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>许可范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    if(type==21){
        html+=
            '<th>注册人名称:</th>' +
            '<td><p name="corporation">'+nodes.corporation+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>注册人住所:</th>' +
            '<td><p name="dwaddr">'+nodes.dwaddr+'</p></td>' +
            '<th>生产地址:</th>' +
            '<td><p name="proaddr">'+nodes.proaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>代理人名称:</th>' +
            '<td><p name="authorizeuser">'+nodes.authorizeuser+'</p></td>' +
            '<th>代理人住所:</th>' +
            '<td><p name="authorizeaddr">'+nodes.authorizeaddr+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>产品名称:</th>' +
            '<td><p name="flname">'+nodes.flname+'</p></td>' +
            '<th>型号规格:</th>' +
            '<td><p name="prospec" style="max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;">'+nodes.prospec+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th width="94">结构及组成:</th>' +
            '<td colspan="3"><p name="structureform" style="max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;" >'+nodes.structureform+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>适合范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>附件:</th>' +
            '<td><p name="attachment" style="max-height:200px;overflow-y:auto;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.attachment+'</p></td>' +
            '<th>其他内容:</th>' +
            '<td><p name="others" style="max-height:200px;overflow-y:auto;word-wrap:break-word;white-space:normal;">'+nodes.others+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td><p name="remarks">'+nodes.remarks+'</p></td>' +
            '</tr>' +
            ''
    }
    if(!type){
        html+='<tr>' +
            '<th  >供应商:</th>' +
            '<td colspan="3"><p name="scompyname">'+nodes.scompyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >企业名称:</th>' +
            '<td colspan="3"><p name="companyname">'+nodes.companyname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >证照编码:</th>' +
            '<td colspan="3"><p name="certno">'+nodes.certno+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >证照名称:</th>' +
            '<td colspan="3"><p name="certname">'+nodes.certname+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证机关:</th>' +
            '<td colspan="3"><p name="lssuioffice">'+nodes.lssuioffice+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >发证日期:</th>' +
            '<td colspan="3"><p name="lssuidate">'+nodes.lssuidate+'</p></td>' +
            '</tr>' +
            '<tr>' +
            '<th  >证照期限:</th>' +
            '<td> <p><span name="certtermstart">'+nodes.certtermstart+'</span>  ~~  <span name="certterm">'+nodes.certterm+'</span></p></td>' +
            '</tr>' +
            '<tr>' +
            '<th>经营范围:</th>' +
            '<td colspan="3"><p name="certscope" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.certscope+'</p></td>'+
            '</tr>' +
            '<tr>' +
            '<th>备注:</th>' +
            '<td colspan="3"><p name="remarks" style="max-height:200px;overflow-y:scroll;max-width: 200px;word-wrap:break-word;white-space:normal;">'+nodes.remarks+'</p></td>'+
            '</tr>' +
            '';
    }
    html+='</table>';
    return html;
}
function getProofInfoNew(nodes){
	 var html="";
    var json=getDataWithDetail(nodes,'select_typeext_lists',null,'/OnLineDevelopProFiles/vsspd_hc/proof/proof_search_goods1');
    var shtml = '';
    shtml += (nodes.isupload == 0 ? '<span class="layui-badge layui-bg-yellow">待提交</span>' : '<span class="layui-badge layui-bg-green">已提交</span>');
    /*shtml += (nodes.status == 0 ? '<span class="layui-badge layui-bg-yellow">待审核</span>' : (nodes.status == 1 ? '<span class="layui-badge layui-bg-green">已通过</span>' : '<span class="layui-badge layui-bg-red" title="' + nodes.message + '">不通过</span> <span style="margin-left: 5px;">理由：' + nodes.message + '</span>'));*/
    html+='<tr>'+
        '<th>状态：</th>'+
        '<td><p class="status"><span class="layui-badge layui-bg-green">已通过</span></p></td>'+
        '<th>有效性：</th>'+
        '<td ><span><p name="yxx">'+nodes.yxx+'</span><span style="float:right"><a onclick="seeImg('+nodes.certcd+',true)" class="layui-btn layui-btn-danger layui-btn-sm">查看文件</a></span></p></td>'+
		//'<td><a onclick="seeImg('+nodes.certcd+',true)" class="layui-btn layui-btn-danger layui-btn-sm">查看文件</a></td>'+
        '</tr>'+
		'<tr><th>供应商:</th><td><p name="scompyname">'+nodes.scompyname+'</p></td></tr>';
    html+='<tr>';
    $.each(json,function (index,val) {
        if(val.fieldname=='scompycd'){
            val.extvalue = getDataWithDetail({companycd:val.extvalue},'select_compyname_bycd',null,'/OnLineDevelopProFiles/vsspd_hc/proof/proof_search_goods1')[0].supplyname;
        }
        html+='<th>'+val.displayname+':</th><td><p name='+val.fieldname+'>'+(nodes[val.fieldname]||'')+'</p>'+(val.fieldname2?'<p name='+val.fieldname2+'>'+(nodes[val.fieldname2]||'')+'</p>':'')+'</td>';
        if((index+1)%2==0){
            html+='</tr><tr>'
        }
    });
    html+='</tr>';
	return html;
}
function CertScopeAnalyse(str) {
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/
    var result="";
    if(str){
        var a="";
        for(var i=0;i<str.length;i++){
            //判断是否是数字
            if(re.test(str[i])){
                a+=str[i];
            }else{
                if(a.trim()){
                    var s = '\''+a+'\'';
                    result=result+s+',';
                    a="";
                }

            }
        }
    }
    result = (result.slice(result.length - 1) === ',' )  ? result.slice(0, -1) : result;
    return result

}
function loadScopeGoods(el) {
    var json = $(el).data("json");
    if (typeof (json) == "string") {
        json = json.replace(/'/g, '"');
        json = JSON.parse(json);
    }
    if(json){
        var sJson={};
        sJson.scompycd = json.scompycd;
        sJson.catgcodes = CertScopeAnalyse(json.certscope);
        if(window.LoadScopeGoodsTable){
            LoadScopeGoodsTable(sJson);
        }
    }
}
function isKeyPressed(event)
{
    if (event.shiftKey==1)
    {
        shiftKey = true;
    }
    else
    {
        shiftKey = false;
    }

}

function monitorShift (filterName) {
    var first,last;
    layui.use('table', function(){
        var table = layui.table;
        var $ = layui.jquery;
        table.on('checkbox('+filterName+')', function(obj){
            var index = $(obj.tr).data("index");
            if(!first&&!last&&first!=0&&!last!=0){
                first = index;
            }
            if((first||first==0)&&!last&&last!=0){
                last = index;
            }
            if((first||first==0)&&(last||last==0)){
                first = last;
                last = index;
            }
            if(shiftKey){
                var startNum = first < last ? first : last;
                var endNum = first > last ? first : last;
                for(var i = startNum; i <= endNum; i++) {
                    if(obj.checked) {
                        $('.layui-table-body table.layui-table tbody tr[data-index='+i+']').find("div.layui-form-checkbox").addClass('layui-form-checked');
                        $('.layui-table-body table.layui-table tbody tr[data-index='+i+']').find("input[type='checkbox']").prop("checked",true);
                    } else {
                        $('.layui-table-body table.layui-table tbody tr[data-index='+i+']').find("div.layui-form-checkbox").removeClass('layui-form-checked');
                        $('.layui-table-body table.layui-table tbody tr[data-index='+i+']').find("input[type='checkbox']").prop("checked",false);

                    }
                }
            }
        });
    })
}
function initlayuiForm(){
    layui.use(['element','table','layer'], function() {
        var $ = layui.jquery, element = layui.element;
        var table = layui.table,layer = layui.layer;
        var form = layui.form;
        form.render("select");
        form.on("select",function (el) {
            var name = $(el['elem']).attr("name");
            var func = $(el['elem']).attr("onchage");
            if(func){
                eval(func);
            }
        })
    })
}
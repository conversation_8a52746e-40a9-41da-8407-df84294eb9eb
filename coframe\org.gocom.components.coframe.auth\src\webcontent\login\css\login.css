body {
  height: 100%;
  width: 100%;
  color: #333;
  font-size: 12px;
  font-family: "microsoft yahei", Microsoft YaHei, STXihei, SimHei, SimSun,
    sans-serif;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
input {
    outline:none;
    background:transparent;
    border:none;
    outline:medium;
}
*:focus { 
    outline: none;
    background-color: transparent;
}
::selection{background:transparent; }
::-moz-selection{background:transparent; }
input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill{
    -webkit-box-shadow: 0 0 0px 1000px white inset
    }
#fs-login-background {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
/* #fs-login,#fs-login-glass{
    position: absolute;
    right: 5;
    top: 50%;
    width: 354px;
    height: 320px;
    margin-top: -140px;
    right: 10%;
} */
/* #fs-login,
#fs-login-glass,
#fs-login-scalebg {
  position: absolute;
  margin-top: -140px;
  margin-left: -200px;
  left: 50%;
  top: 50%;
  width: 400px;
  height: 280px;
} */
/* #cLeft{
  position: relative;
} */
/* #fs-login{
  position: absolute;
  right: 19%;
  top: 50%;
} */
#fs-login-content {
  position: relative;
  padding: 0 20px;
  box-sizing: border-box;
  color: #5150d4;
  text-align:center;
  width: 100%;
  /* width: 80%; */
  /* display: flex; */
  margin: 0 auto;
  /* margin-top:50px */
}
#fs-login-title {
     height: 120px;
     line-height: 120px;
     font-size: 24px;
     font-family: initial;
	 text-align:center;
	 font-weight:bold;
   /* letter-spacing: 3px; */
}
#fs-login-glass {
  filter: Alpha(opacity=60);
  opacity: 0.7;
  background: #fff;
  border: solid 1px #e0e2e7;
  border-radius: 8px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
}
#fs-login-scalebg {
  overflow: hidden;
}
#fs-login-scalebg img.fs-login-scalebg-img {
  -webkit-filter: blur(7px);
  -moz-filter: blur(7px);
  -ms-filter: blur(7px);
  filter: blur(7px);
  filter: progid:DXImageTransform.Microsoft.Blur(PixelRadius=7,MakeShadow=false);
}
div.fs-login-input {
  position: relative;
  border-radius: 2px;
  margin-bottom: 24px;
  padding: 0;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  /* border: solid 1px #c0c0c0; */
}
input,
div.fs-login-errmask {
  margin: 0;
  border: 0;
  border-radius: 1px;
  vertical-align: middle;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  font-size: 14px;
  height: 35px;
}
input {
  position: relative;
  margin: 0 0 0 55px;
  padding: 0;
  outline: 0;
  width: 253px;
  line-height: 42px\9;
}
div.fs-login-errmask {
  filter: Alpha(opacity=80);
  opacity: 0.8;
  position: absolute;
  top: 0;
  left: 0;
  height: 35px;
  color: #fff;
  width: 280px;
  line-height: 35px;
  text-align: center;
  background: #333;
  z-index: 1;
}
div.fs-login-input-focus {
  border-color: #9b9b9b;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.12);
}
div.fs-login-input-username {
  /* background: #f6f9ff ; */
  border: 1px solid #ccc;
  border-radius: 10px;
  text-align: left;
}
div.fs-login-input-password {
  /* background: #f6f9ff ; */
  border: 1px solid #ccc;
  border-radius: 10px;
  text-align: left;
}
a#fs-login-btn {
  display: inline-block;
  box-sizing: border-box;
  background:#0069FF;
  padding:8px 40px;
  color: #fff;
  text-align: center;
  font-size: 16px;
  margin-top:30px;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  cursor: pointer !important;
  text-decoration: none;
  outline: 0;
  font-weight:bold;
  width: 100%;
  opacity: 1;
}
a:hover {
  /* background: #0662c7; */
}
div.fs-login-check {
  text-align: right;
  height: 40px;
  line-height: 40px;
}
span.fs-login-remember {
  cursor: pointer !important;
  font-size: 16px;
  padding-left: 25px;
  background: url("/spdtest02/ReportServer?op=resource&resource=/com/fr/fs/resources/images/fslogin_remember.gif")
    no-repeat 4px 2px;
}
span.fs-login-remember-selected {
  background: url("/spdtest02/ReportServer?op=resource&resource=/com/fr/fs/resources/images/fslogin_remember.gif")
    no-repeat 4px -46px;
}
.login_icon{
	position:absolute;
	top:50%;
	margin-top:-10px;
	font-size:20px;
	left:15px;
	color:#8C8C8C 
}
.bg{
            position: relative;
            background-image: url(../images/logins1.jpg);
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            background-attachment: fixed;
        }
        .header{
            width: 100%;
            height: 92px;
            line-height: 92px;
            background-color: #ffffff;
            position: relative;
        }
        .container{
            width: 100%;
			position:relative;
        }
        .footer{
            width: 100%;
            height: 52px;
            line-height: 52px;
            color: #acacac;
            text-align: center;
            background-color: #ffffff;
            font-size: 14px;
        }
        .header_img{
           margin-left: 20px;
            vertical-align: middle;
            margin-top: 20px;

        }
        .header_title{
            font-size: 24px;
            color:#bfa162;
            font-weight: bold;
            margin-left: 50px;
        }
        

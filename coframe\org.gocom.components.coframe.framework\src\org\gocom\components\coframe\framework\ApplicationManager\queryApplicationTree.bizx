<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="queryApplicationTree.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link4</targetConnections>
    <location x="800" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="java.util.List" name="data" type="query" valueType="Java">data</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="802" y="96"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="查询所有应用" displayName="queryAllAppApplications" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="208" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.queryAllAppApplications</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out" type="query" value="org.gocom.components.coframe.framework.application.AppApplication[]" valueType="Java">application</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="186" y="96"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="查询所有功能组" displayName="queryAllAppFuncgroups" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>invokeSpring2</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="356" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.queryAllAppFuncgroups</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out" type="query" value="org.gocom.components.coframe.framework.application.AppFuncgroup[]" valueType="Java">funcgroup</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="328" y="96"/>
    <figSize height="12" width="85"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="查询所有功能" displayName="queryAppFunctions" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>invokeSpring3</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="504" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFunctionService.queryAppFunctions</process:partner>
      <process:instance instanceName="AppFunctionBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppFunction[]" valueType="Java">function</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="482" y="96"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="拼接成应用功能树" displayName="getApplicationTree" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="652" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.getApplicationTree</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="application" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">application</process:inputVariable>
      <process:inputVariable id="1" name="functionGroup" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">funcgroup</process:inputVariable>
      <process:inputVariable id="2" name="function" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">function</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out" type="query" value="java.util.List&lt;java.util.Map>" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="618" y="96"/>
    <figSize height="12" width="97"/>
    <node>invokeSpring3</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-01 17:33:57" date="2013-03-01Z" description="" name="queryApplicationTree" version="6.3"/>
  <process:variables>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppApplication" description="" historyStateLocation="client" isArray="true" name="applicaiton"/>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppFuncgroup" description="" historyStateLocation="client" isArray="true" name="funcgroup"/>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppFunction" description="" historyStateLocation="client" isArray="true" name="function"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
  </process:variables>
  <process:inputs varArgs="false"/>
  <process:outputs>
    <process:output anyType="java.util.List" description="" isArray="false" name="data"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

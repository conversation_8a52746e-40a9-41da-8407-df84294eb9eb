<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="isend.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="判断用户的启用时间和结束时间是否合法。同时，判断密码是否过期" title="andy&#x9;14-9-11 下午2:56">
    <location x="159" y="348"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="link2" displayName="连接线" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="ISNULL">
          <process:leftOperand type="query">capuser/enddate</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="60" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link5</targetConnections>
    <targetConnections>link11</targetConnections>
    <targetConnections>link14</targetConnections>
    <targetConnections>link12</targetConnections>
    <location x="787" y="45"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Int" name="isend" type="query" valueType="Primitive">isend</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="244"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="789" y="81"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="有失效日期时，则判断是否过期" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="link4" displayName="连接线" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="LE">
          <process:leftOperand type="query">capuser/enddate</process:leftOperand>
          <process:rightOperand type="query">today</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="208" y="124"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="expression" pattern="reference">new Date()</process:from>
      <process:to type="query">today</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="138" y="160"/>
    <figSize height="17" width="169"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="没有失效日期，或未过期时" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>switch1</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link7" name="link7" displayName="连接线" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>switch0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NOTNULL">
          <process:leftOperand type="query">capuser/startdate</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <targetConnections>link1</targetConnections>
    <location x="356" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">1</process:from>
      <process:to type="query">isend</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">new Date()</process:from>
      <process:to type="query">today</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="298" y="244"/>
    <figSize height="17" width="145"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="已过期" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="356" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">3</process:from>
      <process:to type="query">isend</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="352" y="96"/>
    <figSize height="17" width="37"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="未到使用期" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link10</targetConnections>
    <location x="525" y="100"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">4</process:from>
      <process:to type="query">isend</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="509" y="136"/>
    <figSize height="17" width="61"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tSwitch" id="switch1" name="密码是否过期" displayName="空操作" type="switch">
    <sourceConnections xsi:type="process:tLink" id="link12" name="link12" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>switch1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link13" name="link13" displayName="连接线" type="transition">
      <sourceNode>switch1</sourceNode>
      <targetNode>assign4</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="LE">
          <process:leftOperand type="query">capuser/invaldate</process:leftOperand>
          <process:rightOperand type="query">today</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <targetConnections>link9</targetConnections>
    <location x="600" y="185"/>
    <size height="28" width="28"/>
    <nodeLabel>switch1label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="switch1label" name="label" nodeType="label">
    <location x="578" y="221"/>
    <figSize height="17" width="73"/>
    <node>switch1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="密码过期" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link14" name="link14" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="749" y="189"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">5</process:from>
      <process:to type="query">isend</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="739" y="225"/>
    <figSize height="17" width="49"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tSwitch" id="switch0" name="空操作" displayName="空操作" type="switch">
    <sourceConnections xsi:type="process:tLink" description="" id="link10" name="link10" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="GE">
          <process:leftOperand type="query">capuser/startdate</process:leftOperand>
          <process:rightOperand type="query">today</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>switch1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="394" y="131"/>
    <size height="28" width="28"/>
    <nodeLabel>switch0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="switch0label" name="label" nodeType="label">
    <location x="390" y="167"/>
    <figSize height="17" width="37"/>
    <node>switch0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="andy" createTime="2014-09-11 14:09:23" date="2014-09-11Z" description="" name="isend" version="*******"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" name="today" primitiveType="Date"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" modelType="org.gocom.components.coframe.rights.dataset.CapUser" name="capuser"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="isend" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

<%@page pageEncoding="UTF-8"%>
<%@page import="com.primeton.cap.AppUserManager"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<!-- 
  - Author(s): shitf
  - Date: 2013-03-07 15:24:13
  - Description:
-->
<head>
<title>SPD院内物流资源信息管理系统-登录</title>
<%
   String contextPath = request.getContextPath();
%>
 
 <script>
	//直接访问login.jsp之后的跳转
	//location.href="<%=contextPath %>" + "/core/login.jsp";
</script>

<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/style.css" />
<script type="text/javascript">
    if(top!=window){
        top.location.href=top.location.href; 
        top.location.reload; 
    }
</script>
</head>
<%
	String original_url=null;
	Object objAttr=request.getAttribute("original_url");
	if(objAttr != null){
		original_url=(String)objAttr;
	}
 %>
<body class="login">
	<div id="warpper" class="wrap">
		<div class="main">
			<div id="form1" class="login-box">
				<h3>实验室物流信息管理平台</h3>
				<p class="login-item">
				  <em>用户名：</em>
				  <input class="nui-textbox" id="userId" name="userId" style="width:237px;height:26px;"
				   onvalidation="onCheckUserId"/>
				</p>
				<p class="login-item">
				  <em>密　码：</em>
				  <input name="password" class="nui-password" vtype="minLength:6" minLengthErrorText="密码不能少于6个字符"
		                onenter="keyboardLogin" style="width:237px;height:26px;" onvalidation="onCheckPassword"/>
				</p>
				<p id="error" class="login-error" style="display:inline-block;height:20px;color:red;"></p>
				<p class="login-btn center">
					<input class="log" type="button" onclick="login();" value="登 录" />
				</p>
				<div style="width:1px;height:1px;display:none" >
				<iframe id="mainframe" 
				src="" 
				frameborder="0" name="main" 
				style="display:none;" border="0"></iframe>
				</div>
			</div>
		</div>
		<div class="foot">
			<p>(c) 上海万序计算机科技有限公司版权所有  <span></span></p>
		</div>
	</div>
	<div id="window" class="mini-window" title="选择部门" style="width:400px;height:250px;" 
	    showCollapseButton="false" showFooter="true" allowResize="false" allowDrag="true">
	    <div id="datagrid1" class="mini-datagrid" style="width:100%;height:100%;" 
	    	allowResize="false" multiSelect="false" 
	        url="org.gocom.components.coframe.org.employee.queryEmpOfOrg.biz.ext" 
	        dataField="orgs" totalField="total" sizeList="[10,20,50,100]">
	        <div property="columns">
	            <div type="checkcolumn" ></div>        
	            <div field="orgcode" width="80" headerAlign="center" allowSort="true">部门编号</div>    
	            <div field="orgname" width="120" headerAlign="center" allowSort="true">部门名称</div>    
	        </div>
	    </div>
	    <div property="footer" style="text-align:right;padding:5px;padding-right:15px;">
	    	<input type='button' value='确定' onclick="confirm" style='vertical-align:middle;'/>
	        <input type='button' value='取消' onclick="cancel" style='vertical-align:middle;'/>
	    </div>
	</div>
   <script type="text/javascript">
     nui.parse();
     var form = new nui.Form("#form1");
     
     nui.get("userId").focus();
     
     function login(){
     
     
       form.validate();
       if(form.isValid()==false) return;
       var data = form.getData();
       var json = nui.encode(data);
       
       nui.ajax({
         url:"org.gocom.components.coframe.auth.LoginManager.login.biz.ext",
         type:'post',
         data:json,
         success:function(text){
            var o = nui.decode(text);
            if(o.exception==null){
	           var ret = o.retCode;
	           if(ret==1){
	           	 var a="http://*************:8080/login.aspx?username="+data.userId+"&password="+data.password;
	   			// document.getElementById('mainframe').src = a;
	   			/* OpenWindow=window.open(a, "newwin", "height=1, width=1,toolbar=no ,menubar=no"); 
	   			 OpenWindow.document.write("<TITLE>例子</TITLE>") 
			　　OpenWindow.document.write("<BODY BGCOLOR=#ffffff>") 
			　　OpenWindow.document.write("<h1>Hello!</h1>") 
			　　OpenWindow.document.write("New window opened!") 
			　　OpenWindow.document.write("</BODY>") 
			　　OpenWindow.document.write("</HTML>") 
			　　OpenWindow.document.close()
			*/   //OrgOfEmp(data.userId);
	             location.href="<%=request.getContextPath() %>/coframe/auth/login/redirect.jsp?original_url=<%=original_url %>";
	           }else if(ret==0){
	             $("#error").html("输入密码错误");
	           }else if(ret==-2){
	           	 $("#error").html("用户无权限登录，请联系系统管理员");
	           }else{
	             $("#error").html("用户名不存在");
	           }
            }else{
               nui.alert("登录系统出错","系统提示");
            }
         }
       });
     }
     
     function reset(){
       form.reset();
     }
     
     function onCheckUserId(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "用户名不能为空";
           e.isValid = false;
         }
       }
     }
     
     function onCheckPassword(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "密码不能为空";
           e.isValid = false;
         }
       }
     }
    
     //获取键盘 Enter 键事件并响应登录
     function keyboardLogin(e){
       login();
     }
     
     function OrgOfEmp(userid){
     	mini.get("window").show();
		var data={
			"userid":userid
		};
        mini.get("datagrid1").load(data);
     }
     function confirm(){
     }
     function cancel(){
     	mini.get("window").hide();
     }
   </script>
 </body>
</html>

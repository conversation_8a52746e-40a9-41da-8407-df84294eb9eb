<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:ns1="http://schemas.xmlsoap.org/soap/http" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://portal.webserver.jhip.goodwillcis.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="SSOClientWebserviceImpl" targetNamespace="http://portal.webserver.jhip.goodwillcis.com/">
<wsdl:types>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="unqualified" targetNamespace="http://portal.webserver.jhip.goodwillcis.com/" version="1.0">
<xs:element name="getUserDetailInfo" type="tns:getUserDetailInfo"/>
<xs:element name="getUserDetailInfoResponse" type="tns:getUserDetailInfoResponse"/>
<xs:element name="sendPortalNews" type="tns:sendPortalNews"/>
<xs:element name="sendPortalNewsResponse" type="tns:sendPortalNewsResponse"/>
<xs:complexType name="getUserDetailInfo">
<xs:sequence>
<xs:element minOccurs="0" name="InputPara" type="xs:string"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="getUserDetailInfoResponse">
<xs:sequence>
<xs:element minOccurs="0" name="return" type="xs:string"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="sendPortalNews">
<xs:sequence>
<xs:element minOccurs="0" name="InputPara" type="xs:string"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="sendPortalNewsResponse">
<xs:sequence>
<xs:element minOccurs="0" name="return" type="xs:string"/>
</xs:sequence>
</xs:complexType>
</xs:schema>
</wsdl:types>
<wsdl:message name="getUserDetailInfo">
<wsdl:part element="tns:getUserDetailInfo" name="parameters"/>
</wsdl:message>
<wsdl:message name="sendPortalNews">
<wsdl:part element="tns:sendPortalNews" name="parameters"/>
</wsdl:message>
<wsdl:message name="sendPortalNewsResponse">
<wsdl:part element="tns:sendPortalNewsResponse" name="parameters"/>
</wsdl:message>
<wsdl:message name="getUserDetailInfoResponse">
<wsdl:part element="tns:getUserDetailInfoResponse" name="parameters"/>
</wsdl:message>
<wsdl:portType name="SSOClientWebservice">
<wsdl:operation name="getUserDetailInfo">
<wsdl:input message="tns:getUserDetailInfo" name="getUserDetailInfo"/>
<wsdl:output message="tns:getUserDetailInfoResponse" name="getUserDetailInfoResponse"/>
</wsdl:operation>
<wsdl:operation name="sendPortalNews">
<wsdl:input message="tns:sendPortalNews" name="sendPortalNews"/>
<wsdl:output message="tns:sendPortalNewsResponse" name="sendPortalNewsResponse"/>
</wsdl:operation>
</wsdl:portType>
<wsdl:binding name="SSOClientWebserviceImplSoapBinding" type="tns:SSOClientWebservice">
<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
<wsdl:operation name="getUserDetailInfo">
<soap:operation soapAction="" style="document"/>
<wsdl:input name="getUserDetailInfo">
<soap:body use="literal"/>
</wsdl:input>
<wsdl:output name="getUserDetailInfoResponse">
<soap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
<wsdl:operation name="sendPortalNews">
<soap:operation soapAction="" style="document"/>
<wsdl:input name="sendPortalNews">
<soap:body use="literal"/>
</wsdl:input>
<wsdl:output name="sendPortalNewsResponse">
<soap:body use="literal"/>
</wsdl:output>
</wsdl:operation>
</wsdl:binding>
<wsdl:service name="SSOClientWebserviceImpl">
<wsdl:port binding="tns:SSOClientWebserviceImplSoapBinding" name="SSOClientWebserviceImplPort">
<soap:address location="http://***********:10008/portal/services/userinfo"/>
</wsdl:port>
</wsdl:service>
</wsdl:definitions>

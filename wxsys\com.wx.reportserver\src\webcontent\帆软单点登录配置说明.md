# 帆软单点登录集成配置说明

## 概述

本次修改为帆软8、10、11三个版本集成了单点登录功能，实现从应用系统无缝跳转到帆软报表平台。

## 修改的文件

1. `frReport8.jsp` - 帆软8.0版本单点登录
2. `frReport10.jsp` - 帆软10.0版本单点登录  
3. `frReport11.jsp` - 帆软11.0版本单点登录

## 实现原理

### 帆软8.0
- 使用iframe方式进行单点登录
- 接口：`/WebReport/ReportServer?op=fs_load&cmd=sso&fr_username=xx&fr_password=xx`
- 登录成功后跳转到目标报表页面

### 帆软10.0/11.0
- 优先使用Ajax方式进行单点登录
- 接口：`/webroot/decision/login/cross/domain?fine_username=XX&fine_password=XX&validity=-2`
- Ajax失败时自动降级为iframe方式
- 登录成功后跳转到目标报表页面

## 配置步骤

### 1. 帆软系统配置

在每个帆软系统中需要进行以下配置：

#### 1.1 创建单点登录用户
- 在帆软管理系统中创建一个专用的单点登录用户
- 建议用户名：`sso_user`（可自定义）
- 设置密码并记录

#### 1.2 安全设置
- 关闭"内容嗅探攻击防护"
- 关闭"点击劫持攻击防护"（如果使用iframe方式）
- 路径：管理系统 > 系统管理 > 安全防护

#### 1.3 用户权限配置
- 为单点登录用户分配适当的权限
- 确保该用户可以访问需要的报表

### 2. JSP文件配置

修改每个JSP文件中的用户名和密码：

```javascript
// 需要修改的配置项
var frUsername = "admin"; // 改为实际的帆软用户名
var frPassword = "123456"; // 改为实际的帆软密码
```

### 3. 网络配置

确保应用服务器可以访问帆软服务器：
- 检查网络连通性
- 确认端口开放
- 配置防火墙规则（如需要）

## 功能特性

### 1. 用户信息传递
- 自动获取当前登录用户信息
- 将用户信息作为参数传递给帆软系统
- 参数名：`current_user`

### 2. 错误处理
- 单点登录失败时自动降级处理
- 提供详细的错误日志
- 确保用户能够正常访问报表

### 3. 兼容性
- 保持与原有跳转方式的兼容性
- 支持不同版本的帆软系统
- 自动适配不同的报表类型

## 测试验证

### 1. 功能测试
1. 登录应用系统
2. 点击跳转到帆软报表的链接
3. 验证是否自动完成单点登录
4. 检查报表是否正常显示

### 2. 错误场景测试
1. 帆软服务器不可达
2. 用户名密码错误
3. 网络超时
4. 验证降级处理是否正常

## 注意事项

### 1. 安全考虑
- 用户名和密码硬编码在JSP中，建议后续改为配置文件方式
- 考虑使用更安全的认证方式（如Token）
- 定期更换单点登录用户密码

### 2. 性能考虑
- 单点登录会增加页面加载时间
- 建议监控登录接口的响应时间
- 考虑添加超时处理机制

### 3. 维护建议
- 定期检查帆软系统的单点登录接口变化
- 监控错误日志，及时处理异常情况
- 建议将配置信息外部化管理

## 故障排查

### 1. 常见问题
- **单点登录失败**：检查用户名密码是否正确
- **跳转失败**：检查网络连接和端口配置
- **权限错误**：检查帆软用户权限设置

### 2. 调试方法
- 打开浏览器开发者工具查看控制台日志
- 检查网络请求是否成功
- 验证帆软接口返回结果

### 3. 日志查看
- 浏览器控制台日志
- 应用服务器日志
- 帆软系统日志

## 后续优化建议

1. **配置外部化**：将用户名密码等配置移到配置文件
2. **Token认证**：使用更安全的Token方式替代用户名密码
3. **缓存优化**：缓存登录状态，减少重复登录
4. **监控告警**：添加单点登录成功率监控
5. **用户体验**：添加加载提示，优化用户体验

## 联系支持

如遇到问题，请联系技术支持团队，并提供：
- 具体的错误信息
- 浏览器控制台日志
- 网络请求详情
- 帆软系统版本信息

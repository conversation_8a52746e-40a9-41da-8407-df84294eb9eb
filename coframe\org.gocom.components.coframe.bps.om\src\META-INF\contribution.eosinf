<?xml version="1.0" encoding="UTF-8"?>
<contribution xmlns="http://www.primeton.com/xmlns/eos/1.0">
	<!-- MBean config -->
	<module name="Mbean">
		<!-- DataSourceMBean config -->
		<group name="DatasourceMBean">
			<configValue key="Type">config</configValue>
			<configValue key="Class">com.eos.system.management.config.mbean.Config</configValue>
			<configValue key="Handler">com.eos.common.connection.mbean.ContributionDataSourceConfigHandler</configValue>
			<configValue key="ConfigFileType">config</configValue>
		</group>
		<group name="ContributionLoggerMBean">
			<configValue key="Type">config</configValue>
			<configValue key="Class">com.eos.system.management.config.mbean.Config</configValue>
			<configValue key="Handler">com.eos.common.logging.mbean.LogConfigHandler</configValue>
			<configValue key="ConfigFileType">log</configValue>
		</group>
	</module>

	<!-- datasource config -->	
	<module name="DataSource">
		<group name="Reference">
			<!--
				the configuration below describes 
				the corresponding relationship between contribution datasource and application datasource, 
				multiple datasources can be defined. 
				the value 'default' of attibute 'key' denotes a contribution datasource 
				and the field value 'default' of 'configValue' node stands for an application datasource. 
			-->
			<configValue key="default">default</configValue>
		</group>
	</module>
	<module name="PartyTypeAdapter">
		<group name="org">			
			<configValue key="prefix">O</configValue>	
			<configValue key="code">org</configValue>	
			<configValue key="displayName">机构</configValue>	
			<configValue key="description">机构</configValue>	
			<configValue key="showAtRootArea">true</configValue>	
			<configValue key="priority">4</configValue>	
			<configValue key="leafParticipant">false</configValue>	
			<configValue key="juniorParticipantTypeCodes">org,position,emp</configValue>	
			<configValue key="jointParticipantType">false</configValue>	
			<configValue key="jointTypeCodeList"></configValue>	
		</group>
		<group name="group">			
			<configValue key="prefix">G</configValue>	
			<configValue key="code">group</configValue>	
			<configValue key="displayName">工作组</configValue>	
			<configValue key="description">工作组</configValue>	
			<configValue key="showAtRootArea">true</configValue>	
			<configValue key="priority">4</configValue>	
			<configValue key="leafParticipant">false</configValue>	
			<configValue key="juniorParticipantTypeCodes">group,position,emp</configValue>	
			<configValue key="jointParticipantType">false</configValue>	
			<configValue key="jointTypeCodeList"></configValue>	
		</group>
		<group name="role">			
			<configValue key="prefix">R</configValue>	
			<configValue key="code">role</configValue>	
			<configValue key="displayName">角色</configValue>	
			<configValue key="description">角色</configValue>	
			<configValue key="showAtRootArea">true</configValue>	
			<configValue key="priority">2</configValue>	
			<configValue key="leafParticipant">false</configValue>	
			<configValue key="juniorParticipantTypeCodes">emp</configValue>	
			<configValue key="jointParticipantType">false</configValue>	
			<configValue key="jointTypeCodeList"></configValue>	
		</group>
		<group name="emp">			
			<configValue key="prefix">E</configValue>	
			<configValue key="code">emp</configValue>	
			<configValue key="displayName">员工</configValue>	
			<configValue key="description">员工</configValue>	
			<configValue key="showAtRootArea">false</configValue>	
			<configValue key="priority">1</configValue>	
			<configValue key="leafParticipant">true</configValue>	
			<configValue key="juniorParticipantTypeCodes"></configValue>	
			<configValue key="jointParticipantType">false</configValue>	
			<configValue key="jointTypeCodeList"></configValue>
		</group>
		<group name="position">			
			<configValue key="prefix">P</configValue>	
			<configValue key="code">position</configValue>	
			<configValue key="displayName">岗位</configValue>	
			<configValue key="description">岗位</configValue>	
			<configValue key="showAtRootArea">false</configValue>	
			<configValue key="priority">1</configValue>	
			<configValue key="leafParticipant">fales</configValue>	
			<configValue key="juniorParticipantTypeCodes">position,emp</configValue>	
			<configValue key="jointParticipantType">false</configValue>	
			<configValue key="jointTypeCodeList"></configValue>
		</group>
		
		<group name="user">			
			<configValue key="prefix">U</configValue>	
			<configValue key="code">user</configValue>	
			<configValue key="displayName">用户</configValue>	
			<configValue key="description">用户</configValue>	
			<configValue key="showAtRootArea">false</configValue>	
			<configValue key="priority">1</configValue>	
			<configValue key="leafParticipant">true</configValue>	
			<configValue key="juniorParticipantTypeCodes"></configValue>	
			<configValue key="jointParticipantType">false</configValue>	
			<configValue key="jointTypeCodeList"></configValue>
		</group>
	</module>
	
</contribution>

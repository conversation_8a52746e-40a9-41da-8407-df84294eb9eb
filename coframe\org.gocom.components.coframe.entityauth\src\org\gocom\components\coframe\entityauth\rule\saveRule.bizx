<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="saveRule.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node1" name="注释1" displayName="注释" collapsed="false" nodeType="note" type="note" content="添加实体规则" title="lijuntao 13-4-28 上午10:00">
    <location x="102" y="180"/>
    <size height="100" width="171"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <location x="102" y="105"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link1</targetConnections>
    <location x="402" y="105"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="String" name="flag" type="query" valueType="Primitive">flag</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="104" y="141"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="404" y="141"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="保存实体权限规则" displayName="addCapRule" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="242" y="105"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.entityauth.EntityCapRuleService.addCapRule</process:partner>
      <process:instance instanceName="entityCapRuleServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="ruleName" type="query" value="java.lang.String" valueType="Java" pattern="reference">ruleName</process:inputVariable>
      <process:inputVariable id="1" name="namespace" type="query" value="java.lang.String" valueType="Java" pattern="reference">namespace</process:inputVariable>
      <process:inputVariable id="2" name="ruleExpression" type="query" value="org.gocom.components.coframe.entityauth.pojo.Condition[]" valueType="Java" pattern="reference">ruleExpression</process:inputVariable>
      <process:inputVariable id="3" name="party" type="query" value="com.primeton.cap.party.Party" valueType="Java" pattern="reference">party</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">flag</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="208" y="141"/>
    <figSize height="12" width="97"/>
    <node>invokeSpring0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="lijuntao" createTime="2013-04-21 10:57:24" date="2013-04-21Z" description="" name="saveRule" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="ruleName" primitiveType="String"/>
    <process:input description="" isArray="false" name="namespace" primitiveType="String"/>
    <process:input anyType="org.gocom.components.coframe.entityauth.pojo.Condition" description="" isArray="true" name="ruleExpression"/>
    <process:input anyType="com.primeton.cap.party.Party" description="" isArray="false" name="party"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="flag" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

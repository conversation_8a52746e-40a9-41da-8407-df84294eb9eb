package com.wx.reportserver;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.eos.foundation.eoscommon.ConfigurationUtil;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.logging.Logger;
import com.wx.utils.publicMemer.Session.SessionUtil;

/**
 * 帆软服务接口
 * 
 * <AUTHOR>
 * 
 */
public class ReportServlet10 extends HttpServlet {

	private static Logger logger = TraceLoggerFactory.getLogger(ReportServlet10.class);

	private static final long serialVersionUID = -2187695682372304972L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		this.doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		StringBuilder commonAttrBuilder = new StringBuilder();
		HashMap loginUserInfo = SessionUtil.getLoginUserInfo();
		for (Object key : loginUserInfo.keySet()) {
			Object value = loginUserInfo.get(key);
			// 如果还未有参数，则拼接上 ?
			if (commonAttrBuilder.indexOf("?") == -1) {
				commonAttrBuilder.append("?").append(String.valueOf(key)).append("=")
						.append(URLEncoder.encode(String.valueOf(value), "UTF-8"));
			} else {
				// 否则拼接上 &
				commonAttrBuilder.append("&").append(String.valueOf(key)).append("=")
						.append(URLEncoder.encode(String.valueOf(value), "UTF-8"));
			}

		}

		// 获取 url 后面的参数集合
		Map<String, String[]> parameterMap = req.getParameterMap();
		// 将 parameterMap 中的不为空参数拼接到 urlParam 中，排除前台传递的 token 参数
		for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
			if (entry.getValue() != null && entry.getValue().length > 0) {
				// 如果还未有参数，则拼接上 ?
				if (commonAttrBuilder.indexOf("?") == -1) {
					commonAttrBuilder.append("?").append(entry.getKey()).append("=")
							.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
				} else {
					// 否则拼接上 &
					commonAttrBuilder.append("&").append(entry.getKey()).append("=")
							.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
				}
			}
		}

		String commonAttr = commonAttrBuilder.toString();
		String op = req.getParameter("op");
		req.setAttribute("commonAttr", commonAttr);

		String reportServerHost = Optional
				.ofNullable(ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "reportServerHost"))
				.orElse("127.0.0.1");
		String reportServerPort = Optional
				.ofNullable(ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "reportServerPort"))
				.orElse("38899");
		String printUrl = req.getScheme() + "://" + reportServerHost + ":" + reportServerPort
				+ "/webroot/decision/view/report" + commonAttr;

		logger.info("帆软服务的url ==> " + printUrl);
		if ("getSessionID".equals(op)) {
			BufferedReader reader = null;
			StringBuffer sbf = new StringBuffer();
			String result = null;
			try {
				// 用java JDK自带的URL去请求
				URL url = new URL(printUrl);
				HttpURLConnection connection = (HttpURLConnection) url.openConnection();
				connection.setRequestMethod("GET");
				connection.setRequestProperty("Accept", "*/*");
				connection.connect();
				InputStream is = connection.getInputStream();
				reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
				String strRead = null;
				while ((strRead = reader.readLine()) != null) {
					sbf.append(strRead);
				}
				reader.close();
				result = sbf.toString();
				PrintWriter out = resp.getWriter();
				logger.info("帆软返回的sessionID ==> " + result);
				out.print(result);
				// 释放PrintWriter对象
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			req.getRequestDispatcher("/reportserver/frReport10.jsp").forward(req, resp);
		}

	}
}

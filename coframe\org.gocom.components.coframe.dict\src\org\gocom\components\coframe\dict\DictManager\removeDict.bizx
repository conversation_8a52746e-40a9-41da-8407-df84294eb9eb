<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="removeDict.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" collapsed="false" nodeType="note" type="note" content="删除业务字典项&#xD;&#xA;&#xD;&#xA;说明：删除选择的字典项和其子项" title="陈鹏&#x9;13-11-29 下午3:33">
    <location x="49" y="352"/>
    <size height="114" width="192"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>transactionbegin0</targetNode>
    </sourceConnections>
    <location x="135" y="126"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link11</targetConnections>
    <targetConnections>link10</targetConnections>
    <location x="973" y="352"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.dict.dict.EosDictEntry[]" name="data" type="query" valueType="DataObject">data</process:return>
      <process:return id="1" language="String" name="status" type="query" valueType="Primitive">status</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="137" y="162"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="975" y="388"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionBegin" id="transactionbegin0" name="事务开始" displayName="事务开始" type="transactionbegin">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactionbegin0</sourceNode>
      <targetNode>loopstart0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="236" y="126"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionbegin0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionbegin0label" name="label" nodeType="label">
    <location x="226" y="162"/>
    <figSize height="17" width="49"/>
    <node>transactionbegin0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionCommit" id="transactioncommit0" name="事务提交" displayName="事务提交" type="transactioncommit">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactioncommit0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="788" y="300"/>
    <size height="28" width="28"/>
    <nodeLabel>transactioncommit0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactioncommit0label" name="label" nodeType="label">
    <location x="778" y="336"/>
    <figSize height="17" width="49"/>
    <node>transactioncommit0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionRollback" id="transactionrollback0" name="事务回滚" displayName="事务回滚" type="transactionrollback">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactionrollback0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <targetConnections>link17</targetConnections>
    <targetConnections>link8</targetConnections>
    <location x="693" y="399"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionrollback0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionrollback0label" name="label" nodeType="label">
    <location x="683" y="435"/>
    <figSize height="17" width="49"/>
    <node>transactionrollback0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="删除字典项" displayName="deleteEntityBatch" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>transactioncommit0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link16</targetConnections>
    <location x="694" y="300"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.deleteEntityBatch</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObjects" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="675" y="336"/>
    <figSize height="17" width="61"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link10" name="link10" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="878" y="300"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">success</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="880" y="336"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="879" y="399"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">fail</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="881" y="435"/>
    <figSize height="17" width="25"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="赋值" displayName="赋值" collapsed="false" grouped="true" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link12" name="link12" displayName="link17" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="420" y="126"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.dict.dict.EosDictEntry</process:from>
      <process:to type="query">criteria/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">parent/seqno</process:from>
      <process:to type="query">criteria/_expr[1]/seqno</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">like</process:from>
      <process:to type="query">criteria/_expr[1]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">end</process:from>
      <process:to type="query">criteria/_expr[1]/_likeRule</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">parent/eosDictType</process:from>
      <process:to type="query">criteria/_expr[2]/dictid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">=</process:from>
      <process:to type="query">criteria/_expr[2]/_op</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="422" y="162"/>
    <figSize height="17" width="25"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="查询子项" displayName="查询叶子节点" collapsed="false" grouped="true" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link14" name="link14" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>invokePojo2</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link12</targetConnections>
    <location x="510" y="126"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">children</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="497" y="162"/>
    <figSize height="17" width="49"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tLoopStart" description="" id="loopstart0" name="循环" displayName="循环" grouped="true" type="loopstart" matchedName="loopend0" loopType="iterate">
    <sourceConnections xsi:type="process:tLink" id="link13" name="link13" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopstart0</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="336" y="128"/>
    <size height="24" width="24"/>
    <nodeLabel>loopstart0label</nodeLabel>
    <process:condition/>
    <process:iterate iterable="data" iterableElement="parent"/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopstart0label" name="label" nodeType="label">
    <location x="336" y="160"/>
    <figSize height="17" width="25"/>
    <node>loopstart0</node>
  </nodes>
  <nodes xsi:type="process:tLoopEnd" id="loopend0" name="循环结束" displayName="循环结束" grouped="true" type="loopend" matchedName="loopstart0">
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopend0</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link15</targetConnections>
    <location x="693" y="128"/>
    <size height="24" width="24"/>
    <nodeLabel>loopend0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopend0label" name="label" nodeType="label">
    <location x="681" y="160"/>
    <figSize height="17" width="49"/>
    <node>loopend0</node>
  </nodes>
  <nodes xsi:type="model:GroupNode" id="group0" name="group" grouped="true" gstart="loopstart0" gend="loopend0">
    <location x="336" y="73"/>
    <size height="151" width="382"/>
    <chidren>loopend0</chidren>
    <chidren>loopstart0</chidren>
    <chidren>assign4</chidren>
    <chidren>invokePojo1</chidren>
    <chidren>invokePojo2</chidren>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="删除子项" displayName="删除字典项" collapsed="false" grouped="true" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>loopend0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="585" y="126"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.deleteEntityBatch</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObjects" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">children</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="572" y="162"/>
    <figSize height="17" width="49"/>
    <node>invokePojo2</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-15 10:50:40" date="2013-12-15Z" description="" name="删除业务字典项" version="6.3"/>
  <process:variables>
    <process:variable description="业务字典项" historyStateLocation="client" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="children"/>
    <process:variable description="业务字典项" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="parent"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="业务字典项" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="data"/>
  </process:inputs>
  <process:outputs>
    <process:output description="业务字典项" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="data"/>
    <process:output description="" isArray="false" name="status" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

﻿<%@page pageEncoding="UTF-8"%>
<%@page import="com.primeton.cap.AppUserManager"%>
<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge"><!-- 使IE浏览器使用 edge 内核渲染页面 -->
<meta name="renderer" content="webkit"><!-- 使360等国产浏览器使用 webkit 内核渲染页面 -->
<meta name="viewport" content="width=device-width, initial-scale=1"><!-- 响应式布局初始化 -->
<!-- 以上标签必须在最顶端 -->
<html>

<style>
    .foot {
        color: #666;
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
    }

    #fs-login-content {
        position: relative;
        /* padding: 0 45px; */
        /* background: black;
        opacity: .5;
        height: 255px */
    }

    h3 {
        /* text-align: center */
    }
	.login-p{
		margin-top:51px;
		margin-left:44px;
		font-size:45px;
		color: rgb(255, 255, 255);
		position:absolute;
	}
	.rightValidate {
            margin: 0px auto;
		    width: 300px;
		    position: absolute;
		    top: -86px;
		    background: #fff;
		    border-radius:2px;
		    height: 251px;
		    padding:7px;
		    text-align: center;
		    z-index: 99;
		    color:#333;
		    display:none;
			box-shadow: 0px 4px 8px #3C5476;
		}
		.v_rightBtn {
        position: absolute;
        left: 0;
        top: 0;
        height: 33px;
        width: 40px;
        background: #5150d4;
        cursor: pointer;
		color:#fff;
    }
    .imgBtn{
        width:50px;
        height: 50px;
        position: absolute;
        left: 0;
        display: block;
       
    }
    .imgBtn img{
        width:100%
    }
    .imgBg{
        position: relative;
        width: 300px;
        height: 170px;
        box-shadow: 0px 4px 8px #3C5476;
    }
    

    .hkinnerWrap{
        border: 1px solid #eee;
        box-sizing:content-box;
        margin-top:10px;
        line-height:33px;
    }
    .green{
        border-color:#5150d4 !important;
        background:#5150d4 !important;
    }
    .green .v_rightBtn{
        background: #5150d4;
        color: #fff;
    }
	.huakuai{
		color: #585555;
	}
	.green .huakuai,.red .huakuai{
		color:#fff;
	}
    .red{
        border-color:red !important;
    }
    .red .v_rightBtn{
        background: red;
        color: #fff;
    }
    .refresh{
        position: absolute;
        width: 30px;
        height: 30px;
        right: 0;
        top: 0;
        font-size: 12px;
        color: #fff;
        text-shadow: 0px 0px 9px #333;
        cursor: pointer;
        display: none;
    }
    .notSel{
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        -webkit-touch-callout: none;
    }
	.abgcolor:hover{
		background:none;
	}
</style>

<head>
    <title>
        <%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%>-登录
    </title>
    <%
   String contextPath = request.getContextPath();
%>
<link rel="shortcut icon" href="<%=contextPath%>/coframe/tools/icons/favicon.ico" />
    <script>
        //直接访问login.jsp之后的跳转
        //location.href="<%=contextPath %>" + "/core/login.jsp";
    </script>
    <!-- <link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/skin1/skin/css/bootstrap/css/bootstrap.min.css" /> -->
    <link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/top.css" />
    <link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/iconfont/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/auth/login/css/login.css" />
	
    <script type="text/javascript">
        if (top != window) {
            top.location.href = top.location.href;
            top.location.reload;
        }
    </script>
</head>
<%
	String original_url=null;
	Object objAttr=request.getAttribute("original_url");
	if(objAttr != null){
		original_url=(String)objAttr;
	}
 %>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <title></title>

<body  class="bg" onload="getHeight()" style="background-image: url(./images/bg.png);">
    <!-- <div id="switcher">
    <div class="center">
        <ul>
            <div id="Device">
                <li class="device-monitor"><a href="javascript:"><div class="icon-monitor"></div></a></li>
                <li class="device-mobile"><a href="javascript:"><div class="icon-tablet"></div></a></li>
                <li class="device-mobile"><a href="javascript:"><div class="icon-mobile-1"></div></a></li>
                <li class="device-mobile-2"><a href="javascript:"><div class="icon-mobile-2"></div></a></li>
                <li class="device-mobile-3"><a href="javascript:"><div class="icon-mobile-3"></div></a></li>
            </div>
            <li class="top2">
                <i class="iconfont icon-android icon-size-lg" style="color: #36ff20;"></i>
                <div class="vm">
                    <div id="outputAndroid"></div>
                    <p style="color:#808080;margin:10px 0 0 0;">扫一扫，直接在手机上打开</p>
                </div>
            </li>
            <li class="top3">
                <i class="iconfont icon-ai-ios icon-size-lg"></i>
                <div class="vm1">
                    <div id="outputIos"></div>
                    <p style="color:#808080;margin:10px 0 0 0;">扫一扫，直接在手机上打开</p>
                </div>
            </li>
            <li class="remove_frame"></li>
        </ul>
    </div>
</div> -->
    <!-- <div class="header">
        <img src="<%=contextPath%><%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImg")%>" width="<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImgWidth")==null?"60":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImgWidth"))%>" alt="" class="header_img">
        <span class="header_title">
		<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameCN")==null?"万序医用耗材院内物流SPD":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameCN"))%>
			
		</span>
    </div> -->
	 <div id ='container' class="container" style="background-image: url(<%=contextPath%>/coframe/auth/login/images/bg.png)">
        <div  style="display:flex;width: 55%; height: 60%;background-size: 100% 100%;position:absolute;left:50%;top: 50%;transform: translate(-50%, -50%);">
            <!--<div style="flex:1;width: 50%;height: 100%;background-image: url(<%=contextPath%>/coframe/auth/login/images/bg_left.png) ;background-size: 100% 100%;">
               <div> <img src="<%=contextPath%><%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImg")%>" width="<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImgWidth")==null?"60":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImgWidth"))%>" alt="" class="header_img"></div>
            </div>-->
			<div style="flex:1;width: 50%;height: 100%;background-image: url(<%=contextPath%>/coframe/auth/login/images/bg_left.png) ;background-size: 100% 100%;">
               <div> <img src="<%=contextPath%><%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImg")%>" width="<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImgWidth")==null?"60":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "headImgWidth"))%>" style="width:290px;height:50px;" alt="" class="header_img"></div>
            </div>
            <div style="flex:1;width: 50%;height: 100%;position: relative;background-image: url(<%=contextPath%>/coframe/auth/login/images/bg_right.png) ;background-size: 100% 100%;">
                <div id="fs-login" style="position: absolute;transform: translate(-50%, -50%);left: 50%;top: 50%;">
                    <!-- <div id="fs-login-glass"></div> -->
                    <div id="fs-login-content">
						<span style='font-size:24px;font-weight: bold;color: #555555;display:block;margin-bottom:-20px'>上海万序</span>
                        <div id="fs-login-title" style="color: #555555;">
                        <%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "str")==null?"万序体外诊断SPD管理软件":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "str"))%>
                        
                        </div>
                        
                        <div class="fs-login-input fs-login-input-username">
                                <span class="iconfont icon-user login_icon"></span>
                                <input tabindex="1" class="fs-login-username" type="text">
                            </div>
                            <div class="fs-login-input fs-login-input-password">
                                <span class="iconfont icon-lock login_icon"></span>
                                <input tabindex="2" class="fs-login-password" type="password">
                                <input type="hidden" class="fs-login-yz" name="yz" id="yz" />
                            </div>
                        <div class="fs-login-checkImg">
                                <a style="color:#fff;color:#0069FF;display:inline-block;padding:8px 0 ;width:100%;border-radius: 24px;border: 1px solid #0069FF" class="btn" onclick="Show()">点击按钮进行验证</a>
                       <div class="comImageValidate rightValidate" >
                            <div style="width:100%;height:30px;">
                                <span style="float:left;">完成拼图验证</span>
                                <span  style="float:right;cursor:pointer"  class="iconfont icon-close" onclick="Close()"></span>
                                <span style="float:right;cursor:pointer;margin-right:20px;color:#367fa9" onclick="validateImageInit()">换一张</span>
                                <span style="float:right;margin-right:5px;cursor:pointer;color:#367fa9" class="iconfont icon-reload" onclick="validateImageInit()"></span>									
                            </div>
                            <div class="imgBg">
                                <div class="imgBtn">
                                    <img />
                                </div>
                            </div>
                            
                            <div class="hkinnerWrap" style="height: 33px;position: relative">
                                <span  class="v_rightBtn "><em class="notSel">→</em></span>
                                <span class="huakuai"  style="font-size: 12px;line-height: 33px;">向右滑动滑块填充拼图</span>
                                <input type = "hidden" name="validX"/>
                            </div> 
                        </div>
                       </div>
                      <a tabindex="3" href="#" id="fs-login-btn">登 录</a>
                        
                        <!-- <div class="fs-login-check">
                        <span class="fs-login-remember fs-login-remember-selected">保持登录状态</span>
                    </div> -->
                    </div>
                </div>
            </div>
            
        </div>
		<!-- <img id="cLeft" src="<%=contextPath%>/coframe/auth/login/images/bg_container.png" height="70%"  alt=""  style="position: absolute;top: 50%;left:50%;transform: translate(-50%,-50%);"> -->
        
        <div class="footer" style="background-color: transparent;color:#fff ;position: fixed;bottom: 0;">
            <%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "copyright")%>
            <a href="工具安装包.rar" class="abgcolor" download="工具安装包.rar" target="view_window"><span style="color:#95EEFF;">工具下载</span></a>
        </div>
	 </div>
    
    <script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
    <script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/jquery.min.js"></script>
    <script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/jquery.qrcode.min.js"></script>
    <script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/com.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/jsencrypt.js"></script>
    <script type="text/javascript">
		function getHeight() {
			document.getElementsByTagName("body")[0].style.height =  window.innerHeight+'px';
			$(".container").height(window.innerHeight);
			//  document.getElementById("cLeft").style.marginTop = '-'+parseInt((window.innerHeight-144)*0.9/2)+"px"
		}
        nui.parse();

    function changeFrameHeight(){
        var ifr= document.getElementById("container");
        ifr.height=document.documentElement.clientHeight;
        console.log(ifr.height,'ifr.height');

    };

    window.onresize=function(){
        // 监听div和屏幕的改变 并修改iframe的高度
       getHeight()
	   };
		
		// var v = $(".header_title").html().split("SPD");
		// $(".header_title").html(v.join('<span style="color:yellow">SPD</span>'));
        // jQuery('#outputAndroid').qrcode({
            // width: 150,
            // height: 150,
            // text: "http://vansys.gicp.net:31110/update/pda/spdapp.apk"
        // });
        // jQuery('#outputIos').qrcode({
            // width: 150,
            // height: 150,
            // text: "http://vansys.gicp.net:31110/update/pda/spdapp.apk"
        // });
        $('.remove_frame').click(function () {
            $('#switcher').hide(100);
        });
		setTimeout(function(){getCode();},500);
        var imgOffsetX, imgOffsetY, loginImgWidth, loginImgHeight, scale;
        //报错蒙板
        var $mask = $('<div class="fs-login-errmask"/>');
        //用户名
        var $username = $('input.fs-login-username').attr("placeholder", "请输入用户名").attr('title', "用户名");
        //密码
        var $password = $('input.fs-login-password').attr("placeholder", "请输入登录密码").attr('title', "密码");
        $('input').focus(function () {
            $(this).parent().addClass('fs-login-input-focus');
            $mask.hide();
        }).blur(function () {
            $(this).parent().removeClass('fs-login-input-focus');
        });
        //是否保持登录状态
        var $keep = $('span.fs-login-remember').click(
            function () {
                $(this).toggleClass('fs-login-remember-selected');
            }
        );
        //登录按钮
        $('#fs-login-btn').click(
            function () {
                signIN();
            }
        );
        //绑定回车
        $(document).keydown(function (e) {
            if (e.keyCode === 13) {
                signIN();
            }
        });
        /**
         * 初始化FS的登录背景图片
         */
        var initBackgroundImage = function () {
            var self = this;
            var url = "<%=request.getContextPath() %>/coframe/auth/login/images/logins.jpg";
            loginImgWidth = 1920;
            loginImgHeight = 1080;
            var ran = new Date().getTime() + "" + (Math.random() * 1000);
            if ($('body').length > 0) {
                var loginImg = $('img.fs-login-img');
                loginImg.attr("src", url);
                loginImg.css({
                    "margin-left": "-" + imgOffsetX + "px",
                    "margin-top": "-" + imgOffsetY + "px",
                    width: loginImgWidth * scale + "px",
                    height: loginImgHeight * scale + "px"
                });

                var offset = $('#fs-login-scalebg').offset();
                var loginScaleBgImg = $('img.fs-login-scalebg-img');
                loginScaleBgImg.attr("src", url);
                loginScaleBgImg.css({
                    "margin-left": "-" + (imgOffsetX + offset.left) + "px",
                    "margin-top": "-" + (imgOffsetY + offset.top) + "px",
                    width: loginImgWidth * scale + "px",
                    height: loginImgHeight * scale + "px"
                });
            }
        };

        var calcBackgroundScale = function () {
            var windowWidth = document.body.clientWidth;
            var windowHeight = document.body.clientHeight;

            if (windowWidth / windowHeight >= loginImgWidth / loginImgHeight) {
                scale = windowWidth / loginImgWidth;
                imgOffsetX = 0;
                imgOffsetY = (loginImgHeight * scale - windowHeight) / 2;
            } else {
                scale = windowHeight / loginImgHeight;
                imgOffsetX = (loginImgWidth * scale - windowWidth) / 2;
                imgOffsetY = 0;
            }
        };

        var showErrorMsg = function ($pos, msg) {
            $mask.hide().insertAfter($pos).text(msg);
            $mask.click(function () {
                $(this).fadeOut();
                $pos.select();
            }).fadeIn();
        };
		
		//用户名密码加密
		var publicKey = null;
		function getCode(){
		mini.ajax({
			url: "org.gocom.components.coframe.auth.LoginManager.getRSAKey.biz.ext",
			//data: sendData,
			type: "post",
			cache:false,
			async:false,
			contentType:'text/json',
			success: function (result) {
				publicKey = result.pubkey;
			},
			error: function (jqXHR, textStatus, errorThrown) {
				mini.alert(jqXHR.responseText);
			}
		 });
		}
		//验证登录次数限制
		var loginNum = null;
		function getLoginNum(){
		var user = $username.val();
		var data = {"userid":user};
		mini.ajax({
			url: "org.gocom.components.coframe.auth.LoginManager.checkLoginNum.biz.ext",
			data: data,
			type: "post",
			cache:false,
			async:false,
			contentType:'text/json',
			success: function (result) {
				loginNum = result.rsMap;
			},
			error: function (jqXHR, textStatus, errorThrown) {
				mini.alert(jqXHR.responseText);
			}
		 });
		}
		
		//登录图片验证
		var logCheck = null;
		function getLogCheck(){
		var data = {"configname":"REMIMGVAL","deptid":"1"};
		mini.ajax({
			url: "org.gocom.components.coframe.auth.newLoginManager.getConfigurationUtil.biz.ext",
			data: data,
			type: "post",
			cache:false,
			async:false,
			contentType:'text/json',
			success: function (result) {
				logCheck = result.falg;
			},
			error: function (jqXHR, textStatus, errorThrown) {
				mini.alert(jqXHR.responseText);
			}
		 });
		}
		
        var signIN = function () {
            $mask.hide();
            var userOld = $username.val();
            var pwOld = $password.val();
            //用户名为空
            if (userOld == '') {
                showErrorMsg($username, "用户名不能为空");
                return;
            }
            //密码为空
            if (pwOld == '') {
                showErrorMsg($password, "密码不能为空");
                return;
            }
			var encrypt = new JSEncrypt();
			encrypt.setPublicKey(publicKey);
			var user = encrypt.encrypt(userOld);
			var pw = encrypt.encrypt(pwOld);
			//登录次数
			getLoginNum();
			if(loginNum.loginflag =="FALSE"){
				nui.alert("登录错误次数超过3次! 请在"+loginNum.misstime+"后登录!");
				return;
			}            
			//登录图片验证	
			getLogCheck();
			if(logCheck == false){
				if(!$("#yz").val()){
					nui.alert("请先完成验证!");
					//showErrorMsg($yz,"请先完成验证!");
					return;
				}
			}
            var json = {
                "userId": user,
                "password": pw
            };
            nui.ajax({
                url: "org.gocom.components.coframe.auth.LoginManager.weblogin.biz.ext",
                type: 'post',
                data: json,
                success: function (text) {
                    var o = nui.decode(text);
                    if (o.exception == null) {
                        var ret = o.retCode;
                        var times = new Date().getTime();
                        if (ret == 1) {
                            //location.href="<%=request.getContextPath() %>/coframe/auth/login/redirect.jsp?original_url=<%=original_url %>";
                            location.href =
                                "<%=request.getContextPath() %>/coframe/auth/skin1/index.jsp?time=" +
                                times;
                        } else if (ret == 0) {						
						   nui.alert("用户名或密码错误！");
                        } else {
                            nui.alert(o.msg);
                        }
                    } else {
                        nui.alert("登录系统出错");
                    }
                }
            });

        };
        // initBackgroundImage();
        $username.focus();
        $(window).resize(function () {
            calcBackgroundScale();
            $('img.fs-login-img').css({
                "margin-left": "-" + imgOffsetX + "px",
                "margin-top": "-" + imgOffsetY + "px",
                width: loginImgWidth * scale + "px",
                height: loginImgHeight * scale + "px"
            });
            var offset = $('#fs-login-scalebg').offset();
            $('img.fs-login-scalebg-img').css({
                "margin-left": "-" + (imgOffsetX + offset.left) + "px",
                "margin-top": "-" + (imgOffsetY + offset.top) + "px",
                width: loginImgWidth * scale + "px",
                height: loginImgHeight * scale + "px"
            });
        });
		
			<% 						 String  param = null ;		
								try {
                                	  param = (String)request.getSession().getServletContext().getAttribute("ISAUTH");
								   } catch (Exception e) {
									   System.out.println("--------login.jsp 页面报错了------------");
								   }			
								   %>
			var authorg = {};
		   authorg = '<%=param%>';
		if(authorg!=null&&authorg!="null"&&authorg!=""){
			authorg = mini.decode(authorg, false);
			console.info(authorg);
			var showwin = "0";
			if (authorg.ISAUTH == 'TRUE') {
			
			} else if (authorg.ISAUTH == 'FALSE') {
				 nui.alert("系统未授权！并发数受到限制！");
			}
		}				
		
		function Close(){
     	$(".rightValidate").hide();
    }
	function Show(){
        $mask.hide();
            var userOld = $username.val();
            var pwOld = $password.val();
            //用户名为空
            if (userOld == '') {
                showErrorMsg($username, "用户名不能为空");
                return;
            }
            //密码为空
            if (pwOld == '') {
                showErrorMsg($password, "密码不能为空");
                return;
            }
		$(".rightValidate").show();
     		initValidate();
     	
    }
	
	function initValidate(){
      $("#yz").val("");
     validateImageInit();
	 var isMouseDown;
        $('.v_rightBtn').on({
            mousedown: function(e) {
				isMouseDown= true;
				$(".huakuai").html("");
					$(".hkinnerWrap").removeClass("red green")
					var el = $(this);
					var os = el.offset();
					dx = e.pageX - os.left;
					$(this).parents(".hkinnerWrap").off('mousemove');
					$(this).parents(".hkinnerWrap").on('mousemove', function(e) {
						var newLeft=e.pageX - dx;
							el.offset({
								left: newLeft
							});
							var newL=parseInt($(".v_rightBtn").css("left"));
							if(newL<=0){
								newL=0;
							}else if (newL>=248){
								newL=248;
							}
							$(".v_rightBtn").css("left",newL+"px");
							$(".imgBtn").offset({
								left: newLeft
							});
							$(".imgBtn").css("left",newL+"px")
						
					}).on('mouseup', function(e) {
						//$(document)
						$(this).off('mousemove');
					})
                
            }
        }).on("mouseup",function () {
            $(this).parents(".hkinnerWrap").off('mousemove');
            var l=$(this).css("left");
            if(l.indexOf("px")!=-1){
                l=l.substring(0,l.length-2);
            }
            x = l;
			console.log(x);
            submitDate(l,y)
        })
     }
	 
	 $(".comImageValidate").ready(function () {
       initValidate();

    });
    /*图形验证*/
    function submitDate(x,y) {
        $.ajax({
            url:"org.gocom.components.coframe.auth.newLoginManager.verifyImageCode.biz.ext",
            dataType:'json',
            type: "POST",
            data:{"x":x},
            success:function (data) {
                if(data.out1.errcode=="0"){
                    $(".hkinnerWrap").addClass("green").removeClass("red");
                    $(".hkinnerWrap input[name='validX']").val(x);
                    $("#yz").val("1");
                    $(".huakuai").html("验证通过！");
                    $(".v_rightBtn ").hide();
                    
                    
                } else {
                    $(".hkinnerWrap").addClass("red").removeClass("green");
                     setTimeout(function(){
                         $(".hkinnerWrap").removeClass("red green");
                         $(".v_rightBtn").css("left",0);
                         $(".imgBtn").css("left",0);
                     },500)
                    validateImageInit();
                }
            }
        })
    }
	
	/*初始化图形验证码*/
    function validateImageInit() {
     	$.ajax({
				url: "org.gocom.components.coframe.auth.newLoginManager.getImageVerify.biz.ext",
				type: "POST",
               cache:false,
              success:function (data) {
              	$(".v_rightBtn ").show();
                $(".huakuai").html("向右滑动滑块填充拼图");
                $(".imgBg").css("background",'#fff url("data:image/jpg;base64,'+data.out1.bigImage+'")');
                $(".imgBtn").css('top',data.out1.yHeight+'px');
                $(".imgBtn").find("img").attr("src","data:image/png;base64,"+data.out1.smallImage);
                y=data.out1.yHeight;
                $(".hkinnerWrap").removeClass("red green");
                $(".v_rightBtn").css("left",0);
                $(".imgBtn").css("left",0);
            },error:function(err){
                console.log(err)
            }
        })
    }
    </script>

    <!--[if IE 6]>
<script language="javascript" type="text/javascript" src="./script/ie6_png.js"></script>
<script language="javascript" type="text/javascript">
DD_belatedPNG.fix(".png");
</script>
<![endif]-->


</body>

</html>
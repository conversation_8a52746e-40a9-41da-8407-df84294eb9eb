package org.gocom.components.coframe.flowconfig.authconfig;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Properties;

import org.gocom.components.coframe.auth.login.EOSlock;

import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;

public class AuthConfig {

	private static Logger logger = TraceLoggerFactory.getLogger(AuthConfig.class);
	
	public static boolean isOSLinux() {
		Properties prop = System.getProperties();
		String os = prop.getProperty("os.name");
		if (os != null && os.toLowerCase().indexOf("linux") > -1) {
			return true;
		} else {
			return false;
		}
	}
	
	public String getAppPath() {
		String nodepath = this.getClass().getClassLoader().getResource("/").getPath();
		int startIndex = isOSLinux() ? 0 : 1;
		return nodepath.substring(startIndex, nodepath.length() - 16);
	}

	private String getJspText(String path) {
		path = this.getAppPath() + path;
		logger.info("文件路径："+ path);
		StringBuffer text = new StringBuffer("");
		try {
			FileInputStream fis = new FileInputStream(path);
			InputStreamReader isr = new InputStreamReader(fis, "UTF-8");
			BufferedReader reader = new BufferedReader(isr);
			String ch = reader.readLine();
			while (ch != null) {
				text.append(ch);
				ch = reader.readLine();
			}
			reader.close();
			isr.close();
			fis.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return text.toString().replaceAll("\\s*", "");
	}

	public String getLoginJumpUrl() {
		String path = "coframe/auth/login/login.jsp";
		String strbegin = ";location.href=\"<%=request.getContextPath()%>";
		String strend = "?time=\"+times;";
		return this.subString(strbegin, strend, path);
	}

	public String subString(String begin, String end, String path) {
		String text = this.getJspText(path);
		int beginOf = text.lastIndexOf(begin) + begin.length();
		int endOf = text.lastIndexOf(end);
		String txt = "";
		if (beginOf > 0 && endOf > 0) {
			txt = text.substring(beginOf, endOf);
		}
		return txt;
	}

	public String getIndexText() {
		String path = this.getLoginJumpUrl();
		if ("".equals(path))
			return "";
		return this.getJspText(path);
	}

	@Bizlet
	public boolean checkIndex() {
		return true;
//		String str1 = "Stringparam=(String)request.getSession().getServletContext().getAttribute(\"ISAUTH\");";
//		String str2 = "if(authorg!=null&&authorg.ISAUTH=='TRUE'){if(Number(authorg.COUNTDOWNDATE)!=null&&Number(authorg.COUNTDOWNDATE)>-1&&Number(authorg.COUNTDOWNDATE)<Number(lastDays)){mini.alert(\"距离授权到期时间还有\"+authorg.COUNTDOWNDATE+\"天，请提醒管理员及时更新授权！\");}}elseif(authorg.ISAUTH=='FALSE'){checkSessionjava(authorg);}elseif(authorg.ISAUTH=='EXPIRE'){checkSessionjava(authorg);mini.alert(\"授权已到期，请提醒管理员及时更新授权！\");}functioncheckSessionjava(f){mini.open({targetWindow:window,//页面对象。默认是顶级页面。url:'Authorization.html',//页面地址title:'授权',//标题iconCls:'',//标题图标width:400,//宽度height:340,//高度allowResize:false,//允许尺寸调节allowDrag:false,//允许拖拽位置showCloseButton:false,//显示关闭按钮showMaxButton:false,//显示最大化按钮showModal:false,//显示遮罩loadOnRefresh:false,//true每次刷新都激发onload事件onload:function(){variframe=this.getIFrameEl();vardata=f;//调用弹出页面方法进行初始化iframe.contentWindow.SetData(data);},ondestroy:function(action){//弹出页面关闭前if(action==\"1\"){window.location.href=\"../login/logout.jsp\";}else{setTimeout('checkSessionjava()',1800000);}}});}";
//		String text = this.getIndexText();
//		if (text.indexOf(str1) > 0 && text.indexOf(str2) > 0) {
//			return this.checkAuthorization();
//		}
//		logger.info("首页被篡改！");
//		return false;
	}

	public boolean checkAuthorization() {
		EOSlock.checkAuth();
		String path = this.getLoginJumpUrl();
		logger.info("getLoginJumpUrl：" + path);
		if ("".equals(path))
			return false;
		String JumpName = path.substring(path.lastIndexOf("/") + 1, path.length());
		String newUrl = path.replaceAll(JumpName, "Authorization.html");
		String str = "<!DOCTYPEhtml><htmlxmlns=\"http://www.w3.org/1999/xhtml\"><head><title>授权</title><metacharset=\"utf-8\"><metaname=\"viewport\"content=\"width=device-width,initial-scale=1.0\"><metahttp-equiv=\"X-UA-Compatible\"content=\"IE=edge\"><metaname=\"keywords\"content=\"\"><metaname=\"description\"content=\"\"><!--linkcss--><linkrel=\"stylesheet\"href=\"./js/layui/css/layui.css\"></head><body><divstyle=\"text-align:center;\"><divclass=\"content\"style=\"padding-top:10%;padding:10px\"><h5style=\"line-height:30px;\"id=\"noAuth\">您的系统<spanstyle=\"color:red;font-weight:bold;\">未被授权</span>，请提供注册信息并联系</br><spanstyle=\"font-weight:bold;\">万序健康&nbsp;021-55155381</span>获取许可证书。</h5><divid=\"isAuth\"style=\"display:none;\">您的系统<spanstyle=\"color:red;font-weight:bold;\">已授权</span>，如需更改请提供注册信息并联系</br><spanstyle=\"font-weight:bold;\">万序健康&nbsp;021-55155381</span>获取许可证书。</div><h4style=\"line-height:30px;padding-bottom:10px;\">注册信息:</h4><h5><formclass=\"layui-form\"action=\"\"><divclass=\"layui-form-item\"><labelclass=\"layui-form-label\">项目名称</label><divclass=\"layui-input-block\"><inputtype=\"text\"name=\"projectName\"id=\"projectName\"lay-verify=\"required\"autocompvare=\"off\"placeholder=\"请输入项目名称\"class=\"layui-input\"></div></div><divclass=\"layui-form-item\"><labelclass=\"layui-form-label\">Mac地址</label><divclass=\"layui-input-block\"><inputtype=\"text\"name=\"macAddress\"id=\"macAddress\"lay-verify=\"macAddress\"autocompvare=\"off\"disabledplaceholder=\"请输入Mac地址\"class=\"layui-input\"></div></div><divclass=\"layui-form-item\"><labelclass=\"layui-form-label\">机器码</label><divclass=\"layui-input-block\"><textareaplaceholder=\"请输入请求码\"class=\"layui-textarea\"style=\"min-height:55px;\"lay-verify=\"required\"disabledid=\"machineCode\"name=\"machineCode\"></textarea></div></div><div><buttontype=\"submit\"class=\"layui-btn\"lay-submitlay-filter=\"generateReq\">导出注册信息</button><buttontype=\"button\"class=\"layui-btnlayui-btn-normal\"id=\"uploadLic\"><iclass=\"layui-icon\"></i>上传lic文件</button><buttontype=\"button\"class=\"layui-btn\"style=\"margin-left:10px;\"title=\"重置项目名称\"id=\"reset\"><iclass=\"layui-iconlayui-icon-refresh\"></i></button></div></form></h5></div></div><scriptsrc=\"./js/jquery.min.js\"></script><scriptsrc=\"./js/layui/layui.js\"></script><script>layui.use(['element','form','upload'],function(){var$=layui.jquery,element=layui.element,form=layui.form,layer=layui.layer,upload=layui.upload;//请求信息文件form.on('submit(generateReq)',function(data){deletedata.field.file;data.field.projectName=encodeURIComponent(encodeURIComponent(data.field.projectName));varsendObj=JSON.stringify(data.field);varuri=\"org.gocom.components.coframe.license.bean.dowloadRegInfo.flow?license=\"+sendObj;window.location.href=uri;returnfalse;});//重置项目名称$(\"#reset\").click(function(){$('#projectName').val('');$(\"#projectName\").attr(\"disabled\",false);layer.alert(\"重置成功，请重新输入并生成注册信息\",{icon:1,skin:'layer-ext-demo',title:\"提示\"});});//lic文件上传upload.render({elem:'#uploadLic',url:'org.gocom.components.coframe.license.bean.uploadLicense.flow',accept:'file',exts:'lic',//只允许上传lic文件before:function(obj){//obj参数包含的信息，跟choose回调完全一致，可参见上文。layer.load();//上传loading},error:function(index,upload){layer.closeAll('loading');//关闭loading},done:function(res){layer.closeAll('loading');//关闭loadingif(res.flag=='true'){layer.alert(\"授权成功！请重新登录\",{icon:1,skin:'layer-ext-demo',title:\"提示\"},function(index){window.CloseOwnerWindow(1);layer.close(index);});}elseif(res.flag=='expire'){layer.alert(\"lic文件已过期！请重新申请\",{icon:2,skin:'layer-ext-demo',title:\"提示\"});}else{layer.alert(\"授权失败！请检查lic文件\",{icon:2,skin:'layer-ext-demo',title:\"提示\"});}}});})$(document).ready(function(){init();})functionSetData(data){$('#content').hide();if(data){if(data.ISAUTH=='TRUE'){$('#noAuth').hide();$('#isAuth').show();isUpdate=true;}$('#content').show();$('#macAddress').val(data.MACADDRESS);$('#machineCode').val(data.MACHINECODE);if(data.PROJECTNAME&&data.PROJECTNAME!=undefined&&data.PROJECTNAME!=''){$('#projectName').val(data.PROJECTNAME);$(\"#projectName\").attr(\"disabled\",true);}}}functioninit(){SetData(GetQueryString(\"authorg\"));}functionGetQueryString(name){varreg=newRegExp(\"(^|&)\"+name+\"=([^&]*)(&|$)\");varr=window.location.search.substr(1).match(reg);if(r!=null)returnunescape(r[2]);returnnull;}</script></body></html>";
		String text = this.getJspText(newUrl);
		if (text.indexOf(str) == 0) {
			return true;
		}
		logger.info("授权页被篡改！");
		return false;
	}

}

package org.gocom.components.coframe.auth.login;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.util.Map;

import org.apache.commons.httpclient.HttpStatus;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.logging.Logger;

/**
 * <AUTHOR> wangc
 * @date : 2020/6/23
 */
public final class SSOUtil {
	
	private static Logger logger = TraceLoggerFactory.getLogger(SSOUtil.class);
	
	private static final char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

	public static String encode(File file) {
		FileInputStream in = null;
		MessageDigest md5 = null;
		try {
			in = new FileInputStream(file);
			FileChannel ch = in.getChannel();
			MappedByteBuffer byteBuffer = ch.map(FileChannel.MapMode.READ_ONLY, 0, file.length());
			md5 = MessageDigest.getInstance("MD5");
			md5.update(byteBuffer);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return toHex(md5.digest());
	}

	public static String encode(String arg) {
		if (arg == null) {
			arg = "";
		}
		MessageDigest md5 = null;
		try {
			md5 = MessageDigest.getInstance("MD5");
			md5.update(arg.getBytes("UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return toHex(md5.digest());
	}

	private static String toHex(byte[] bytes) {
		StringBuffer str = new StringBuffer(32);
		for (byte b : bytes) {
			str.append(hexDigits[(b & 0xf0) >> 4]);
			str.append(hexDigits[(b & 0x0f)]);
		}
		return str.toString();
	}

	public static String sendGet(String url) {
		String result = "";
		BufferedReader in = null;
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection connection = realUrl.openConnection();
			// 设置通用的请求属性
			connection.setRequestProperty("Content-Type", "application/json");
			connection.setRequestProperty("accept", "*/*");
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			// 建立实际的连接
			connection.connect();
			// 定义 BufferedReader输入流来读取URL的响应
			in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			System.out.println("发送GET请求出现异常！" + e);
			e.printStackTrace();
		}
		// 使用finally块来关闭输入流
		finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e2) {
				e2.printStackTrace();
			}
		}
		System.out.println(result);

		return result;
	}

	/**
	 * post请求
	 * 
	 * @param url
	 * @param json
	 * @return
	 */
	public static JSONObject doPost(String url, JSONObject json) {
		logger.info("发送 POST 请求 BEGIN，url=" + url + ",params=" + JSON.toJSONString(json));
		DefaultHttpClient client = new DefaultHttpClient();
		HttpPost post = new HttpPost(url);
		JSONObject response = null;
		try {
			StringEntity s = new StringEntity(json.toString());
			s.setContentEncoding("UTF-8");
			s.setContentType("application/json");// 发送json数据需要设置contentType
			post.setEntity(s);
			HttpResponse res = client.execute(post);
			if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				HttpEntity entity = res.getEntity();
				String result = EntityUtils.toString(entity, "UTF-8");// 返回json格式：
				response = JSONObject.parseObject(result);
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		logger.info("发送 POST 请求 END，url=" + url + ",response=" + JSON.toJSONString(response));
		return response;
	}
	
	/**
	 * post请求
	 * 
	 * @param url 请求地址
	 * @param json 请求参数
	 * @param headers 请求头，可为null
	 * @return
	 */
	public static JSONObject doPost(String url, JSONObject json, Map<String, String> headers) {
	    logger.info("发送 POST 请求 BEGIN，url=" + url + ",params=" + JSON.toJSONString(json) + ",headers=" + (headers != null ? headers.toString() : "null"));
	    DefaultHttpClient client = new DefaultHttpClient();
	    HttpPost post = new HttpPost(url);
	    JSONObject response = null;
	    try {
	        // 设置请求头
	        if (headers != null && !headers.isEmpty()) {
	            for (Map.Entry<String, String> entry : headers.entrySet()) {
	                post.addHeader(entry.getKey(), entry.getValue());
	            }
	        }
	        
	        StringEntity s = new StringEntity(json.toString());
	        s.setContentEncoding("UTF-8");
	        s.setContentType("application/json");// 发送json数据需要设置contentType
	        post.setEntity(s);
	        HttpResponse res = client.execute(post);
	        if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
	            HttpEntity entity = res.getEntity();
	            String result = EntityUtils.toString(entity, "UTF-8");// 返回json格式：
	            response = JSONObject.parseObject(result);
	        }
	    } catch (Exception e) {
	        throw new RuntimeException(e);
	    }
	    logger.info("发送 POST 请求 END，url=" + url + ",response=" + JSON.toJSONString(response));
	    return response;
	}
	
}
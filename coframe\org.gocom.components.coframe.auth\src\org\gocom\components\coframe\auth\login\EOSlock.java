package org.gocom.components.coframe.auth.login;

import java.io.File;
import java.net.SocketException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

import org.gocom.components.coframe.flowconfig.authconfig.AuthConfig;
import org.gocom.components.coframe.license.AuthorizationService;
import org.gocom.components.coframe.license.AuthorizationUtils;

import com.eos.foundation.database.DatabaseExt;
import com.eos.foundation.eoscommon.ConfigurationUtil;
import com.eos.runtime.core.ApplicationContext;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;
import com.primeton.bfs.engine.json.JSONException;
import com.primeton.bfs.engine.json.JSONObject;

public class EOSlock {

	static Logger logger = TraceLoggerFactory.getLogger(EOSlock.class);

	private static String macaddrs;// 服务器mac地址
	private static String autocode;// 授权码
	private static String isauth; // 是否授权
	private static String projectname;// 项目名称
	private static String authdate; // 授权时间
	private static String authexpdate;// 有效期
	private static String lsjzs; // 专属套餐
	private static String countdowndate;// 授权剩余日期

	public static String getCountdowndate() {
		return countdowndate;
	}

	public static void setCountdowndate(String countdowndate) {
		EOSlock.countdowndate = countdowndate;
	}

	private static String istruedate;

	public static String getIstruedate() {
		return istruedate;
	}

	public static void setIstruedate(String istruedate) {
		EOSlock.istruedate = istruedate;
	}

	private static boolean istrue;

	public static void setIstrue(boolean istrue) {
		EOSlock.istrue = istrue;
	}

	@Bizlet("校验应用是否授权保存到session")
	public static void checkandSetsession_old() {
		JSONObject json = new JSONObject();
		AuthConfig aut = new AuthConfig();
		try {
			// json.put("ISAUTH", EOSlock.ischeck() ? aut.checkIndex() ? "TRUE"
			// : "FALSE" : "FALSE");
			json.put("MACADDRESS", Utils.getLocatMac(""));
			json.put("COUNTDOWNDATE", EOSlock.getCountdowndate());
		} catch (JSONException | SocketException e) {
			System.out.println("解析授权信息发生错误" + e.getMessage());
			try {
				json.put("ISAUTH", "FALSE");
				json.put("MACADDRESS", Utils.getLocatMac(""));
			} catch (JSONException | SocketException e1) {
				e1.printStackTrace();
			}
		}
		System.out.println(json.toString());
		LoginService.setApplication("ISAUTH", json.toString());
	}

	@Bizlet("校验应用是否授权保存到session")
	public static void checkandSetsession() {
		JSONObject json = new JSONObject();
		AuthConfig aut = new AuthConfig();
		try {
			if (EOSlock.ischeck().equals("expire")) {
				json.put("ISAUTH", "EXPIRE");
				json.put("MACADDRESS", AuthorizationUtils.getMac());
				json.put("MACHINECODE", AuthorizationUtils.getMachineCode());
				json.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
				json.put("COUNTDOWNDATE", EOSlock.ischeck().equals("true") ? EOSlock.getExpireDay() : "");
				json.put("EXPRIEDATE", AuthorizationUtils.getExpireTime());
			} else {
				json.put("ISAUTH", EOSlock.ischeck().equals("true") ? aut.checkIndex() ? "TRUE" : "FALSE" : "FALSE");
				json.put("MACADDRESS", AuthorizationUtils.getMac());
				json.put("MACHINECODE", AuthorizationUtils.getMachineCode());
				json.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
				json.put("COUNTDOWNDATE", EOSlock.ischeck().equals("true") ? EOSlock.getExpireDay() : "");
				json.put("EXPRIEDATE", AuthorizationUtils.getExpireTime());

			}
		} catch (JSONException e) {
			logger.error("解析授权信息发生错误" + e.getMessage());
			try {
				json.put("ISAUTH", "FALSE");
				json.put("MACADDRESS", AuthorizationUtils.getMac());
				json.put("MACHINECODE", AuthorizationUtils.getMachineCode());
				json.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
			} catch (JSONException e1) {
				e1.printStackTrace();
			}
		}
		logger.info(json.toString());
		LoginService.setApplication("ISAUTH", json.toString());
	}

	@Bizlet
	public static HashMap<String, String> getAuthInfo() {
		AuthConfig aut = new AuthConfig();
		HashMap<String, String> map = new HashMap<>();
		String version = ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "version");
		if (EOSlock.ischeck().equals("expire")) {
			map.put("ISAUTH", "EXPIRE");
			map.put("MACADDRESS", AuthorizationUtils.getMac());
			map.put("MACHINECODE", AuthorizationUtils.getMachineCode());
			map.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
			map.put("COUNTDOWNDATE", EOSlock.ischeck().equals("true") ? EOSlock.getExpireDay() : "");
			map.put("VERSION", version);
			map.put("EXPRIEDATE", String.valueOf(AuthorizationUtils.getExpireTime()));
		} else {
			map.put("ISAUTH", EOSlock.ischeck().equals("true") ? aut.checkIndex() ? "TRUE" : "FALSE" : "FALSE");
			map.put("MACADDRESS", AuthorizationUtils.getMac());
			map.put("MACHINECODE", AuthorizationUtils.getMachineCode());
			map.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
			map.put("COUNTDOWNDATE", EOSlock.ischeck().equals("true") ? EOSlock.getExpireDay() : "");
			map.put("VERSION", version);
			map.put("EXPRIEDATE", String.valueOf(AuthorizationUtils.getExpireTime()));
		}
		return map;
	}

	public static String getExpireDay() {
		String expireTime = String.valueOf(AuthorizationUtils.getExpireTime());
		if (expireTime != null) {
			int days = (int) ((Long.valueOf(expireTime) - new Date().getTime()) / (1000 * 3600 * 24));
			return String.valueOf(days + 1);
		}
		return String.valueOf(-1);
	}

	static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

	public static String authResultString = null;

	/**
	 * 校验应用是否授权
	 * 
	 * @return
	 */
	public static String ischeck() {
		String filePath = ApplicationContext.getInstance().getApplicationConfigPath() + File.separator + "vanxsoft.lic";
		if (authResultString == null) {
			authResultString = AuthorizationService.activateLicFile(filePath);
		}
		return authResultString;
	}

	public static void checkAuth() {
		String filePath = ApplicationContext.getInstance().getApplicationConfigPath() + File.separator + "vanxsoft.lic";
		authResultString = AuthorizationService.activateLicFile(filePath);
	}

	/**
	 * 校验应用是否授权 - 旧版本
	 * 
	 * @return
	 */
	public static boolean ischeck_old() {
		String newdate = format.format(new Date());
		if ((EOSlock.getAutocode() != null && EOSlock.getProjectname() != null && EOSlock.getAuthdate() != null) || (EOSlock.getLsjzs() != null && "".equals(EOSlock.getLsjzs()))) {
			if (EOSlock.getIstruedate() == null)
				return getEosLockcheck();
			try {
				if (format.parse(EOSlock.getIstruedate()).getTime() - format.parse(newdate).getTime() != 0)
					return getEosLockcheck();
			} catch (ParseException e) {
				e.printStackTrace();
				return getEosLockcheck();
			}
			return istrue == false ? getEosLockcheck() : istrue;
		}
		return getEosLockcheck();
	}

	/**
	 * 应用授权
	 * 
	 * @param projectname 应用名称
	 * @param autocode 授权码
	 * @return
	 */
	@Bizlet("应用授权")
	public static boolean eoslockinsert(String projectname, String authcode) {
		HashMap<String, String> param = new HashMap<String, String>();
		param.put("projectname", projectname);
		param.put("authcode", authcode);
		String mac = "";
		try {
			mac = Utils.getLocatMac("");
			param.put("macaddrs", mac);
			param.put("authexpdate", format.format(MD5.getXqDateBytime(format.format(new Date()), MD5.getxqdate(authcode))));
			String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
			// 当前时间 + (项目名 + MAC地址) + 有效期限 + 秘钥
			if (MD5.keyCheck(date + projectname + mac + MD5.getxqdate(authcode), authcode)) {
				DatabaseExt.executeNamedSql("default", "org.gocom.components.coframe.auth.locknamingsql.inserteosdictlock", param);
			} else {
				return false;
			}
			getEosLockcheck();
			return true;
		} catch (SocketException e) {
			return false;
		}
	}

	@Bizlet
	public static boolean getEosLockcheck() {
		Object[] obj = DatabaseExt.queryByNamedSql("default", "org.gocom.components.coframe.auth.locknamingsql.selecteosdictlock", null);
		if (obj == null || obj.length < 1)
			return false;
		setEosLock((HashMap) obj[0]);
		for (int i = 1; i < obj.length; i++) {
			if (!istrue) {
				setEosLock((HashMap) obj[i]);
			}
		}
		return istrue;
	}

	/**
	 * 是否第一次授权
	 * 
	 * @return
	 */
	public static boolean getFirstAuth() {
		Object[] obj = DatabaseExt.queryByNamedSql("default", "org.gocom.components.coframe.auth.locknamingsql.selecteosdictlock", null);
		if (obj == null || obj.length < 1)
			return true;
		return false;
	}

	public static void setEosLock(HashMap map) {
		try {
			EOSlock.setMacaddrs(Utils.getLocatMac(map.get("MACADDRS").toString()));
			EOSlock.setAutocode(map.get("AUTHCODE").toString());
			EOSlock.setIsauth(map.get("ISAUTH").toString());
			EOSlock.setProjectname(map.get("PROJECTNAME").toString());
			EOSlock.setAuthdate(map.get("AUTHDATE").toString());
			EOSlock.setAuthexpdate(map.get("AUTHEXPDATE").toString());
			EOSlock.setLsjzs(map.get("LSJZS") == null ? "" : map.get("LSJZS").toString());
			EOSlock.setCountdowndate(map.get("COUNTDOWNDATE") == null ? "0" : map.get("COUNTDOWNDATE").toString());
			EOSlock.setIstrue(false);
			EOSlock.setIstruedate(format.format(new Date()).toString());
			if (macCheck()) {
				EOSlock.setIstrue(true);
				if (!MD5.datecheck(EOSlock.getAuthdate(), EOSlock.getAutocode())) {
					EOSlock.setIstrue(false);
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			EOSlock.setIstrue(false);
		}
	}

	/**
	 * 授权时间+项目名称+项目授权码验证
	 * 
	 * @return
	 */
	public static boolean macCheck() {
		StringBuffer str = new StringBuffer();
		str.append(EOSlock.getAuthdate());
		str.append(EOSlock.getProjectname() == null ? "" : EOSlock.getProjectname());
		str.append(EOSlock.getMacaddrs());
		try {
			str.append(String.valueOf(format.parse(EOSlock.getAuthexpdate()).getTime() - format.parse(EOSlock.getAuthdate()).getTime()));
		} catch (ParseException e) {
			e.printStackTrace();
			return false;
		}
		return MD5.keyCheck(str.toString(), EOSlock.getAutocode());
	}

	/**
	 * 老司机专属验证 授权时间+万序软件+lsjzs
	 * 
	 * @return
	 */
	public static boolean lsjzsCheck() {
		StringBuffer str = new StringBuffer();
		str.append(EOSlock.getAuthdate());
		str.append("万序软件");
		str.append("lsjzs");
		try {
			str.append(String.valueOf(format.parse(EOSlock.getAuthexpdate()).getTime() - format.parse(EOSlock.getAuthdate()).getTime()));
		} catch (ParseException e) {
			e.printStackTrace();
			return false;
		}
		return MD5.keyCheck(str.toString(), EOSlock.getLsjzs());
	}

	@Bizlet()
	public static String getMacaddrs() {
		return macaddrs;
	}

	public static void setMacaddrs(String macaddrs) {
		EOSlock.macaddrs = macaddrs;
	}

	public static String getAutocode() {
		return autocode;
	}

	public static void setAutocode(String autocode) {
		EOSlock.autocode = autocode;
	}

	public static String getIsauth() {
		return isauth;
	}

	public static void setIsauth(String isauth) {
		EOSlock.isauth = isauth;
	}

	public static String getProjectname() {
		return projectname;
	}

	public static void setProjectname(String projectname) {
		EOSlock.projectname = projectname;
	}

	public static String getAuthdate() {
		return authdate;
	}

	public static void setAuthdate(String authdate) {
		EOSlock.authdate = authdate;
	}

	public static String getAuthexpdate() {
		return authexpdate;
	}

	public static void setAuthexpdate(String authexpdate) {
		EOSlock.authexpdate = authexpdate;
	}

	public static String getLsjzs() {
		return lsjzs;
	}

	public static void setLsjzs(String lsjzs) {
		EOSlock.lsjzs = lsjzs;
	}
}

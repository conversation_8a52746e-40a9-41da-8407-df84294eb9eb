<?xml version="1.0" encoding="UTF-8"?>
<web-app id="defaultWebApp" version="2.4"
	xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">

	<filter>
		<filter-name>InterceptorFilter</filter-name>
		<filter-class>com.eos.access.http.InterceptorFilter</filter-class>
	</filter>
	<listener>
		<listener-class>com.primeton.sca.host.webapp.SCAWebServiceServletListener</listener-class>
	</listener>
	<listener>
		<listener-class>com.primeton.ext.runtime.core.RuntimeJ2EEHost</listener-class>
	</listener>
	<listener>
		<listener-class>com.primeton.engine.core.impl.process.SessionListener</listener-class>
	</listener>
	<listener>
		<listener-class>com.eos.access.http.UserObjectSessionListener</listener-class>
	</listener>
	<filter-mapping>
		<filter-name>InterceptorFilter</filter-name>
		<url-pattern>/*</url-pattern>
		<dispatcher>FORWARD</dispatcher>
		<dispatcher>REQUEST</dispatcher>
		<dispatcher>INCLUDE</dispatcher>
	</filter-mapping>

	<!-- 配置和映射Servlet -->
	<servlet>
		<!--Servlet 注册的名字 -->
		<servlet-name>LoginServlet</servlet-name>
		<!--Servlet 的全类名 -->
		<servlet-class>org.gocom.components.coframe.auth.login.LoginServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>LoginServlet</servlet-name>
		<url-pattern>/method=login.do</url-pattern>
	</servlet-mapping>

	<!-- 配置和映射Servlet -->
	<servlet>
		<!--Servlet 注册的名字 -->
		<servlet-name>SSOLoginServlet</servlet-name>
		<!--Servlet 的全类名 -->
		<servlet-class>org.gocom.components.coframe.auth.login.SSOLoginServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>SSOLoginServlet</servlet-name>
		<url-pattern>/ssoLogin</url-pattern>
	</servlet-mapping>
	
	<servlet>
		<servlet-name>ControllerServlet</servlet-name>
		<servlet-class>
			com.eos.access.http.ControllerServlet
		</servlet-class>
		<load-on-startup>10</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.flow</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.flowx</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.gzip</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.ajax</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.beanx</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.debug</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>/common.remote</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.precompile</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.ext</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ControllerServlet</servlet-name>
		<url-pattern>*.terminate</url-pattern>
	</servlet-mapping>

	<session-config>
		<session-timeout>30</session-timeout>
	</session-config>
	<welcome-file-list>
		<welcome-file>index.jsp</welcome-file>
	</welcome-file-list>
	<mime-mapping>
		<extension>xml</extension>
		<mime-type>application/xml</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>war</extension>
		<mime-type>application/zip</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>ear</extension>
		<mime-type>application/zip</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>zip</extension>
		<mime-type>application/zip</mime-type>
	</mime-mapping>
	<error-page>
		<error-code>404</error-code>
		<location>/common/notFound.jsp</location>
	</error-page>
	<!-- <jsp-config> <jsp-property-group> <url-pattern>*.jsp</url-pattern> 
		<page-encoding>UTF-8</page-encoding> </jsp-property-group> </jsp-config> -->
</web-app>

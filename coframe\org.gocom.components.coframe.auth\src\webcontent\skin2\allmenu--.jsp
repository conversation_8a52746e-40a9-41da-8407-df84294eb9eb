<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@include file="/coframe/tools/skins/common.jsp" %>
<!--
  - Author(s): wanghl
  - Date: 2013-03-22 10:48:55
  - Description:
-->
<head>
<link rel="stylesheet" type="text/css" href="../skin1/skin/css/bootstrap/css/bootstrap.min.css" />
<link rel="stylesheet" type="text/css" href="../skin1/skin/css/style.css" />
<link rel="stylesheet" type="text/css" href="../skin1/skin/css/index.css" />
<title>SPD院内物流资源信息管理系统</title>
</head>
<body style="overflow: auto;font-family:'Microsoft YaHei';">
<div class="tab-body" id="tab1">
	<div class="login-title">
		<img src="../skin1/skin/images/logo.png"class="mg-auto"/>
		<h3>SPD院内物流资源信息管理系统</h3>
	</div>
	<div class="panel panel-info index-menu" id="td1"></div>
	<div class="panel panel-info index-menu" id="td2"></div>
	<div class="panel panel-info index-menu" id="td3"></div>
	<div class="panel panel-info index-menu" id="td4"></div>
</div>
</body>
<script tyle="text/javascript">

	getMenuData();

	function getMenuData(){
		$.ajax({
			url: "org.gocom.components.coframe.auth.LoginManager.getMenuData.biz.ext",
			type: "POST",
			success: function(text){
				var treeNodes = text.treeNodes;
				setMenuData(treeNodes);
			}
		});
	}

	function setMenuData(data){
		if(data){
			var levmenus = ["","","",""];
			for(var i = 0; i < data.length; i++){
				var levmenu = "";
				var menuName = data[i].menuName;
				var linkAction = data[i].linkAction ? data[i].linkAction: "";
				var menuPrimeKey = data[i].menuPrimeKey;
				var menuSeq = data[i].menuSeq;
				if(linkAction == ""){
					levmenu += "<div class='panel-heading text-center'><h3 class='levmenu panel-title' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + "<em class='glyphicon glyphicon-folder-open'></em>" + menuName + "</h3></div>";
				}else{
					levmenu += "<div class='panel-heading text-center'><a class='levmenu panel-title' href='" + "<%=contextPath %>" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + "<em class='glyphicon glyphicon-folder-open '></em>" + menuName + "</a></div>";
				}
				var secondChilds = "";
				if(data[i].childrenMenuTreeNodeList){
					var Lev2childrens = data[i].childrenMenuTreeNodeList;
					for(var j = 0; j < Lev2childrens.length; j++){
						var menuName = Lev2childrens[j].menuName;
						var linkAction = Lev2childrens[j].linkAction ? Lev2childrens[j].linkAction : "";
						var menuPrimeKey = Lev2childrens[j].menuPrimeKey;
						var menuSeq = Lev2childrens[j].menuSeq;
						var otherChildrens = getThirdOrMoreChild(Lev2childrens[j]);
						if(otherChildrens && (otherChildrens != "")){
							secondChilds += "<div class='faded list-group'><a class='lev2menu1 list-group-item list-group-item-info faded' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a>" + otherChildrens + "</div>";
						}else{
							secondChilds += "<li class='faded'><a class='lev2menu faded' href='javascript:topwindowurl(\""+menuPrimeKey+"\",\""+ menuName +"\",\""+linkAction+"\")' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></li>";
						}
					}
				}
				// if(secondChilds){
				// 	levmenu += "<ul class='panel-body'>" + secondChilds + "</ul>";
				// }
				levmenu = levmenu + "<ul class='panel-body'>" + secondChilds + "</ul>";
				levmenus[i%4] += levmenu;
			}
			insertMenuToHtml(levmenus);
		}
	}

	function getThirdOrMoreChild(lev2Childrens){
		var results = "";
		if(lev2Childrens.childrenMenuTreeNodeList){
			var childrens = lev2Childrens.childrenMenuTreeNodeList;
			for(var i = 0; i < childrens.length; i++){
				var menuName = childrens[i].menuName;
				var linkAction = childrens[i].linkAction ? childrens[i].linkAction : "";
				var menuPrimeKey = childrens[i].menuPrimeKey;
				var menuSeq = childrens[i].menuSeq;
				if(linkAction == ""){
					results += "<a class='lev3menu list-group-item faded' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a>";
				}else{
					results += "<a class='lev3menu list-group-item faded' href='javascript:topwindowurl(\""+menuPrimeKey+"\",\""+ menuName +"\",\""+linkAction+"\")' url='" + "<%=contextPath %>" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a>";
				}
				var ret = getThirdOrMoreChild(childrens[i]);
				if(ret){
					results += ret;
				}
			}
			if(results){
				return results;
			}
		}
	}

	function insertMenuToHtml(levmenu){
		$("#td1").html(levmenu[0]);
		$("#td2").html(levmenu[1]);
		$("#td3").html(levmenu[2]);
		$("#td4").html(levmenu[3]);
	}

	function topwindowurl(id,name,url){
    	window.parent.ontabpagetourl(id,name,url);
	}

</script>
</html>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:sca="http://www.springframework.org/schema/sca" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd http://www.springframework.org/schema/sca http://www.osoa.org/xmlns/sca/1.0/spring-sca.xsd">
    <bean class="org.gocom.components.coframe.flowconfig.ProcessConfigServiceBean" id="ProcessConfigServiceBeanBean"/>
    <bean id="CapRuleBean" parent="DefaultBaseTransactionProxy">
        <property name="proxyInterfaces">
            <list>
                <value>org.gocom.components.coframe.flowconfig.ICapRuleService</value>
            </list>
        </property>
        <property name="target">
            <bean class="org.gocom.components.coframe.flowconfig.CapRuleService">
                <property name="dataSource" ref="DefaultDataSource"/>
            </bean>
        </property>
    </bean>
    <bean id="FlowConfigServiceBean" parent="DefaultBaseTransactionProxy">
        <property name="proxyInterfaces">
            <list/>
        </property>
        <property name="target">
            <bean class="org.gocom.components.coframe.flowconfig.FlowConfigService">
                <property name="dataSource" ref="DefaultDataSource"/>
            </bean>
        </property>
    </bean>
</beans>

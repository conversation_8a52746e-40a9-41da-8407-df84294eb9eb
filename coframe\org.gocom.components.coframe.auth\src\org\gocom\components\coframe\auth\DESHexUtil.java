package org.gocom.components.coframe.auth;

import java.nio.charset.StandardCharsets;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;

import com.eos.system.annotation.Bizlet;

public class DESHexUtil {

	/**
	 * 解密算法
	 * 
	 * @param pToDecrypt 待解密的字符串
	 * @param key        密钥
	 * @return 解密后的字符串，如果解密失败则返回空字符串
	 */
	@Bizlet
	public static String decrypt(String pToDecrypt, String key) {
		if (pToDecrypt == null || pToDecrypt.trim().isEmpty()) {
			return "";
		}

		try {
			// 1. 将十六进制字符串转换为字节数组
			byte[] inputByteArray = new byte[pToDecrypt.length() / 2];
			for (int x = 0; x < pToDecrypt.length() / 2; x++) {
				int i = Integer.parseInt(pToDecrypt.substring(x * 2, x * 2 + 2), 16);
				inputByteArray[x] = (byte) i;
			}

			// 2. 创建DES密钥
			DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.US_ASCII));
			SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
			SecretKey secretKey = keyFactory.generateSecret(desKeySpec);

			// 3. 创建初始化向量（IV），这里使用与密钥相同的字节
			IvParameterSpec iv = new IvParameterSpec(key.getBytes(StandardCharsets.US_ASCII));

			// 4. 创建Cipher对象并初始化为解密模式
			Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding"); // 或者 "DES/CBC/NoPadding"
			cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

			// 5. 执行解密
			byte[] decryptedByteArray = cipher.doFinal(inputByteArray);

			// 6. 将解密后的字节数组转换为字符串
			return new String(decryptedByteArray, StandardCharsets.UTF_8); // 或者其他合适的编码

		} catch (Exception e) {
			e.printStackTrace(); // 打印异常信息，方便调试
			return "";
		}
	}

	/**
	 * 加密算法
	 * 
	 * @param pToEncrypt 待加密的字符串
	 * @param key        密钥 (必须是8个字符)
	 * @return 加密后的十六进制字符串，如果加密失败则返回空字符串
	 */
	@Bizlet
	public static String encrypt(String pToEncrypt, String key) {
		if (pToEncrypt == null || pToEncrypt.isEmpty()) {
			return "";
		}
		try {
			// 1. 创建DES密钥
			DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.US_ASCII));
			SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
			SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
			// 2. 创建初始化向量（IV），这里使用与密钥相同的字节
			IvParameterSpec iv = new IvParameterSpec(key.getBytes(StandardCharsets.US_ASCII));
			// 3. 创建Cipher对象并初始化为加密模式
			Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding"); // 或者 "DES/CBC/NoPadding"
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
			// 4. 执行加密
			byte[] inputByteArray = pToEncrypt.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
			byte[] encryptedByteArray = cipher.doFinal(inputByteArray);
			// 5. 将加密后的字节数组转换为十六进制字符串
			StringBuilder stringBuilder = new StringBuilder();
			for (byte b : encryptedByteArray) {
				stringBuilder.append(String.format("%02X", b)); // 将每个字节转换为两位的十六进制字符串
			}
			return stringBuilder.toString();
		} catch (Exception e) {
			e.printStackTrace(); // 打印异常信息，方便调试
			return "";
		}
	}

	public static void main(String[] args) {
		// 示例用法
		String encryptedText = "your_encrypted_hex_string_here"; // 替换为你的十六进制加密字符串
		String key = "your_des_key"; // 替换为你的DES密钥 (8个字符)

		String decryptedText = decrypt(encryptedText, key);
		System.out.println("解密后的文本: " + decryptedText);
	}
}

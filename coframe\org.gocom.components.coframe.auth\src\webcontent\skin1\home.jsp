﻿<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<%@include file="/coframe/tools/skins/common.jsp" %>

<head>
	<title>
		<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%>
	</title>
	<link rel="stylesheet" href="<%=contextPath%>/coframe/auth/skin1/js/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="<%=contextPath%>/coframe/auth/skin1/js/layui/css/modules/layer/default/layer.css" media="all">
    <link rel="stylesheet" href="<%=contextPath%>/coframe/auth/skin1/css/admin2.css" media="all">
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/css/bootstrap.min.css" />
	<link href="<%=contextPath%>/coframe/auth/skin1/css/font-awesome/css/font-awesome.css" type="text/css"
		rel="stylesheet" />
	<link href="<%=contextPath%>/coframe/auth/skin1/css/font-awesome/css/components-rounded.min.css" type="text/css"
		rel="stylesheet" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/css/style.min.css" />

	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/Notice/css/main.css" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/date/css/date.css" />
	<link rel="stylesheet" href="<%=contextPath%>/common/css/restore.css">  
	<!--<link rel="stylesheet" type="text/css" href="calendar/simple-calendar.css">
	 <script type="text/javascript" src="calendar/simple-calendar.js"></script> -->

	<style>
		#wrapper {
			background-color: #fff;
		}

		.img-thumbnail,
		body {
			background-color: #eee;
			font-size: 12px;
		}

		#calendar {
			position: relative;
		}

		.ibox-title {
			padding: 10px 0px 0px;
		}

		.ibox-title h5 {
			background-color: #3ba6dc;
			padding: 5px 10px;
			font-size: 12px;
			margin: 0 0 7px 15px;
			border-radius: 10px;
			color: #fff;
			font-weight: 400;
		}

		.tableform td {
			border: 1px solid #000;
			color: #000;
		}

		.tableform thead td {
			font-weight: 700;
			background-color: #defdfd !important;
		}

		.tableform tbody td {
			text-align: right;
		}

		.tableform2 tbody td {
			text-align: center;
		}

		.tableform>tbody>tr>td,
		.tableform>thead>tr>td {
			padding: 3px 8px;
		}

		.table-responsive {
			padding-right: 0px;
		}

		.tableform {
			text-align: center;
			/* cellpadding: 0;
			cellspacing: 0; */
			border: 2px solid #000;
		}

		.ibox {
			background-color: #fff;
		}

		.ibox>.row {
			margin: 0;
		}

		.messagelist {
			font-size: 14px;

		}

		.messagelist ul {
			padding: 0;
		}

		.messagelist li {
			list-style: none;
			float: left;
			padding-left: 10px;
			margin-bottom: 5px;
			border-left: 2px solid #3ba6dc;
		}

		.messagelist a {
			color: #000;
		}

		.feed-element .leftdiv {
			margin-right: 10px;
			padding: 5px;
			border-radius: 5px;
			color: #fff;
			background-color: #1ab394;
		}

		.spinner {
			width: 100%;
			height: 100%;

			position: relative;
			/* margin: 0 auto; */
		}

		.bounce1,
		.bounce2 {
			width: 50px;
			height: 50px;
			border-radius: 50%;
			background-color: #67CF22;
			opacity: 0.6;
			position: absolute;
			top: 40%;
			left: 50%;
			transform: translateX(-50%);
			-webkit-animation: bounce 2.0s infinite ease-in-out;
			animation: bounce 2.0s infinite ease-in-out;
		}

		.bounce2 {
			-webkit-animation-delay: -1.0s;
			animation-delay: -1.0s;
		}

		@-webkit-keyframes bounce {

			0%,
			100% {
				-webkit-transform: translateX(-50%) scale(0.0)
			}

			50% {
				-webkit-transform: translateX(-50%) scale(1.0)
			}
		}

		@keyframes bounce {

			0%,
			100% {
				transform: translateX(-50%) scale(0.0);
				-webkit-transform: translateX(-50%) scale(0.0);
			}

			50% {
				transform: translateX(-50%) scale(1.0);
				-webkit-transform: translateX(-50%) scale(1.0);
			}
		}

		.modal {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			background-color: rgba(0, 0, 0, .1);
			z-index: 999;
			display: none;
		}

		element.style {
			height: 100%;
			overflow: scroll;
		}

		html,
		body,
		div,
		h1,
		h2,
		h3,
		h4,
		h5,
		h6,
		ul,
		li,
		ol,
		dl,
		dt,
		dd,
		p,
		span,
		em,
		b,
		i,
		input,
		select,
		textarea {
			margin: 0;
			padding: 0;
		}

		* {
			font-family: courier;
		}

		*,
		:after,
		:before {
			-webkit-box-sizing: border-box;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
		}

		user agent stylesheet div {
			display: block;
		}

		.ibox-content {
			background-color: #fff;
			color: inherit;
			padding: 15px 20px 20px;
			border-color: #e7eaec;
			-webkit-border-image: none;
			-o-border-image: none;
			border-image: none;
			border-style: solid solid none;
			border-width: 1px 0;
		}

		.messagelist {
			font-size: 14px;
		}

		.img-thumbnail,
		body {
			background-color: #eee;
			font-size: 12px;
		}

		body {
			font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
			font-size: 13px;
			color: #676a6c;
			overflow-x: hidden;
		}

		body {
			color: #333;
			padding: 0 !important;
			margin: 0 !important;
			direction: "ltr";
			font-size: 14px;
		}

		body,
		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			font-family: "Open Sans", sans-serif;
		}

		body {
			font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
			font-size: 14px;
			line-height: 1.42857;
			color: #333;
		}

		body {
			font-family: 微软雅黑;
			font-size: 12px;
			line-height: 22px;
		}

		body {
			font-family: 微软雅黑;
			font-size: 12px;
			line-height: 22px;
		}

		body {
			font-family: Tahoma, Verdana, 宋体;
			font-size: 12px;
			line-height: 22px;
		}

		body {
			font-size: 12px;
			line-height: 180%;
			font-family: Verdana, Simsun;
		}

		html {
			font-size: 10px;
			-webkit-tap-highlight-color: transparent;
		}

		html {
			font-family: sans-serif;
			-ms-text-size-adjust: 100%;
			-webkit-text-size-adjust: 100%;
		}

		user agent stylesheet html {
			color: -internal-root-color;
		}

		*,
		:after,
		:before {
			-webkit-box-sizing: border-box;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
		}

		*,
		:after,
		:before {
			-webkit-box-sizing: border-box;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
		}

		::-webkit-scrollbar {
			width: 6px;
			/* background-color: #F5F5F5; */
		}

		::-webkit-scrollbar {
			width: 6px;
			height: 6px;
		}

		::-webkit-scrollbar {
			width: 8px;
			height: 6px;
		}

		::-webkit-scrollbar-thumb {
			background-color: #fff !important;
		}
	</style>
</head>

<body >
	<div class="wrapper wrapper-content">
		<div class="row">
			<div class="col-sm-7 ui-sortable shortcuts">
				<div class="ibox float-e-margins">
					<div class="ibox-title">
						<h5>快捷菜单</h5>
						<a onclick="AddShotCut()" class="layui-btn layui-btn-default layui-btn-xs" style="margin-left: 10px;cursor: pointer;" >
                            <i class="layui-icon layui-icon-add-1" style="position: relative;top: 0;left: 0;"></i>
                            添加
                        </a>
					</div>
					<div class="ibox-content" style="padding: 15px 10px 20px 10px;height:345px;" >
						<div id="content">
							 <div class="layui-carousel layadmin-carousel layadmin-shortcut" id="test1">
								 <div carousel-item id="shortCutList">
									<!--<ul class="layui-row layui-col-space10 layui-this">-->
									<!--<li class="layui-col-xs3">-->
									<!--<a lay-href="home/homepage1">-->
									<!--<i class="layui-icon layui-icon-console"></i>-->
									<!--<cite>主页一</cite>-->
									<!--</a>-->
									<!--</li>-->
									<!--</ul>-->
								 </div>
							 </div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="col-sm-5 ui-sortable messagelist">
				<div class="ibox float-e-margins">
					<div class="ibox-title">
						<h5>消息通知</h5>
						<!--<div class="ibox-tools">
								<a href="javascript:;" class="label label-info-light" style="margin-right: 15px;background-color:#3ba6dc;color:#fff;"><i class="fa fa-refresh"></i> 刷新</a>
							</div>-->
					</div>
					<div class="ibox-content spinner" style="position:  relative;padding-bottom:50px;">
						<div style="height:100%;">
							<div class="feed-activity-list" style="height:100%;overflow-y:scroll;">
								<div class="feed-element">
									<a href="profile.html" class="pull-left">
										过效期预警
									</a>
									<div class="media-body ">
										<small class="pull-right">5分钟前</small>
										<strong>截至2018-04-10，过效期品规数5个，涉及金额125639.12元，关联标签10个，禁止使用并尽快处理。</strong>
										<br>
										<small class="text-muted">2014.11.8 12:22</small>
									</div>
								</div>
							</div>
							<!--<button class="btn btn-primary btn-block m-t" style="width:20%;bottom: 10px;padding: 6px 0px;position:absolute;
								left: 50%;
								transform: translateX;
								transform: translateX(-50%);">
								<i class="fa fa-arrow-down"></i> 加载更多</button>
							 <div class="double"></div>
  									<div class="double"></div> -->
						</div>
						<div class="modal">
							<div class="double"></div>
							<div class="double"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-7 ui-sortable crkfx">
				<div class="ibox float-e-margins">
					<div class="ibox-title">
						<h5>出入库分析</h5>
					</div>
					<div class="ibox-content">
						<div class="row">
							<div class="col-sm-5 table-responsive">
								<table class="table tableform table-bordered">
									<thead>
										<td>日期</td>
										<td>入库金额(万元)</td>
									</thead>
									<tbody id="insoutstr">
									</tbody>
								</table>
							</div>
							<div class="col-sm-7">
								<div id="main" style="width:100%;height:220px;"></div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-5 table-responsive">
								<table class="table tableform table-bordered">
									<thead>
										<td>日期</td>
										<td>出库金额(万元)</td>
									</thead>
									<tbody id="outsoutstr">
									</tbody>
								</table>
							</div>
							<div class="col-sm-7">
								<div id="main1" style="width:100%;height:220px;"></div>
							</div>
						</div>
						<div style="font-size:14px;color:#000;margin-top:10px;">
							<span>在库品规数 ：</span>
							<span style='margin-right:100px;' class="totalqty">0</span>
							<span>库存金额 ：</span>
							<span class="totalmoney">0</span>
							<span>万元</span>
						</div>
					</div>
				</div>
			</div>
			<div class="col-sm-5 ui-sortable sldfx">
				<div class="ibox float-e-margins">
					<div class="ibox-title">
						<h5>申领单分析</h5>
						<div class="ibox-tools">
							<span class="label label-warning-light"
								style="margin-right: 15px;background-color:#1ab394;">更多</span>
						</div>
					</div>
					<div class="ibox-content" style="height:529px;">
						<div class="row">
							<div class="col-sm-12 table-responsive">
								<table class="table tableform tableform2">
									<thead>
										<td>科室</td>
										<td>当日新增</td>
										<td>拣货中</td>
										<td>配送中</td>
										<td>科室验收</td>
										<td>累计待捡</td>
									</thead>
									<tbody id="Claim">
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			
		</div>
	</div>
</body>
<div id="shortCutWrap" style="display: none;padding: 5px;">
        <div class="layui-form layui-form-pane">
            <div class="layui-form-item">
                <label class="layui-form-label"><span style="color:red">*</span>菜单名称</label>
                <div class="layui-input-block">
                    <input type="text" notempty="true" errmsg="菜单名称不能为空" class="layui-input" name="menuname">
                    <input type="hidden" name="menuid">
                    <input type="hidden" name="cutcd">
                    <input type="hidden" name="revision">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span style="color:red">*</span>菜单URL</label>
                <div class="layui-input-block">
                    <input type="text" name="url" notempty="true" errmsg="请选择菜单URL" class="layui-input " readonly="readonly" >
                    <a onclick="chooseUrls()" style="position: absolute;top: 1px;right: 0;" class="layui-btn layui-btn-primary layui-btn-sm layui-bg-green"><i class="layui-icon layui-icon-more"></i></a>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span style="color:red">*</span>图标</label>
                <div class="layui-input-block">
                    <input type="text" name="icons" notempty="true" errmsg="请选择菜单图标" class="layui-input " readonly="readonly">
                    <a onclick="chooseIcons()" style="position: absolute;top: 1px;right: 0;" class="layui-btn layui-btn-primary layui-btn-sm layui-bg-green"><i class="layui-icon layui-icon-more"></i></a>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="text" name="ordercd" class="layui-input " onkeyup="onlyInputInt(this)">
                </div>
            </div>

        </div>
    </div>
<script src="<%=contextPath%>/coframe/auth/skin1/js/jquery.min.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/bootstrap.min.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/Notice/js/main.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/echarts/echarts.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/echarts/echarts-demo.min.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/layui/layui.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/layui/lay/modules/layer.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/common.js"></script>
<!-- <script src="<%=contextPath%>/coframe/auth/skin1/date/js/date.js"></script> -->
<script>
    loadBg();
	loadShortCut();
	window.addEventListener("storage",function(e){
        loadBg();
    });
	var insoutlist = window.parent.commonselect({
		name: 'searchInsoutlist',
		sendData: {
			"param": JSON.stringify({
				deptids: window.parent.DeptId
			})
		}
	});
	var outsoutlist = window.parent.commonselect({
		name: 'searchOutsoutlist',
		sendData: {
			"param": JSON.stringify({
				deptids: window.parent.DeptId
			})
		}
	});
	var totaldata = window.parent.commonselect({
		name: 'searchTotal',
		sendData: {
			"param": JSON.stringify({
				deptids: window.parent.DeptId
			})
		}
	});
	var Claimdata = window.parent.commonselect({
		name: 'searchClaimAnalysis'
	});
	var BIData = window.parent.commonselect({
		name: 'searchMessage',
		sendData: {
			"param": JSON.stringify({
				deptids: window.parent.DeptId
			})
		}
	});
	var noticeData = window.parent.commonselect({
		name: 'searchNotice',
		sendData: {
			"param": JSON.stringify({
				deptids: window.parent.DeptId
			})
		}
	});
	$('.totalqty').text(totaldata[0].totalqty ? totaldata[0].totalqty : 0);
	$('.totalmoney').text(totaldata[0].totalmoney ? (totaldata[0].totalmoney / 10000).toFixed(2) : 0);
	var flag = 0;
	newsoutlist(insoutlist, "#insoutstr", "main");
	newsoutlist(outsoutlist, "#outsoutstr", "main1");
	newClaimList(Claimdata, '#Claim');
	setmsglist();
	// setInterval(setmsglist,5000);
	/*$('.label-info-light').click(function(){
		flag=1;
		$('.modal').show();
		$('.double').eq(0).addClass('bounce1');
		$('.double').eq(1).addClass('bounce2');
		$('.feed-element a').attr({'href':'javascript:;'});
		setmsglist();
	})*/
	function slicestr(str) {
		var num = str.indexOf('.');
		if (!num) {
			return str;
		} else {
			return str.slice(0, num + 3);
		}
	}

	function newChart(data, id) {
		var myChart = echarts.init(document.getElementById(id));
		option = {
			grid: {
				top: 10,
				bottom: 80,
				right: 0,
				left: '15%',
			},
			xAxis: {
				type: 'category',
				data: data.insoutX,
				nameRotate: 30,
				axisLabel: {
					interval: 0,
					rotate: 60
				}
			},
			yAxis: {
				type: 'value',
			},
			series: [{
				data: data.insoutY,
				type: 'bar',
				itemStyle: {

					borderWidth: '30%',
					normal: {
						show: true,
						color: '#3ba6dc',
						label: {
							show: true, //开启显示
							position: 'top', //在上方显示
							textStyle: { //数值样式
								color: '#999',
							}
						}
					}
				},
				barWidth: '50%',
				barMinHeight: 15,
			}]
		};
		// 使用刚指定的配置项和数据显示图表。
		myChart.setOption(option);
	}

	function newsoutlist(data, id, id1) {
		var insoutstr = '';
		var insoutdata = {
			insoutX: [],
			insoutY: []
		};
		for (var i = 0; i < data.length; i++) {
			if (data[i].taxcostamt == null) {
				data[i].taxcostamt = "";
			}
			data[i].insoutdate = data[i].insoutdate.slice(0, 11);
			insoutdata.insoutX.push(data[i].insoutdate);
			insoutdata.insoutY.push((data[i].taxcostamt / 10000).toFixed(2));
			insoutstr += "<tr>" +
				"<td style='text-align: center;'>" + data[i].insoutdate + "</td>" +
				"<td>" + (data[i].taxcostamt / 10000).toFixed(2) + "</td>" +
				"</tr>"
		}

		$(id).html(insoutstr);
		newChart(insoutdata, id1);
	}

	function newClaimList(data, id) {
		var insoutstr = '';
		for (var i = 0; i < data.length; i++) {
			if (data[i].taxcostamt == null) {
				data[i].taxcostamt = "";
			}
			insoutstr += "<tr>" +
				"<td style='text-align: center;'>" + data[i].deptname + "</td>" +
				"<td>" + data[i].ordcnt + "</td>" +
				"<td>" + data[i].pickcnt + "</td>" +
				"<td>" + data[i].ztcnt + "</td>" +
				"<td>" + data[i].deptcnt + "</td>" +
				"<td>" + data[i].wcnt + "</td>" +
				"</tr>"
		}
		$(id).html(insoutstr);
	}

	function timetrans(date) {
		var days = Math.floor(date / (1000 * 60 * 60 * 24));
		var hours = parseInt((date % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
		var minutes = parseInt((date % (1000 * 60 * 60)) / (1000 * 60));
		var seconds = (date % (1000 * 60)) / 1000;
		if (days > 0) {
			return days + "天" + hours + "小时" + minutes + "分钟前"
		} else {
			return hours + "小时" + minutes + "分钟前";
		}
	}

	function topwindowurl(id, name, url) {
		window.parent.ontabpagetourl(id, name, url);
		//logrecord();
	}
	
	//记录查看消息通知日志
	/*function logrecord(){
		var logrecos = [{EMPID:"李四"}]
		var Grid_Rows = new SS_Records(logrecos);
		var json_args={"EXECSQL":"PJSON_INS_ORDER.P_DEPT_ORDER_WF_PO"};
		var json_argsdetail={"EMPID":"张三"};
		var json_columns=[];
		var tagkey="查看日志操作";
		var dbName="";
		var result=Grid_Rows.callSysRoute(json_args,json_argsdetail,json_columns,tagkey,dbName);
		if(result.flag){
			alert(result.message);
		}else{
			alert(result.message);
		}
	}*/
	
	
	
	function setmsglist() {
		if ($('.messagelist .ibox-content').height() < $('.ibox-content').eq(0).height()) {
			$('.messagelist .ibox-content').height($('.ibox-content').eq(0).height() - 30);
		}
		var listHeight = $('.messagelist .ibox-content').height();
		var msgHeight = document.querySelector('.messagelist .ibox-content .feed-element').offsetHeight;
		//var listnum = parseInt(listHeight / (msgHeight + 15));
		var msgnum = BIData.length;
		var msgstr = '';
		for (var i = 0; i < noticeData.length; i++) {
			var time2 = new Date().getTime() - new Date(Date.parse(noticeData[i].adddate.replace(/-/g, "/"))).getTime();
			// var time=new Date().getTime()-new Date(data.adddate).getTime();
			time2 = timetrans(time2);
			msgstr += "<div class='feed-element'>" +
				"<a  href='javascript:;'>" +
				"<div  class='pull-left leftdiv'>" + '系统消息通知' + "</div>" +
				"<div class='media-body'>" +
				"<small class='pull-right'>" + noticeData[i].adddate + "</small>" +
				"<strong>" + noticeData[i].msgvalues + "</strong><br>" +
				"<small class='text-muted'>" + time2 + "</small>" +
				"</div>" +
				"</a>" +
				"</div>"
		}

		// $('.messagelist  .ibox-tools>span').html(msgnum+'条未读')
		for (var j = 0; j< BIData.length; j++) {
			//if (i < listnum) {
				var data = BIData[j];
				var time = new Date().getTime() - new Date(Date.parse(data.adddate.replace(/-/g, "/"))).getTime();
				// var time=new Date().getTime()-new Date(data.adddate).getTime();
				time = timetrans(time);
				msgstr += "<div class='feed-element'>" +
					"<a  href='javascript:topwindowurl(\"" + data.msgid + "\"" + "," + "\"" + data.msgabstrct + "\"" +
					//"," + "\"" + data.meansrc + "\"" + ")'" + "id=" + "\"" + data.msgid + "\"" + "menuSeq=" + "\"" + data
					"," + "\"" + data.meansrc + "?id=" + data.msgid + "\"" + ")'" + "id=" + "\"" + data.msgid + "\"" + "menuSeq=" + "\"" + data
					.addtermid + "\"" + ">" +
					"<div  class='pull-left leftdiv'>" + data.msgtype + "</div>" +
					"<div class='media-body'>" +
					"<small class='pull-right'>" + time + "</small>" +
					"<strong>" + data.msgtitle + "</strong><br>" +
					"<small class='text-muted'>" + data.adddate + "</small>" +
					"</div>" +
					"</a>" +
					"</div>"
			//}
		}
		$('.messagelist .feed-activity-list').html(msgstr);
		$(".leftdiv:contains(过效期预警)").css({
			'backgroundColor': '#1ab394'
		});
		$(".leftdiv:contains(来单提醒)").css({
			'backgroundColor': '#3ba6dc',
			'padding': '5px 12px'
		});
		$(".leftdiv:contains(通知)").css({
			'backgroundColor': '#1ab394',
			//'padding': '5px 25px'
		});
		if (flag) {
			setTimeout(function () {
				$('.modal').hide();
				$('.double').eq(0).removeClass('bounce1')
				$('.double').eq(1).removeClass('bounce2')
			}, 2000);
			flag = 0;
		}
	}
	function loadBg() {
        var leftmenucolor = localStorage.getItem("leftmenucolor");
        var bottomcolor = localStorage.getItem("bottomcolor");
        var fontcolor = localStorage.getItem("fontcolor");
        var headercolor = localStorage.getItem("headercolor");
        var titlefontcolor = localStorage.getItem("titlefontcolor");
        var activecolor = localStorage.getItem("activecolor");
        var gridhovercolor = localStorage.getItem("gridhovercolor");
        var gridselectcolor = localStorage.getItem("gridselectcolor");
        var headerlogocolor = localStorage.getItem("headerlogocolor");
        if(leftmenucolor||fontcolor||bottomcolor||headercolor||titlefontcolor||activecolor){
            var root = document.querySelector(':root');
            root.setAttribute('style', '--leftmenucolor: '+leftmenucolor+';--fontcolor: '+fontcolor+';--titlefontcolor: '+titlefontcolor+';--headercolor: '+headercolor+';--activecolor: '+activecolor+';--gridselectcolor: '+gridselectcolor+';--headerlogocolor: '+headerlogocolor+';--gridhovercolor: '+gridhovercolor+';--bottomcolor: '+bottomcolor+'');
        }
    }
	
	function loadShortCut(){
		var json = window.parent.commonselect({
			name: 'searchShortCuts',
			sendData: {
				"param": JSON.stringify({
					userid: window.parent.EmpId,
					depttype:window.parent.DeptType
				})
			}
		});
        var html ='<ul class="layui-row layui-col-space10">';
        $.each(json,function (index,val) {
            html+='<li data-json="'+JSON.stringify(val).replace(/\"/g, "'")+'" class="layui-col-xs3" style="position: relative" onmouseover="showBtn(this)" onmouseleave="hideBtn(this)">' +
                    '<a onclick="JumpTab(this)" data-txt="'+val.menulabel+'" data-id="'+val.menuid+'" data-url="'+val.url+'">' +
                    '<i class="'+val.icons+'"></i>' +
                    '<cite>'+val.menuname+'</cite>' +
                    '</a>' +
                    '<div class="layui-btn-group layui-hide" style="position: absolute;right: 5px;top: 5px;">' +
                    '<a class="layui-btn layui-btn-primary layui-btn-xs" onclick="EditShortCut(this)">编辑</a>' +
                    '<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="DelShortCut(this)">删除</a>' +
                    '</div>'+
                    '</li>';
            if((index+1)%8==0){
                html+='</ul><ul class="layui-row layui-col-space10">'
            }
        });
        html+='</ul>';
        $("#shortCutList").html(html);
        layui.use('carousel', function(){
            var carousel = layui.carousel;
            //建造实例
            carousel.render({
                elem: '#test1'
                ,width: '100%' //设置容器宽度
                ,arrow: 'always' //始终显示箭头
                ,autoplay:false
                ,height:'310px'
                //,anim: 'updown' //切换动画方式
            });
        });
		
	}
	function JumpTab(el){
		var id = $(el).data("id");
		var txt = $(el).data("txt");
		var url = "<%=contextPath%>"+$(el).data("url");
		console.log(url);
		window.parent.JumpT(id, txt,url);
		
	}
	 function showBtn(el) {
        $(el).find(".layui-btn-group").removeClass("layui-hide");
    }
    function hideBtn(el) {
        $(el).find(".layui-btn-group").addClass("layui-hide");
    }
	function AddShotCut() {
        clearFormDatasById("#shortCutWrap",1);
        layer.open({
            type:1,
            area:['350px','300px'],
            shade:0,
            maxmin:true,
            content:$("#shortCutWrap"),
            btn:['确定','取消'],
            btn1:function (index,layero) {
                var re = getFormData("#shortCutWrap");
                if(!re.flag){
                    return false;
                }
                re = re.datas;
                re.userid = window.parent.EmpId;
                re.depttype = window.parent.DeptType;
                if(!re.userid){
                    bsAlert("获取userid异常");
                    return false;
                }
                re._state = 'ADDED';
                var a = doAction(re,'PKG_CAP_SHORTCUT.P_JSON_EDIT','快捷菜单维护');
                if(a===true){
                    layer.msg("执行成功",{time:600},function(){
                        loadShortCut();
                        layer.close(index);
                    });
                }
            }
        });
    }
	 function chooseIcons() {
        layer.open({
            type:2,
            title:'选择图标',
            area:['670px','500px'],
            shade:0,
            maxmin:true,
            content:'icons_list.jsp',
            btn:['确定','取消'],
            btn1:function (index,layero) {
                var iframe = window['layui-layer-iframe' + index];
                var  name =iframe.getIconName();
                if(!name){
                    bsAlert("请选择图标");
                    return false;
                }
                $("#shortCutWrap").find("input[name='icons']").val(name);
                layer.close(index);
            }
        });
    }
	function chooseUrls() {
        layer.open({
            type:2,
            title:'选择菜单',
            area:['500px','500px'],
            shade:0,
            maxmin:true,
            content:'urls_list.jsp?depttype='+window.parent.DeptType+'&userid='+window.parent.EmpId,
            btn:['确定','取消'],
            btn1:function (index,layero) {
                var iframe = window['layui-layer-iframe' + index];
                var  url =iframe.getMenuUrl();
                if(!url){
                    bsAlert("请选择菜单");
                    return false;
                }
                if(url.isleaf==0){
                    bsAlert("选中的菜单包含子菜单，请选择子菜单");
                    return false;
                }
                $("#shortCutWrap").find("input[name='url']").val(url.funcaction);
                $("#shortCutWrap").find("input[name='menuname']").val(url.funcname);
                $("#shortCutWrap").find("input[name='menuid']").val(url.id);
                layer.close(index);
            }
        });
    }
	function EditShortCut(el) {
        var json = $(el).parents("li").data("json");
        if (typeof (json) == "string") {
            json = json.replace(/'/g, '"');
            json = JSON.parse(json);
        }
        if(json){
            clearFormDatasById("#shortCutWrap",1);
            layer.open({
                type:1,
                title:'编辑快捷菜单',
                area:['350px','300px'],
                shade:0,
                maxmin:true,
                content:$("#shortCutWrap"),
                btn:['确定','取消'],
                btn1:function (index,layero) {
                    var re = getFormData("#shortCutWrap");
                    if(!re.flag){
                        return false;
                    }
                    re = re.datas;
                    re.userid = window.parent.EmpId;
                    re.depttype = window.parent.DeptType;
                    if(!re.userid){
                        bsAlert("获取userid异常");
                        return false;
                    }
                    re._state = 'MODIFIED';
                    var a = doAction(re,'PKG_CAP_SHORTCUT.P_JSON_EDIT','快捷菜单维护');
                    if(a===true){
                        layer.msg("执行成功",{time:600},function(){
                            loadShortCut();
                            layer.close(index);
                        });
                    }
                }
            });
            setFormData("#shortCutWrap",json);
        }
    }
    function DelShortCut(el) {
        var json = $(el).parents("li").data("json");
        if (typeof (json) == "string") {
            json = json.replace(/'/g, '"');
            json = JSON.parse(json);
        }
        if(json){
            layer.confirm('确定删除？',{title:'提示'},function (index,layero) {
                json._state ='DELETE';
                var a = doAction(json,'PKG_CAP_SHORTCUT.P_JSON_EDIT','快捷菜单维护');
                if(a===true){
                    layer.msg("执行成功",{time:600},function(){
                        loadShortCut();
                        layer.close(index);
                    });
                }
            })
        }
    }
	function loadMenuList(data) {
		var json = window.parent.commonselect({
				name: 'select_user_menu_list',
				sendData: {
					"param": JSON.stringify(data)
				}
			});
		return json;
	}
</script>

</html>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:ns2="http://goodwillcis.com" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:s0="http://bjgoodwillcis.com" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="http://bjgoodwillcis.com">
    <types>
        <s:schema elementFormDefault="qualified" targetNamespace="http://bjgoodwillcis.com">
            <s:element name="Send">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="pInput" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="SendResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="SendResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
        </s:schema>
        <s:schema elementFormDefault="qualified" targetNamespace="http://goodwillcis.com">
            <s:element name="JiaheSecurity">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="UserName" type="s:string"/>
                        <s:element minOccurs="0" name="Password" type="s:string"/>
                        <s:element minOccurs="0" name="Timestamp" type="s:string"/>
                        <s:element minOccurs="0" name="FromSYS" type="s:string"/>
                        <s:element minOccurs="0" name="IV" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
        </s:schema>
    </types>
    <message name="SendSoapIn">
        <part element="s0:Send" name="parameters"/>
    </message>
    <message name="SendSoapOut">
        <part element="s0:SendResponse" name="parameters"/>
    </message>
    <message name="Headers">
        <part element="ns2:JiaheSecurity" name="JiaheSecurity"/>
    </message>
    <portType name="ServiceSoap">
        <operation name="Send">
            <input message="s0:SendSoapIn"/>
            <output message="s0:SendSoapOut"/>
        </operation>
    </portType>
    <binding name="ServiceSoap" type="s0:ServiceSoap">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="Send">
            <soap:operation soapAction="http://bjgoodwillcis.com/JHIPLIB.SOAP.BS.Service.Send" style="document"/>
            <input>
                <soap:body use="literal"/>
                <soap:header message="s0:Headers" part="JiaheSecurity" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="Service">
        <port binding="s0:ServiceSoap" name="ServiceSoap">
            <soap:address location="http://**********:52773/soap/JHIPLIB.SOAP.BS.Service.cls"/>
        </port>
    </service>
</definitions>

<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="loginbi" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="用户登录" title="shitf&#x9;13-3-12 下午4:05">
    <location x="45" y="250"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" description="" id="link23" name="link23" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign4</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">usercd</process:leftOperand>
          <process:rightOperand type="literal">10</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link20" name="link20" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign5</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">usercd</process:leftOperand>
          <process:rightOperand type="literal">20</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="30" y="30"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link2</targetConnections>
    <targetConnections>link12</targetConnections>
    <targetConnections>link15</targetConnections>
    <targetConnections>link16</targetConnections>
    <targetConnections>link17</targetConnections>
    <location x="870" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Int" name="retCode" type="query" valueType="Primitive">retCode</process:return>
      <process:return id="1" language="String" name="msg" type="query" valueType="Primitive">msg</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="32" y="66"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="872" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="验证用户" displayName="authentication_2017_old" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="link3" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">retCode</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <targetConnections>link21</targetConnections>
    <location x="360" y="150"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" transactionType="join" varArgs="false">
      <process:partner type="literal">this.authentication_2017_old</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
        <process:inputVariable id="1" name="password" type="query" value="String" valueType="Primitive" pattern="reference">password</process:inputVariable>
        <process:inputVariable id="2" name="verifyimgflag" type="literal" value="String" valueType="Primitive" pattern="reference">false</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="returnValue" type="query" value="Int" valueType="Primitive">retCode</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="349" y="185"/>
    <figSize height="17" width="49"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="构造登录用户实例" displayName="login" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="225" y="307"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.login</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userObject" type="query" value="com.eos.data.datacontext.UserObject" valueType="Java" pattern="reference">userObject</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="191" y="343"/>
    <figSize height="17" width="97"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="初始化用户" displayName="initUserObject" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="225" y="232"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.initUserObject</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userId" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="com.eos.data.datacontext.UserObject" valueType="Java">userObject</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="209" y="268"/>
    <figSize height="17" width="61"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="330" y="307"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">userId</process:from>
      <process:to type="query">empObject/userId</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="332" y="343"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="479" y="329"/>
    <figSize height="17" width="185"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="初始化用户上下文数据empObject" displayName="initEmpObject" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>invokeSpring3</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link11</targetConnections>
    <location x="557" y="293"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.initEmpObject</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userObject" type="query" value="com.eos.data.datacontext.UserObject" valueType="Java" pattern="reference">userObject</process:inputVariable>
      <process:inputVariable id="1" name="obj" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">empObject</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="expandEntityByTemplate" displayName="expandEntityByTemplate" type="invoke" index="3" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="435" y="307"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.expandEntityByTemplate</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="template" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">empObject</process:inputVariable>
      <process:inputVariable id="2" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">empObject</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">nn</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="375" y="343"/>
    <figSize height="17" width="143"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="callSysRoute" displayName="authentication_2017_old" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link10" name="link10" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">LoginUserInfo/RETN_CODE</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">LoginUserInfo/RETN_CODE</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="660" y="405"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="6" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">com.wx.utils.publicMemer.SysRoute.callSysRoute</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="dbName" type="literal" value="String" valueType="Primitive" pattern="reference">default</process:inputVariable>
        <process:inputVariable id="1" name="datas" type="query" value="java.util.HashMap[]" valueType="Java" pattern="reference">json_argsdetail</process:inputVariable>
        <process:inputVariable id="2" name="json_args" type="query" value="java.util.HashMap" valueType="Java" pattern="reference">json_args</process:inputVariable>
        <process:inputVariable id="3" name="json_argsdetail" type="expression" value="java.util.HashMap" valueType="Java" pattern="reference">null</process:inputVariable>
        <process:inputVariable id="4" name="json_columns" type="expression" value="String[]" valueType="Primitive" pattern="reference">null</process:inputVariable>
        <process:inputVariable id="5" name="tagkey" type="literal" value="String" valueType="Primitive" pattern="reference">查询登录人员信息</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="result" type="query" value="java.util.HashMap" valueType="Java">LoginUserInfo</process:outputVariable>
        <process:outputVariable id="1" name="message" type="query" value="String" valueType="Primitive"/>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="637" y="440"/>
    <figSize height="17" width="73"/>
    <node>subprocess1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值1" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>subprocess1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="433" y="405"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">PX_LOGIN.P_LOGIN</process:from>
      <process:to type="query">json_args/EXECSQL</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">userId</process:from>
      <process:to type="query">json_argsdetail[1]/LOGINNAME</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">empObject/orgid</process:from>
      <process:to type="query">json_argsdetail[1]/DEPTID</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="431" y="441"/>
    <figSize height="17" width="32"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="555" y="385"/>
    <figSize height="17" width="32"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值2" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokeSpring2</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link10</targetConnections>
    <location x="557" y="349"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">LoginUserInfo</process:from>
      <process:to type="query">empObject/LoginUserInfo</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="赋值3" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link12" name="link12" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="825" y="354"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">LoginUserInfo/RETN_MSG</process:from>
      <process:to type="query">msg</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">-3</process:from>
      <process:to type="query">retCode</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="823" y="390"/>
    <figSize height="17" width="32"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="初始化用户1" displayName="sessionPut" collapsed="false" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link14" name="link14" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand>a</process:leftOperand>
          <process:rightOperand>2</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="557" y="225"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.sessionPut</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="keyvalue" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.String>" valueType="Java" pattern="reference">LoginUserInfo</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="537" y="261"/>
    <figSize height="17" width="68"/>
    <node>invokeSpring3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="setSessionId" displayName="setSessionId" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link13" name="link13" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="637" y="197"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginOnlySession.setSessionId</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userObject/userId</process:inputVariable>
      <process:inputVariable id="1" name="sessionId" type="query" value="java.lang.String" valueType="Java" pattern="reference">userObject/uniqueId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="611" y="233"/>
    <figSize height="17" width="74"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess2" name="userbtn" displayName="authentication_2017_old" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="连接线" lineType="note" type="exception">
      <sourceNode>subprocess2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link18</targetConnections>
    <targetConnections>link19</targetConnections>
    <location x="746" y="260"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess2label</nodeLabel>
    <process:flow index="1" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.userbtn</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userid" type="query" value="String" valueType="Primitive" pattern="reference">userObject/userId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess2label" name="label" nodeType="label">
    <location x="737" y="295"/>
    <figSize height="17" width="45"/>
    <node>subprocess2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo2" name="checkandSetsession" displayName="checkandSetsession" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link18" name="link18" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>subprocess2</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link19" name="link19" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>subprocess2</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="709" y="321"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo synchronization="true">
      <process:partner>org.gocom.components.coframe.auth.login.EOSlock.checkandSetsession</process:partner>
      <process:instance/>
    </process:pojo>
    <process:inputVariables/>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="662" y="357"/>
    <figSize height="17" width="117"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="赋值4" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <targetConnections>link23</targetConnections>
    <location x="151" y="39"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">rc</process:from>
      <process:to type="query">userId</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">000000</process:from>
      <process:to type="query">password</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="149" y="75"/>
    <figSize height="17" width="32"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign5" name="赋值5" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link21" name="link21" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign5</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <targetConnections>link20</targetConnections>
    <location x="135" y="105"/>
    <size height="28" width="28"/>
    <nodeLabel>assign5label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">rc1</process:from>
      <process:to type="query">userId</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">000000</process:from>
      <process:to type="query">password</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign5label" name="label" nodeType="label">
    <location x="133" y="141"/>
    <figSize height="17" width="32"/>
    <node>assign5</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="shitf" createTime="2013-03-07 18:58:23" date="2013-03-07Z" description="" name="login" version="6.3"/>
  <process:variables>
    <process:variable anyType="com.eos.data.datacontext.UserObject" description="" historyStateLocation="client" isArray="false" name="userObject"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.auth.queryentity.QueryEmpObject" name="empObject"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="false" name="json_args"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="true" name="json_argsdetail"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="false" name="LoginUserInfo"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="userId" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="password" primitiveType="String"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="usercd" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="retCode" primitiveType="Int"/>
    <process:output description="" isArray="false" name="msg" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

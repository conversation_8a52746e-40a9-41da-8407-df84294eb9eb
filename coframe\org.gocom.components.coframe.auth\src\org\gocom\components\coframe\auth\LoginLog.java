package org.gocom.components.coframe.auth;

import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.eos.data.datacontext.DataContextManager;
import com.eos.data.datacontext.IMUODataContext;
import com.eos.data.datacontext.IRequestMap;
import com.eos.data.datacontext.ISessionMap;
import com.eos.data.datacontext.IUserObject;
import com.eos.foundation.database.DatabaseExt;
import com.eos.system.annotation.Bizlet;
import com.primeton.ext.common.muo.MUODataContextHelper;

public class LoginLog {
	private  String userid;
	private  String flag;
	private  String  ipAddress;
	private HashMap<String,String> logininfo;
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	
	public String getIpAddress() {
		String ip = "";
		IMUODataContext muo = DataContextManager.current().getMUODataContext();
		if (muo != null) {
			IUserObject userobject = muo.getUserObject();
			if (userobject != null) {
				 ip = userobject.getUserRemoteIP();
			}
		}
		if(ip==null||"".equals(ip)){
			IRequestMap requestmap = DataContextManager.current().getRequestCtx();
			if (requestmap == null) {
				requestmap = MUODataContextHelper.getMapContextFactory().getRequestMap();
			}
			if (requestmap != null) {
				Object rootObject = requestmap.getRootObject();
				if ((rootObject != null) && (rootObject instanceof HttpServletRequest)) {
					HttpServletRequest request = (HttpServletRequest) rootObject;
					 ip = this.getip(request);
				}
			}
		}
		this.setIpAddress(ip);
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	public HashMap<String,String> getLoginInfo(){
		if(this.logininfo==null){
			logininfo = new HashMap<String,String>();
		}
		logininfo.put("userid", this.userid);
		logininfo.put("flag", this.getFlag());
		logininfo.put("ipAddress", this.getIpAddress());  
		return logininfo;
	}
   public String getFlag() {
		return flag;
	}
	public void setFlag(String flag) {
		this.flag = flag;
	}
public boolean save(){
	   boolean flag  = false;
	   try {
		   DatabaseExt.executeNamedSql("default", "org.gocom.components.coframe.auth.loginLog.addLoginLog", this.getLoginInfo());
	       flag = true;
	   } catch (Exception e) {
		   flag  = false;
	   }
	   return flag;
   }
public String getip(HttpServletRequest request){

	String ip = request.getHeader("x-forwarded-for");  
	if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		ip = request.getHeader("Proxy-Client-IP");  
	}  
	if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		ip = request.getHeader("WL-Proxy-Client-IP");  
	}  
	if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		ip = request.getHeader("HTTP_CLIENT_IP");  
	}  
	if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		ip = request.getHeader("HTTP_X_FORWARDED_FOR");  
	}  
	if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {  
		ip = request.getRemoteAddr();  
	}  
	return ip;  

}
 
}

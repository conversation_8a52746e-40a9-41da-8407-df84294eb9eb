package org.gocom.components.coframe.auth.login;

import java.util.HashMap;

import com.eos.system.annotation.Bizlet;
import com.primeton.bfs.engine.json.JSONException;
import com.primeton.bfs.engine.json.JSONObject;

public class LoginUserBtn {
    @Bizlet
	public static void  setSession(HashMap[] maps,String userid){
		if(maps==null||maps.length<1) return;
		JSONObject json = new JSONObject(); 
		for (int i = 0; i < maps.length; i++) {
			HashMap hashMap = maps[i];
			try {
				json.put(hashMap.get("BTNCD")==null? "btncd":hashMap.get("BTNCD").toString(),hashMap.get("BTNFLAG")==null? "0":hashMap.get("BTNFLAG").toString());
			} catch (JSONException e) {
				// TODO 自动生成的 catch 块
				e.printStackTrace();
			}
		}
		System.out.println("----------json.toString()-----------"+json.toString());
		LoginService.sessionPut("userBtn",json.toString());
		LoginService.sessionPut("userId",userid);
	}
}

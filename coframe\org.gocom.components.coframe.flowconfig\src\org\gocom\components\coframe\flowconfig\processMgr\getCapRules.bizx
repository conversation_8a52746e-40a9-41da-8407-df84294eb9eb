<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getCapRules" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="获取规则信息" title="wanghl&#x9;13-3-13 下午6:51">
    <location x="62" y="255"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring2</targetNode>
    </sourceConnections>
    <location x="60" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link1</targetConnections>
    <location x="585" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.flowconfig.flowconfigEntity.CapRule[]" name="data" type="query" valueType="Java">data</process:return>
      <process:return id="1" language="Int" name="total" type="query" valueType="Primitive">total</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="587" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="查询所有规则" displayName="queryCapRules" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="480" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.CapRuleService.queryCapRules</process:partner>
      <process:instance instanceName="CapRuleBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
      <process:inputVariable id="1" name="pageCond" type="query" value="com.eos.foundation.PageCond" valueType="Java" pattern="reference">page</process:inputVariable>
      <process:inputVariable id="2" name="sortField" type="query" value="java.lang.String" valueType="Java" pattern="reference">sortField</process:inputVariable>
      <process:inputVariable id="3" name="sortOrder" type="query" value="java.lang.String" valueType="Java" pattern="reference">sortOrder</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.flowconfig.flowconfigEntity.CapRule[]" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="458" y="186"/>
    <figSize height="17" width="73"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="判断规则数目" displayName="countCapRules" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="361" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.CapRuleService.countCapRules</process:partner>
      <process:instance instanceName="CapRuleBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">total</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="339" y="186"/>
    <figSize height="17" width="73"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="解析流程配置文件" displayName="parseForm" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="150" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.ProcessConfigServiceBean.parseForm</process:partner>
      <process:instance instanceName="ProcessConfigServiceBeanBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="proc" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">proc</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="116" y="186"/>
    <figSize height="17" width="97"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="257" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">proc/nameSpace</process:from>
      <process:to type="query">criteria/_expr[2]/namespace</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">=</process:from>
      <process:to type="query">criteria/_expr[2]/_op</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="259" y="186"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="wanghl" createTime="2013-03-04 15:53:43" date="2013-03-04Z" description="" name="queryCapRules" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
    <process:input description="" isArray="false" name="sortField" primitiveType="String"/>
    <process:input description="" isArray="false" name="sortOrder" primitiveType="String"/>
    <process:input description="" isArray="false" modelType="com.eos.foundation.PageCond" name="page"/>
    <process:input anyType="commonj.sdo.DataObject" description="" isArray="false" name="proc"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="org.gocom.components.coframe.flowconfig.flowconfigEntity.CapRule" description="" isArray="true" name="data"/>
    <process:output description="" isArray="false" name="total" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

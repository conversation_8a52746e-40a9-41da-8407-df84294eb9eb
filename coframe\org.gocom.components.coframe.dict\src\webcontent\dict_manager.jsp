<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page pageEncoding="UTF-8"%>
<%@include file="/coframe/dict/common.jsp"%>
<html xmlns="http://www.w3.org/1999/xhtml">
<!-- 
  - Author(s): 陈鹏
  - Date: 2013-11-28 10:21:23
  - Description:
-->
<head>
<title>业务字典配置</title>
</head>
<body style="margin:0px;height:100%;">

<div id="tabs" class="nui-tabs" activeIndex="0" style="width:100%;height:100%;" plain="true">
    <div title="业务字典管理" >
    	<table border="0" style="width:100%;height:100%;">
    	<tr>
    	<td style="width:50%;height:100%;" valign="top">
			<div class="nui-panel" title="业务字典类型查询" style="width:100%;height:135px;margin-bottom:4px;"
			    showToolbar="false" showCollapseButton="true" showFooter="false" allowResize="false" >
				
				<form id="query_dict_type_form" method="post">
				<table border="0" style="width:250px;" align="center">
				<tr>
				<td style="width:30%;" align="right">类型代码：</td>
				<td><input id="dicttypeid" name="dicttypeid" class="nui-textbox" style="width:160px;" onenter="onKeyEnter"/></td>
				</tr>
				<tr>
				<td align="right">类型名称：</td>
				<td><input id="dicttypename" name="dicttypename" class="nui-textbox" style="width:160px;" onenter="onKeyEnter"/></td>
				</tr>
				<tr height="3px;"><td colspan="2"></td></tr>
				<tr align="center">
				<td colspan="2">
					<a class="nui-button" onclick="searchDictType();">查询</a>&nbsp;
					<a class="nui-button" onclick="exportDict();">导出</a>&nbsp;
					<a class="nui-button" onclick="resetQuery();">重置</a>&nbsp;
					<a class="nui-button" onclick="refreshDictCache();">刷新缓存</a>
				</td>
				</tr>
				</table>
				</form>
				
			</div>
			<div class="nui-fit">
			<div class="nui-panel" title="业务字典类型" style="width:100%;height:100%;"
			    showToolbar="true" showCollapseButton="true" showFooter="false" allowResize="false" >
				<div property="toolbar" >
					<a class="nui-button" iconCls="icon-add" onclick="addDictType()" plain="true">添加</a>
					<a class="nui-button" iconCls="icon-addnew" onclick="addSubDictType()" plain="true">添加子类型</a>
			        <a class="nui-button" iconCls="icon-edit" onclick="editDictType()" plain="true">修改</a>
                    <a class="nui-button" iconCls="icon-remove" onclick="removeDictType()" plain="true">删除</a>
			    </div>
			    
			    <div id="dict_type_tg" class="nui-treegrid" style="width:100%;height:100%;"
				    showPager="true" pageSize="10" sizeList="[10,20,50]" allowAlternating="true" multiSelect="true"
				    url="org.gocom.components.coframe.dict.DictManager.queryDictType.biz.ext"
					onselectionchanged="onDictTypeSelected" ondrawnode="onDictTypeDrawNode" onload="onDictTypeLoad"
				    dataField="data" idField="dicttypeid" treeColumn="dicttypeid">
				    <div property="columns">
				        <div type="checkcolumn" ></div>
				        <div name="dicttypeid" field="dicttypeid" allowSort="true" width="50%">类型代码</div>
				        <div field="dicttypename" allowSort="true" width="50%">类型名称</div>
				    </div>
				</div>
				
			</div>
			</div>
		</td>
		<td style="height:100%;" rowspan="2" valign="top">
			<div class="nui-fit">
			<div class="nui-panel" title="业务字典项" style="width:100%;height:100%;"
			    showToolbar="true" showCollapseButton="true" showFooter="false" allowResize="false" >
				<div property="toolbar" >
			        <a class="nui-button" iconCls="icon-add" onclick="addDict()" plain="true">添加</a>
					<a class="nui-button" iconCls="icon-addnew" onclick="addSubDict()" plain="true">添加子项</a>
			        <a class="nui-button" iconCls="icon-edit" onclick="editDict()" plain="true">修改</a>
                    <a class="nui-button" iconCls="icon-remove" onclick="removeDict()" plain="true">删除</a>
			    </div>
			    
			    <div id="dict_tg" class="nui-treegrid" style="width:100%;height:100%;" autoLoad="false"
			    	 showPager="true" pageSize="15" sizeList="[15,25,50]" allowAlternating="true" multiSelect="true"
				    url="org.gocom.components.coframe.dict.DictManager.queryDict.biz.ext"
				    onselectionchanged="onDictSelected"  ondrawnode="onDictDrawNode" onnodeclick="onDictNodeClick"
				    dataField="data" idField="dictid" treeColumn="dictid">
				    <div property="columns">
				        <div type="checkcolumn" ></div>
				        <div name="dictid" field="dictid" allowSort="true" width="45%">字典项代码</div>
				        <div field="dictname" allowSort="true" width="47%">字典项名称</div>
				        <div field="sortno" allowSort="true" width="8%">排序</div>
				    </div>
				</div>
			    
			</div>
			</div>
		</td>
		</tr>
		</table>
    </div>
    <div title="业务字典导入" >
    	<form id="import_dict_form" action="org.gocom.components.coframe.dict.impl.importDict.flow?_eosFlowAction=importDict" method="post" enctype="multipart/form-data">
        <table border="0" style="width:500px;height:80px;" align="center">
		<tr>
		<td width="35%" align="right">请选择您要导入的Excel文件:</td>
		<td><input type="file" id="dict_file" name="dict_file" size="60" style="width:300px;"></td>
		</tr>
		<tr align="center">
		<td colspan="2">
			<a class="nui-button" onclick="startUpload();">导入</a>&nbsp;
			<a class="nui-button" onclick="resetImport();">重置</a>
		</td>
		</tr>
        </table>
    </div>
</div>

</body>
</html>

<script type="text/javascript">
	nui.parse();
	
	var dict_type_tg = nui.get("dict_type_tg");
	var dict_tg = nui.get("dict_tg");
	var seldicttypeid;
	var seldictid;
	
	function onKeyEnter(e) {
		searchDictType();
	}
	function searchDictType() {
        var dicttypeid = nui.get("dicttypeid").getValue();
        var dicttypename = nui.get("dicttypename").getValue();
        dict_type_tg.load({ id: dicttypeid, name: dicttypename});
        seldicttypeid = null;
        seldictid = null;
    }
    function exportDict() {        
        var form = document.getElementById("query_dict_type_form");
		form.action = "org.gocom.components.coframe.dict.impl.exportDict.flow";
        form.submit();
    }
    function resetQuery() {
    	var form = new nui.Form("query_dict_type_form");
    	form.reset();
    }
    function refreshDictCache(){
    	nui.ajax({
			url: "org.gocom.components.coframe.dict.DictManager.refreshDictCache.biz.ext",
			type: "post",
			cache: false,
			contentType: 'text/json',
			success: function (json) {
				if(json.status == 'success'){
				nui.alert("刷新缓存成功！");
			}
			else nui.alert("刷新缓存失败！");
			},
			error: function () {
				nui.alert("刷新缓存失败！");
			}
		});
    }
    function onDictTypeSelected(e) {
        var grid = e.sender;
		var record = grid.getSelected();
        if (record) {
	        dict_tg.load({ dicttypeid: record.dicttypeid });
	        seldicttypeid = record.dicttypeid;
        }
        else{
        	dict_tg.clearData();
        	seldicttypeid = null;
        }
        
        seldictid = null;
    }
	function onDictTypeDrawNode(e){//节点加载完清空参数，避免影响查询和翻页
		dict_type_tg._dataSource.loadParams.dicttypeid = null;
	}
	function onDictTypeLoad(e){//加载第一个类型的字典项
		nui.parse();		
		if(e.data[0] != null) {
			nui.get("dict_tg").load({ dicttypeid: e.data[0].dicttypeid });
			seldicttypeid = null;
	        seldictid = null;
		}
	}
	function checkDictTypeSelected() {
		if(seldicttypeid == null){
			nui.alert("请先选择一条业务字典类型记录！");
			return false;
		}
		else return true;
	}
    function addDictType() {
		nui.open({
			url: "<%=contextPath%>/coframe/dict/edit_dict_type.jsp",
			title: "添加字典类型", width: 320, height: 180,
			onload: function () {
				var iframe = this.getIFrameEl();
				var data = {action:"add"};
				iframe.contentWindow.loadForm(data);
			},
			ondestroy: function (action) {
				dict_type_tg.reload();
			}
		});
    }
    function addSubDictType() {
		if(checkDictTypeSelected()){
			nui.open({
				url: "<%=contextPath%>/coframe/dict/edit_dict_type.jsp",
				title: "添加子类型", width: 320, height: 180,
				onload: function () {
					var iframe = this.getIFrameEl();
					var parent = dict_type_tg.getSelected();
					var data = {rank:parent.rank, parentid:parent.dicttypeid, seqno:parent.seqno};
					data.action = 'add';
					iframe.contentWindow.loadForm(data);
				},
				ondestroy: function (action) {
					dict_type_tg.reload();
				}
			});
		}
    }
    function editDictType() {
    	if(checkDictTypeSelected()){
			nui.open({
				url: "<%=contextPath%>/coframe/dict/edit_dict_type.jsp",
				title: "修改字典类型", width: 320, height: 180,
				onload: function () {
					var iframe = this.getIFrameEl();
					var data = nui.clone(dict_type_tg.getSelected());
					data.action = 'edit';
					iframe.contentWindow.loadForm(data);
				},
				ondestroy: function (action) {
					dict_type_tg.reload();
				}
			});
		}
    }
    function removeDictType() {
	    if(checkDictTypeSelected()){
	    	var rows = dict_type_tg.getSelecteds();
	    	
	    	nui.confirm("所有关联的业务字典类型和业务字典项都将被删除，确认删除业务字典类型？","", function(action){
	    		if(action == 'ok'){
					nui.ajax({
						url: "org.gocom.components.coframe.dict.DictManager.removeDictType.biz.ext",
						type: "post",
						data: nui.encode({data:rows}),
						cache: false,
						contentType: 'text/json',
						success: function (json) {
							if(json.status == 'success'){
								seldicttypeid = null;
								dict_tg.clearData();
								dict_type_tg.reload();								
							}
							else nui.alert("删除失败！");
						},
						error: function () {
							nui.alert("删除失败！");
						}
					});
				}
			});
	    }
    }
    
    function onDictSelected(e) {
        var grid = e.sender;
        var record = grid.getSelected();
        if (record) {
	        seldictid = record.dictid;
        }
        else seldictid = null;
    }
    function onDictDrawNode(e) {//节点加载完清空参数，避免影响查询和翻页
	    dict_tg._dataSource.loadParams.dictid = null;
	    dict_tg._dataSource.loadParams.parenttypeid = null;
    }
    function onDictNodeClick(e) {
	    dict_tg._dataSource.loadParams.parenttypeid = e.node.eosDictType.dicttypeid;
    }
    function checkDictSelected() {
		if(seldictid == null){
			nui.alert("请先选择一条业务字典项记录！");
			return false;
		}
		else return true;
	}
    function addDict() {
    	if(checkDictTypeSelected()){
			nui.open({
				url: "<%=contextPath%>/coframe/dict/edit_dict.jsp",
				title: "添加字典项", width: 320, height: 230,
				onload: function () {
					var iframe = this.getIFrameEl();
					var data = {action:"add"};
					data.eosDictType = dict_type_tg.getSelected();
					iframe.contentWindow.loadForm(data);
				},
				ondestroy: function (action) {
					dict_tg.reload();
				}
			});
		}
    }
    function addSubDict() {
		if(checkDictSelected()){
			nui.open({
				url: "<%=contextPath%>/coframe/dict/edit_dict.jsp",
				title: "添加子项", width: 320, height: 230,
				onload: function () {
					var iframe = this.getIFrameEl();
					var parent = dict_tg.getSelected();
					var data = {rank:parent.rank, parentid:parent.dictid, seqno:parent.seqno};
					data.action = 'add';
					data.parentdicttypeid = parent.eosDictType.dicttypeid;
					iframe.contentWindow.loadForm(data);
				},
				ondestroy: function (action) {
					dict_tg.reload();
				}
			});
		}
    }
    function editDict() {
    	if(checkDictSelected()){
			nui.open({
				url: "<%=contextPath%>/coframe/dict/edit_dict.jsp",
				title: "修改字典项", width: 320, height: 230,
				onload: function () {
					var iframe = this.getIFrameEl();
					var data = nui.clone(dict_tg.getSelected());
					data.action = 'edit';
					iframe.contentWindow.loadForm(data);
				},
				ondestroy: function (action) {
					dict_tg.reload();
				}
			});
		}
    }
    function removeDict() {
	    if(checkDictSelected()){
	    	var rows = dict_tg.getSelecteds();

	    	nui.confirm("所有关联的业务字典项都将被删除，确认删除业务字典项？","", function(action){
	    		if(action == 'ok'){
					nui.ajax({
						url: "org.gocom.components.coframe.dict.DictManager.removeDict.biz.ext",
						type: "post",
						data: nui.encode({data:rows}),
						cache: false,
						contentType: 'text/json',
						success: function (json) {
							if(json.status == 'success'){
								seldictid = null;
								dict_tg.reload();
							}
							else nui.alert("删除失败！");
						},
						error: function () {
							nui.alert("删除失败！");
						}
					});
				}
			});
	    }
    }
	
	function startUpload() {
        var form = $("#import_dict_form");
        var file = $("#dict_file").val();
        
        if (file == "") {
			nui.alert("请选择文件！");
			return;
		}
		var reg = /.xls$/;
		if (!reg.test(file))
		{
			nui.alert('请选择Excel格式文件！');
			return;
		}
		form.submit();
    }
    function resetImport() {
    	var obj = document.getElementById('dict_file') ;  
		obj.select();  
		document.selection.clear();  

    }
    
    var retcode = '<%= request.getAttribute("retCode") %>';
    if(retcode != "null"){
    	var tabs = nui.get("tabs");
    	tabs.activeTab(tabs.tabs[1]);
    	
        if(retcode == -1){
        	nui.alert('导入失败！');
        }else{
        	nui.alert('导入成功！');
        }
    }
</script>
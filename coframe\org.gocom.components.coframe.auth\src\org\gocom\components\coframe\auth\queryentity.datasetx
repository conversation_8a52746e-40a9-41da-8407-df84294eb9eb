<?xml version="1.0" encoding="UTF-8"?>
<model:DataSetDiagram xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://www.primeton.com/datamodel" xmlns:model_1="http://com.primeton.emf.core" name="queryentity" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" displayName="querymgr" author="guwei">
  <nodes xsi:type="model_1:Note" id="node0" name="注释" nodeType="note" content="因为cap_partyauth表中的party_id字段是字符串型，而员工、工作组、机构和岗位表中的是数值型，查询的时候要转换，DB2中要先转为整数再转为字符串" title="caozw&#x9;13-5-30 下午2:29">
    <location x="374" y="449"/>
    <size height="122" width="197"/>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity0" name="QueryUserByEmp" nodeType="table" displayName="Entity" author="guwei" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryUserByEmpImpl" table="">
    <location x="135" y="66"/>
    <size height="138" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property0" name="empid" displayName="empid" columnName="empid" nullAble="true" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="empid" entityField="empid" id="empid" label="empid" name="empid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="empid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property1" name="tenantId" displayName="tenantId" columnName="tenant_id" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property2" name="userId" displayName="userId" columnName="user_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userId" entityField="userId" id="userId" label="userId" name="userId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="userId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property3" name="userName" displayName="userName" columnName="user_name" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userName" entityField="userName" id="userName" label="userName" name="userName" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="userName" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property4" name="email" displayName="email" columnName="email" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="email" entityField="email" id="email" label="email" name="email" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="email" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property17" name="operatorId" displayName="operatorId" columnName="operator_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="operatorId" entityField="operatorId" id="operatorId" label="operatorId" name="operatorId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="operatorId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select b.empid, b.tenant_id, a.user_id, a.user_name, a.email, a.operator_id &#xD;
from cap_user a left join org_employee b &#xD;
on a.user_id=b.userid&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity1" name="QueryUserByRole" nodeType="table" displayName="Entity" author="guwei" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryUserByRoleImpl" table="">
    <location x="373" y="66"/>
    <size height="156" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property5" name="roleId" displayName="roleId" columnName="ROLE_ID" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="roleId" entityField="roleId" id="roleId" label="roleId" name="roleId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="roleId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property6" name="partyType" displayName="partyType" columnName="PARTY_TYPE" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="partyType" entityField="partyType" id="partyType" label="partyType" name="partyType" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="partyType" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property7" name="userId" displayName="userId" columnName="USER_ID" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userId" entityField="userId" id="userId" label="userId" name="userId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="userId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property8" name="userName" displayName="userName" columnName="USER_NAME" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userName" entityField="userName" id="userName" label="userName" name="userName" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="userName" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property9" name="tenantId" displayName="tenantId" columnName="TENANT_ID" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property10" name="operatorId" displayName="operatorId" columnName="OPERATOR_ID" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="operatorId" entityField="operatorId" id="operatorId" label="operatorId" name="operatorId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="operatorId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property18" name="email" displayName="email" columnName="EMAIL" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="128" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="email" entityField="email" id="email" label="email" name="email" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="email" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.user_id, a.user_name, a.tenant_id, a.email as email, a.operator_id from cap_user a, cap_partyauth b where a.user_id=b.party_id and a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity2" name="QueryEmpByRole" nodeType="table" displayName="Entity" author="guwei" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryEmpByRoleImpl" table="">
    <location x="127" y="242"/>
    <size height="138" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property11" name="roleId" displayName="roleId" columnName="role_id" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="roleId" entityField="roleId" id="roleId" label="roleId" name="roleId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="roleId" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="indexed"/>
          <attr defaultValue="" name="validateAttr"/>
          <attr name="accesskey"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="title"/>
          <attr defaultValue="" name="maxlength"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="onmouseup"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="" name="onmousemove"/>
          <attr defaultValue="" name="extAttr"/>
          <attr defaultValue="" name="ondblclick"/>
          <attr defaultValue="" name="onmouseout"/>
          <attr defaultValue="" name="styleClass"/>
          <attr defaultValue="" name="tabindex"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onkeypress"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="" name="onchange"/>
          <attr defaultValue="" name="onmouseover"/>
          <attr defaultValue="" name="size"/>
          <attr defaultValue="" name="onselect"/>
          <attr defaultValue="" name="onclick"/>
          <attr defaultValue="" name="onmousedown"/>
          <attr defaultValue="" name="readonly"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="disabled"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property12" name="partyType" displayName="partyType" columnName="party_type" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="partyType" entityField="partyType" id="partyType" label="partyType" name="partyType" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="partyType" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="indexed"/>
          <attr defaultValue="" name="validateAttr"/>
          <attr name="accesskey"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="title"/>
          <attr defaultValue="" name="maxlength"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="onmouseup"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="" name="onmousemove"/>
          <attr defaultValue="" name="extAttr"/>
          <attr defaultValue="" name="ondblclick"/>
          <attr defaultValue="" name="onmouseout"/>
          <attr defaultValue="" name="styleClass"/>
          <attr defaultValue="" name="tabindex"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onkeypress"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="" name="onchange"/>
          <attr defaultValue="" name="onmouseover"/>
          <attr defaultValue="" name="size"/>
          <attr defaultValue="" name="onselect"/>
          <attr defaultValue="" name="onclick"/>
          <attr defaultValue="" name="onmousedown"/>
          <attr defaultValue="" name="readonly"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="disabled"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property13" name="empid" displayName="empid" columnName="empid" nullAble="false" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="empid" entityField="empid" id="empid" label="empid" name="empid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="empid" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="indexed"/>
          <attr defaultValue="" name="validateAttr"/>
          <attr name="accesskey"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="title"/>
          <attr defaultValue="" name="maxlength"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="onmouseup"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="" name="onmousemove"/>
          <attr defaultValue="" name="extAttr"/>
          <attr defaultValue="" name="ondblclick"/>
          <attr defaultValue="" name="onmouseout"/>
          <attr defaultValue="" name="styleClass"/>
          <attr defaultValue="" name="tabindex"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onkeypress"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="" name="onchange"/>
          <attr defaultValue="" name="onmouseover"/>
          <attr defaultValue="" name="size"/>
          <attr defaultValue="" name="onselect"/>
          <attr defaultValue="" name="onclick"/>
          <attr defaultValue="" name="onmousedown"/>
          <attr defaultValue="" name="readonly"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="disabled"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property14" name="empcode" displayName="empcode" columnName="empcode" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="30" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="empcode" entityField="empcode" id="empcode" label="empcode" name="empcode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="empcode" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="indexed"/>
          <attr defaultValue="" name="validateAttr"/>
          <attr name="accesskey"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="title"/>
          <attr defaultValue="" name="maxlength"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="onmouseup"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="" name="onmousemove"/>
          <attr defaultValue="" name="extAttr"/>
          <attr defaultValue="" name="ondblclick"/>
          <attr defaultValue="" name="onmouseout"/>
          <attr defaultValue="" name="styleClass"/>
          <attr defaultValue="" name="tabindex"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onkeypress"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="" name="onchange"/>
          <attr defaultValue="" name="onmouseover"/>
          <attr defaultValue="" name="size"/>
          <attr defaultValue="" name="onselect"/>
          <attr defaultValue="" name="onclick"/>
          <attr defaultValue="" name="onmousedown"/>
          <attr defaultValue="" name="readonly"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="disabled"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property15" name="empname" displayName="empname" columnName="empname" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="50" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="empname" entityField="empname" id="empname" label="empname" name="empname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="empname" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="indexed"/>
          <attr defaultValue="" name="validateAttr"/>
          <attr name="accesskey"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="title"/>
          <attr defaultValue="" name="maxlength"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="onmouseup"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="" name="onmousemove"/>
          <attr defaultValue="" name="extAttr"/>
          <attr defaultValue="" name="ondblclick"/>
          <attr defaultValue="" name="onmouseout"/>
          <attr defaultValue="" name="styleClass"/>
          <attr defaultValue="" name="tabindex"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onkeypress"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="" name="onchange"/>
          <attr defaultValue="" name="onmouseover"/>
          <attr defaultValue="" name="size"/>
          <attr defaultValue="" name="onselect"/>
          <attr defaultValue="" name="onclick"/>
          <attr defaultValue="" name="onmousedown"/>
          <attr defaultValue="" name="readonly"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="disabled"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property16" name="tenantId" displayName="tenantId" columnName="tenant_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="indexed"/>
          <attr defaultValue="" name="validateAttr"/>
          <attr name="accesskey"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="title"/>
          <attr defaultValue="" name="maxlength"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="onmouseup"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="" name="onmousemove"/>
          <attr defaultValue="" name="extAttr"/>
          <attr defaultValue="" name="ondblclick"/>
          <attr defaultValue="" name="onmouseout"/>
          <attr defaultValue="" name="styleClass"/>
          <attr defaultValue="" name="tabindex"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onkeypress"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="" name="onchange"/>
          <attr defaultValue="" name="onmouseover"/>
          <attr defaultValue="" name="size"/>
          <attr defaultValue="" name="onselect"/>
          <attr defaultValue="" name="onclick"/>
          <attr defaultValue="" name="onmousedown"/>
          <attr defaultValue="" name="readonly"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="disabled"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.empid, a.empcode, a.empname, a.tenant_id &#xD;
from org_employee a left join cap_partyauth b &#xD;
on b.party_id=cast(a.empid as char)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id as role_id, b.party_type as party_type, a.empid as empid, a.empcode as empcode, a.empname as empname, a.tenant_id as tenant_id&#xD;
from org_employee a left join cap_partyauth b &#xD;
on b.party_id = char(cast(a.empid as integer))&#xD;
where a.tenant_id = b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.empid, a.empcode, a.empname, a.tenant_id &#xD;
from org_employee a left join cap_partyauth b &#xD;
on b.party_id=TO_CHAR(a.empid)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="default" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity3" name="QueryEmpByOrgRole" nodeType="table" displayName="Entity" author="guwei" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryEmpByOrgRoleImpl" table="">
    <location x="373" y="242"/>
    <size height="156" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property19" name="roleId" displayName="roleId" columnName="role_id" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="roleId" entityField="roleId" id="roleId" label="roleId" name="roleId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="roleId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property20" name="partyType" displayName="partyType" columnName="party_type" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="partyType" entityField="partyType" id="partyType" label="partyType" name="partyType" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="partyType" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property21" name="orgid" displayName="orgid" columnName="orgid" nullAble="true" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="orgid" entityField="orgid" id="orgid" label="orgid" name="orgid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="orgid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property22" name="empid" displayName="empid" columnName="empid" nullAble="false" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="empid" entityField="empid" id="empid" label="empid" name="empid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="empid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property23" name="empcode" displayName="empcode" columnName="empcode" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="30" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="empcode" entityField="empcode" id="empcode" label="empcode" name="empcode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="empcode" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property24" name="empname" displayName="empname" columnName="empname" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="50" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="empname" entityField="empname" id="empname" label="empname" name="empname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="empname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property25" name="tenantId" displayName="tenantId" columnName="tenant_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select d.role_id, d.party_type, c.orgid, a.empid, a.empcode, a.empname, a.tenant_id &#xD;
from org_employee a left join org_emporg b&#xD;
on a.empid = b.empid&#xD;
left join org_organization c &#xD;
on b.orgid=c.orgid &#xD;
left join cap_partyauth d&#xD;
on cast(a.empid as char) = d.party_id&#xD;
where a.tenant_id=b.tenant_id &#xD;
        and a.tenant_id=c.tenant_id&#xD;
        and a.tenant_id=d.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="" needSave="true">
      <sql>select d.role_id, d.party_type, c.orgid, a.empid, a.empcode, a.empname, a.tenant_id &#xD;
from org_employee a left join org_emporg b&#xD;
on a.empid = b.empid&#xD;
left join org_organization c &#xD;
on b.orgid=c.orgid &#xD;
left join cap_partyauth d&#xD;
on char(cast(a.empid as integer)) = d.party_id&#xD;
where a.tenant_id=b.tenant_id &#xD;
        and a.tenant_id=c.tenant_id&#xD;
        and a.tenant_id=d.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="" needSave="true">
      <sql>select d.role_id, d.party_type, c.orgid, a.empid, a.empcode, a.empname, a.tenant_id &#xD;
from org_employee a left join org_emporg b&#xD;
on a.empid = b.empid&#xD;
left join org_organization c &#xD;
on b.orgid=c.orgid &#xD;
left join cap_partyauth d&#xD;
on TO_CHAR(a.empid) = d.party_id&#xD;
where a.tenant_id=b.tenant_id &#xD;
        and a.tenant_id=c.tenant_id&#xD;
        and a.tenant_id=d.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity4" name="QueryEmpAndUserByUser" nodeType="table" displayName="Entity" author="guwei" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryEmpAndUserByUserImpl" table="">
    <location x="120" y="405"/>
    <size height="138" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property26" name="userid" displayName="userid" columnName="userId" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userid" entityField="userid" id="userid" label="userid" name="userid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="userid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property27" name="userName" displayName="userName" columnName="user_name" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userName" entityField="userName" id="userName" label="userName" name="userName" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="userName" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property28" name="empid" displayName="empid" columnName="empid" nullAble="true" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="empid" entityField="empid" id="empid" label="empid" name="empid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="empid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property29" name="empcode" displayName="empcode" columnName="empcode" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="30" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="empcode" entityField="empcode" id="empcode" label="empcode" name="empcode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="empcode" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property30" name="empname" displayName="empname" columnName="empname" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="50" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="empname" entityField="empname" id="empname" label="empname" name="empname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="empname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property31" name="tenantId" displayName="tenantId" columnName="tenant_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select a.user_id userId, a.tenant_id, a.user_name, b.empid, b.empcode, b.empname&#xD;
from cap_user a left join org_employee b &#xD;
on a.user_id = b.userid&#xD;
where a.tenant_id = b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity5" name="QueryOrgByRole" nodeType="table" displayName="Entity" author="YANGZHOU" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryOrgByRoleImpl" table="">
    <location x="-75" y="75"/>
    <size height="138" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property32" name="roleId" displayName="roleId" columnName="role_id" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="roleId" entityField="roleId" id="roleId" label="roleId" name="roleId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="roleId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property33" name="partyType" displayName="partyType" columnName="party_type" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="partyType" entityField="partyType" id="partyType" label="partyType" name="partyType" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="partyType" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property34" name="orgid" displayName="orgid" columnName="orgid" nullAble="false" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="orgid" entityField="orgid" id="orgid" label="orgid" name="orgid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="orgid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property35" name="orgcode" displayName="orgcode" columnName="orgcode" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="32" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="orgcode" entityField="orgcode" id="orgcode" label="orgcode" name="orgcode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="orgcode" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property36" name="orgname" displayName="orgname" columnName="orgname" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="orgname" entityField="orgname" id="orgname" label="orgname" name="orgname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="orgname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property37" name="tenantId" displayName="tenantId" columnName="tenant_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.orgid, a.orgcode, a.orgname, a.tenant_id &#xD;
from org_organization a left join cap_partyauth b&#xD;
on b.party_id=cast(a.orgid as char)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="" needSave="true">
      <sql>&#xD;
select b.role_id, b.party_type, a.orgid, a.orgcode, a.orgname, a.tenant_id &#xD;
from org_organization a left join cap_partyauth b&#xD;
on b.party_id=char(cast(a.orgid as integer))&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.orgid, a.orgcode, a.orgname, a.tenant_id &#xD;
from org_organization a left join cap_partyauth b&#xD;
on b.party_id=TO_CHAR(a.orgid)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity6" name="QueryPositionByRole" nodeType="table" displayName="Entity" author="YANGZHOU" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryPositionByRoleImpl" table="">
    <location x="-75" y="255"/>
    <size height="138" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property38" name="roleId" displayName="roleId" columnName="role_id" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="roleId" entityField="roleId" id="roleId" label="roleId" name="roleId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="roleId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property39" name="partyType" displayName="partyType" columnName="party_type" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="partyType" entityField="partyType" id="partyType" label="partyType" name="partyType" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="partyType" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property40" name="positionid" displayName="positionid" columnName="positionid" nullAble="false" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="positionid" entityField="positionid" id="positionid" label="positionid" name="positionid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="positionid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property41" name="posicode" displayName="posicode" columnName="posicode" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="20" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="posicode" entityField="posicode" id="posicode" label="posicode" name="posicode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="posicode" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property42" name="posiname" displayName="posiname" columnName="posiname" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="posiname" entityField="posiname" id="posiname" label="posiname" name="posiname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="posiname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property43" name="tenantId" displayName="tenantId" columnName="tenant_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.positionid, a.posicode, a.posiname, a.tenant_id &#xD;
from org_position a left join cap_partyauth b&#xD;
on b.party_id=cast(a.positionid as char)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.positionid, a.posicode, a.posiname, a.tenant_id &#xD;
from org_position a left join cap_partyauth b&#xD;
on b.party_id=char(cast(a.positionid as integer))&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.positionid, a.posicode, a.posiname, a.tenant_id &#xD;
from org_position a left join cap_partyauth b&#xD;
on b.party_id=TO_CHAR(a.positionid)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:NPEntityNode" id="Entity7" name="PartyDataObject" nodeType="table" displayName="Entity" author="YANGZHOU" entType="NPEntity" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.PartyDataObjectImpl">
    <location x="626" y="91"/>
    <size height="120" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property44" name="id" displayName="id" persistent="false" userDefined="true" nullAble="true" PK="false" columnType="" eosDataType="String" dasType="string" overWriteDefaultColumnType="false" showType="String">
      <viewField dict="false" dictTypeId="" displayName="property0" entityField="id" id="id" label="id" name="property0" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="property0" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property45" name="code" displayName="code" persistent="false" userDefined="true" nullAble="true" PK="false" columnType="" eosDataType="String" dasType="string" overWriteDefaultColumnType="false" showType="String">
      <viewField dict="false" dictTypeId="" displayName="property0" entityField="code" id="code" label="code" name="property0" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="property0" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property46" name="name" displayName="name" persistent="false" userDefined="true" nullAble="true" PK="false" columnType="" eosDataType="String" dasType="string" overWriteDefaultColumnType="false" showType="String">
      <viewField dict="false" dictTypeId="" displayName="property1" entityField="name" id="name" label="name" name="property1" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="property1" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property47" name="partyTypeID" displayName="partyTypeID" persistent="false" userDefined="true" nullAble="true" PK="false" columnType="" eosDataType="String" dasType="string" overWriteDefaultColumnType="false" showType="String">
      <viewField dict="false" dictTypeId="" displayName="property1" entityField="partyTypeID" id="partyTypeID" label="partyTypeID" name="property1" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="property1" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property48" name="tenantID" displayName="tenantID" persistent="false" userDefined="true" nullAble="true" PK="false" columnType="" eosDataType="String" dasType="string" overWriteDefaultColumnType="false" showType="String">
      <viewField dict="false" dictTypeId="" displayName="property0" entityField="tenantID" id="tenantID" label="tenantID" name="property0" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="property0" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity500" name="QueryGroupByRole" collapsed="false" nodeType="table" displayName="Entity" author="YANGZHOU" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="org.gocom.components.coframe.auth.queryentity.impl.QueryGroupByRoleImpl" table="">
    <location x="600" y="253"/>
    <size height="120" width="176"/>
    <figSize height="20" width="176"/>
    <columnProperty id="property49" name="roleId" displayName="roleId" columnName="role_id" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="roleId" entityField="roleId" id="roleId" label="roleId" name="roleId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="roleId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property50" name="partyType" displayName="partyType" columnName="party_type" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="partyType" entityField="partyType" id="partyType" label="partyType" name="partyType" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="partyType" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property51" name="groupid" displayName="groupid" columnName="groupid" nullAble="false" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="groupid" entityField="groupid" id="groupid" label="groupid" name="groupid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="groupid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property52" name="groupname" displayName="groupname" columnName="groupname" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="50" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="groupname" entityField="groupname" id="groupname" label="groupname" name="groupname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="groupname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property53" name="tenantId" displayName="tenantId" columnName="tenant_id" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.groupid,  a.groupname, a.tenant_id &#xD;
from org_group a left join cap_partyauth b&#xD;
on b.party_id=cast(a.groupid as char)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.groupid, a.groupname, a.tenant_id &#xD;
from org_group a left join cap_partyauth b&#xD;
on b.party_id=char(cast(a.groupid as integer))&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="" needSave="true">
      <sql>select b.role_id, b.party_type, a.groupid, a.groupname, a.tenant_id &#xD;
from org_group a left join cap_partyauth b&#xD;
on b.party_id=TO_CHAR(a.groupid)&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity8" name="QueryEmpObject" nodeType="table" displayName="Entity" author="转移到QQ-17362686" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="" table="">
    <location x="-75" y="405"/>
    <size height="228" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property54" name="empid" displayName="empid" columnName="EMPID" nullAble="false" PK="false" columnType="NUMBER" eosDataType="Int" length="10" dasType="int" showType="Int">
      <viewField dict="false" dictTypeId="" displayName="empid" entityField="empid" id="empid" label="empid" name="empid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property55" name="userId" displayName="userId" columnName="USER_ID" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userId" entityField="userId" id="userId" label="userId" name="userId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property56" name="userName" displayName="userName" columnName="USER_NAME" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="userName" entityField="userName" id="userName" label="userName" name="userName" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property57" name="email" displayName="email" columnName="EMAIL" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="255" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="email" entityField="email" id="email" label="email" name="email" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property58" name="operatorId" displayName="operatorId" columnName="OPERATOR_ID" nullAble="false" PK="false" columnType="NUMBER" eosDataType="Long" length="18" dasType="long" showType="Long">
      <viewField dict="false" dictTypeId="" displayName="operatorId" entityField="operatorId" id="operatorId" label="operatorId" name="operatorId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property59" name="orgid" displayName="orgid" columnName="ORGID" nullAble="true" PK="false" columnType="NUMBER" eosDataType="Int" length="10" dasType="int" showType="Int">
      <viewField dict="false" dictTypeId="" displayName="orgid" entityField="orgid" id="orgid" label="orgid" name="orgid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property60" name="orgname" displayName="orgname" columnName="ORGNAME" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="orgname" entityField="orgname" id="orgname" label="orgname" name="orgname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property61" name="areaid" displayName="areaid" columnName="AREAID" nullAble="true" PK="false" columnType="NUMBER" eosDataType="Int" precision="-127" dasType="int" showType="Int">
      <viewField dict="false" dictTypeId="" displayName="areaid" entityField="areaid" id="areaid" label="areaid" name="areaid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property62" name="empname" displayName="empname" columnName="EMPNAME" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="50" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="empname" entityField="empname" id="empname" label="empname" name="empname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property72" name="orgtype" displayName="orgtype" columnName="ORGTYPE" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="12" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="orgtype" entityField="orgtype" id="orgtype" label="orgtype" name="orgtype" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property73" name="orgseq" displayName="orgseq" columnName="ORGSEQ" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="512" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="orgseq" entityField="orgseq" id="orgseq" label="orgseq" name="orgseq" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select b.empid, b.empname,b.orgid,org.orgname,org.orgtype,org.orgseq,&#xD;
a.user_id, a.user_name, a.email, a.operator_id,&#xD;
1 as areaid&#xD;
from cap_user a &#xD;
inner join org_employee b  on a.user_id=b.userid&#xD;
inner join org_organization org on org.orgid = b.orgid&#xD;
where a.tenant_id=b.tenant_id</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase_ASE" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity9" name="QueryBtnDataByUser" nodeType="table" displayName="Entity" author="gkm" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="" table="">
    <location x="-75" y="660"/>
    <size height="228" width="150"/>
    <figSize height="20" width="158"/>
    <columnProperty id="property63" name="btnid" displayName="btnid" columnName="BTNID" nullAble="false" PK="false" columnType="NUMBER" eosDataType="Decimal" precision="-127" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="btnid" entityField="btnid" id="btnid" label="btnid" name="btnid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property64" name="btnname" displayName="btnname" columnName="BTNNAME" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="30" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="btnname" entityField="btnname" id="btnname" label="btnname" name="btnname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="btnname" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property65" name="btncode" displayName="btncode" columnName="BTNCODE" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="30" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="btncode" entityField="btncode" id="btncode" label="btncode" name="btncode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property66" name="funccode" displayName="funccode" columnName="FUNCCODE" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="100" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="funccode" entityField="funccode" id="funccode" label="funccode" name="funccode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property67" name="imgsrc" displayName="imgsrc" columnName="IMGSRC" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="300" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="imgsrc" entityField="imgsrc" id="imgsrc" label="imgsrc" name="imgsrc" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property68" name="status" displayName="status" columnName="STATUS" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="2" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="status" entityField="status" id="status" label="status" name="status" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property69" name="title" displayName="title" columnName="TITLE" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="30" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="title" entityField="title" id="title" label="title" name="title" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property70" name="funcaction" displayName="funcaction" columnName="FUNCACTION" nullAble="true" PK="false" columnType="VARCHAR2" eosDataType="String" length="256" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="funcaction" entityField="funcaction" id="funcaction" label="funcaction" name="funcaction" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property71" name="displayorder" displayName="displayorder" columnName="DISPLAYORDER" nullAble="true" PK="false" columnType="NUMBER" eosDataType="Decimal" precision="-127" dasType="big_decimal" showType="Decimal">
      <viewField dict="false" dictTypeId="" displayName="displayorder" entityField="displayorder" id="displayorder" label="displayorder" name="displayorder" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property80" name="partyId" displayName="partyId" columnName="PARTY_ID" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="64" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="partyId" entityField="partyId" id="partyId" label="partyId" name="partyId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property81" name="menuid" displayName="menuid" columnName="MENUID" nullAble="false" PK="false" columnType="VARCHAR2" eosDataType="String" length="40" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="menuid" entityField="menuid" id="menuid" label="menuid" name="menuid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>select a.*,b.funcaction,g.party_id,f.menuid&#xD;
from app_button a, &#xD;
     app_function b, &#xD;
     cap_resauth d, &#xD;
     cap_partyauth g,&#xD;
     app_menu f&#xD;
where a.funccode=b.funccode &#xD;
  and d.res_id=a.funccode &#xD;
  and d.party_id=g.role_id&#xD;
  and b.funccode=f.funccode&#xD;
order by a.displayorder&#xD;
</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase_ASE" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node1" name="AppUsualmenu" nodeType="table" displayName="AppUsualmenu" author="Administrator" allUseViewModel="true" table="APP_USUALMENU">
    <location x="127" y="600"/>
    <size height="178" width="150"/>
    <figSize height="20" width="150"/>
    <columnProperty id="property74" name="menuid" displayName="menuid" columnName="MENUID" nullAble="false" PK="true" columnType="VARCHAR2" eosDataType="String" length="20" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="menuid" id="menuid" label="menuid" name="menuid" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property75" name="name" displayName="name" columnName="NAME" nullAble="true" columnType="VARCHAR2" eosDataType="String" length="30" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="name" id="name" label="name" name="name" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property76" name="title" displayName="title" columnName="TITLE" nullAble="true" columnType="VARCHAR2" eosDataType="String" length="30" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="title" id="title" label="title" name="title" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property77" name="url" displayName="url" columnName="URL" nullAble="false" PK="true" columnType="VARCHAR2" eosDataType="String" length="100" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="url" id="url" label="url" name="url" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property78" name="empid" displayName="empid" columnName="EMPID" nullAble="false" PK="true" columnType="VARCHAR2" eosDataType="String" length="30" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="empid" id="empid" label="empid" name="empid" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property79" name="adduserid" displayName="adduserid" columnName="ADDUSERID" nullAble="true" columnType="VARCHAR2" eosDataType="String" length="20" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="adduserid" id="adduserid" label="adduserid" name="adduserid" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property82" name="adddate" displayName="adddate" columnName="ADDDATE" nullAble="true" columnType="DATE" eosDataType="Date" dasType="date" overWriteDefaultColumnType="false" showType="Date" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="adddate" id="adddate" label="adddate" name="adddate" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="date"/>
          <attr attrValue="nui-datepicker" defaultValue="nui-datepicker" name="class"/>
          <attr attrValue="" defaultValue="" name="popup"/>
          <attr attrValue="" defaultValue="" name="popupHeight"/>
          <attr attrValue="100%" defaultValue="100%" name="popupWidth"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="validateOnChanged"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property83" name="depttype" displayName="depttype" columnName="DEPTTYPE" nullAble="false" PK="true" columnType="VARCHAR2" eosDataType="String" length="30" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment xsi:nil="true"/>
      <viewField dict="false" displayName="depttype" id="depttype" label="depttype" name="depttype" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <topRuler/>
  <leftRuler/>
</model:DataSetDiagram>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd"><%@include file="/coframe/tools/skins/common.jsp" %><%@include file="/utils/public2.jsp"%>
<html><!--   - Author(s): gkm  - Date: 2015-09-23 15:34:19  - Description:-->
<!--
 - Date: 2018-08-01 16:25:12
-->
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="<%=contextPath%>/coframe/auth/skin1/js/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="<%=contextPath%>/coframe/auth/skin1/js/layui/css/modules/layer/default/layer.css" media="all">
    <link rel="stylesheet" href="<%=contextPath%>/coframe/auth/skin1/js/zTree_v3/css/zTreeStyle/zTreeStyle.css" type="text/css">

</head>
<body style="padding:10px 0 0 10px;height:100%;overflow:scroll">
    <div class="treeDemoWrap">
        <ul id="treeDemo" class="ztree"></ul>
    </div>
</body>

<script src="<%=contextPath%>/coframe/auth/skin1/js/layui/layui.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/zTree_v3/js/jquery.ztree.core.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/layui/lay/modules/layer.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/contextjs/js/context.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/common.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/layui/lay/modules/tableSelect.js"></script>
<script>
/**
 * Created by wimi on 2019/11/21.
 */
var zTree, rMenu;
var userid = geturl().userid;
var depttype = geturl().depttype;
var zNodes=[];
var zTreeObj;
var setting = {
    callback: {
        onExpand:loadChild
    }
};
$(function () {
    initList();
});
function initList() {
    zNodes= loadList({depttype:depttype,userid:userid});
    if(zNodes.length){
        zTreeObj = $.fn.zTree.init($("#treeDemo"), setting, zNodes);
    }
    zTree = $.fn.zTree.getZTreeObj("treeDemo");
}
//加载子节点
function loadChild(event,treeId,treeNode) {
    var pid = treeNode.id;
    if(!zTree.getNodeByParam("pid",treeNode.id)){
        var json=loadList({depttype:depttype,pid:pid,userid:userid});
        zTree.addNodes(treeNode,json,true);
    }

}
//获取数据
function loadList(data) {
	var json = window.parent.loadMenuList(data);
    json = reloadData(json);
    return json;
}
//重置数据
function reloadData(zNodes) {
    if(zNodes){
        $.each(zNodes,function (index,val) {
            if(val.isleaf==0||val.isLeaf==0){
                var children=[];
                zNodes[index].children=children;
            }
        })
    }
    return zNodes;
}

function getMenuUrl() {
    return zTree.getSelectedNodes()[0];
}
</script>

</html>
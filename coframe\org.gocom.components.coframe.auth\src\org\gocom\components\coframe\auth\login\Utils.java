package org.gocom.components.coframe.auth.login;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;

public class Utils {
	public static String transBytesToStr(byte[] bytes) {
		StringBuffer buffer = new StringBuffer();
		for (int i = 0; i < bytes.length; i++) {
			if (i != 0)
				buffer.append("-");
			int intMac = bytes[i] & 0xff;
			StringBuffer str = new StringBuffer(Integer.toHexString(intMac));
			if (str.length() < 2) {
				for (int j = 0; j < 2 - str.length(); j++) {
					str.insert(0, "0");
				}
			}
			buffer.append(str);
		}
		return buffer.toString().toUpperCase();
	}

	public static String getLocatMac(String macadd) throws SocketException {
		String result = "";
		try {
			if (macadd.length() > 5) {
				Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
				while (allNetInterfaces.hasMoreElements()) {
					NetworkInterface nif = allNetInterfaces.nextElement();
					Enumeration<InetAddress> addresses = nif.getInetAddresses();
					while (addresses.hasMoreElements()) {
						InetAddress addr = addresses.nextElement();
						if (addr instanceof Inet4Address) {
							byte[] macBytes1 = nif.getHardwareAddress();
							if (macBytes1 != null)
								result = transBytesToStr(macBytes1);
							if (result.equals(macadd)) {
								return macadd;
							}
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			InetAddress adress = InetAddress.getLocalHost();
			NetworkInterface net = NetworkInterface.getByInetAddress(adress);
			byte[] macBytes = net.getHardwareAddress();
			result = transBytesToStr(macBytes);
		} catch (UnknownHostException e) {
			result = "00-50-56-88-19-A01";
			e.printStackTrace();
		} catch (SocketException e) {
			result = "00-50-56-88-19-A01";
			e.printStackTrace();
		}
		return result;

	}
}

/*
* @Author: <PERSON>
* @Date:   2016-03-03 16:24:19
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-04-05 11:01:27
*/
body{
    background-color: #eee;
}
.main-body{
}
.navbar{
    font-size: 14px;
    background-color: #09C;
}
.container-fluid{
    padding: 0;
}
.navbar-menu{
    color: #fff;
    padding: 14px;
    font-size: 24px;
    height: 49px;
    float: left;
}
/* .container-fluid .active{
    background-color: #22282e;
}
.container-fluid .active:hover{
    background-color: #37424f;
} */
.navbar-menu:hover{
    color: #fff;
    background-color: #008fbf;
}
.navbar-menu:focus{
    color:#fff;
}
.top-logo{
    display: block;
    padding: 12px 32px;
    float: left;
}
.top-logo img{
    height: 25px;
    width: 138px;
}
.top-logo:hover{
    background-color: #008fbf;
}
.navbar-text{
    color: #fff;
    margin: 0;
}
.navbar-text ul{
    clear: both;
}
.navbar-text li{
    float: left;
    padding:14px 10px;
    border-right: 1px solid #31b0d5;
}
.navbar-text .nav-btn:hover{
    background-color: #008fbf;
}
.navbar-text .nav-btn a{
    color: #fff;
}
#dropdown{
    position: relative;
}
.nav-btn ul{
    display: none;
    position: absolute;
    top: 48px;
    left: 0px;
    background-color: #09C;
    width: 116px;
}
.nav-btn li{
    display: block;
    border-bottom: 1px solid #31b0d5;
    padding: 5px 10px;
    width: 100%;
}
.nav-btn li:hover{
    background-color: #008fbf;
}
.nav-btn li span{
    display: block;
}
.nav-icon-1,.nav-icon-2{
    display: block;
    float: left;
    margin: 2px 5px 0 0;
}

.nav-icon-1{
    font-size: 16px;
}
.nav-icon-2{
    font-size: 18px;
}
.caret{
    margin-left: 5px;
}
.menu-left{
    width: 180px;
    height: 100%;
    background-color: #293038;
    position: fixed;
    float: left;
    left: 0px;
    overflow: auto;
    z-index: 999;
}
.menu-li h4{
    text-align: left;
    color: #eee;
    background-color: #22282e;
    border-bottom: 1px solid #555;
    cursor: pointer;
    font-size: 14px;
    min-height: 48px;
    line-height: 48px;
    font-weight: 100;
    padding: 0 10px;
}
.menu-li h4:hover{
    background-color: #37424f;
    color: #fff;
}
.menu-li span,.menu-li em{
    margin:16px 5px 0 5px;
    display: block;
}
.menu-li span{
    float: right;
}
.menu-li em{
    float: left;
}
.menu-li ul{
    display: none;
    list-style-type:disc;
    border-bottom: 1px solid  #555;
}
.menu-li li{
    display: block;
    text-align: left;
    padding: 3px 10px 3px 32px;
    list-style-type:disc;
}
.menu-li li a{
    font-size: 14px;
    color: #9d9d9d;
}
.menu-li li a:hover,.menu-li ul li a:focus{
    color: #fff;
}
.main-body{
    width: 100%;
    height: 100%;
    margin-left: 180px;
    display: block;
    background-color: #eee;
    overflow: auto;
    z-index: 0;
}
.nav{
    padding:10px 10px  0 10px;
    background-color: #fff;
}
.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus{
    background-color: #eee;
}
.nav  li{
    margin-right: 10px;
}
.nav  li a{
    padding-right: 0px;
}
.nav  li a:hover span,.nav  li.active  a span{
    color: #555;
}

.tab-close{
    color: #fff;
    clear: both;
    margin: 3px 5px;
    font-size: 18px;
    line-height: 10px;
}
.tab-close:hover{
    color: #fff;
}
.tab-body{
    overflow: auto;
    padding: 0 10px;
}
.login-title{
    padding: 20px 0 10px 0;
}
.login-title img{
    padding: 0;
    display: block;
    height: 64px;
}
.login-title h3{
    font-size: 18px;
}
.index-menu{
    width: 70%;
    margin: 20px auto;
    font-size: 14px;
}

.lev2menu:hover,.lev2menu.active,.lev3menu:hover,.lev3menu.active{
    background-color: #d9edf7 !important;
    color: #31708f !important;
    text-decoration:none;
}
.lev2menu a{
    display:block;
    text-align: center;
    /* padding:10px; */
    width: 100%;
    height: 100%;
}
.lev2menu{
    float: left;
    cursor: pointer;
    margin: 5px;
    border: 1px solid #bce8f1;
    border-radius: 4px;
    color: #555;
    padding:10px;
}
.list-group{
    width: 22%;
    margin:0 1.5%;
    margin-bottom: 15px;
    float: left;
}
.panel-title em{
    margin-right: 10px;
    font-size:14px;
}
.tab-tool{
    margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 6px;
    height: 114px;
    overflow: auto;
}
.tab-tool .panel-body{
    padding: 3px;
}
#tab-tool-menu{
    padding: 5px 10px 0 10px;
}
#tab-tool-menu li{
    border-color: #fff;
    margin: 0;
    font-size: 14px;
    padding: 4px 10px;
}
.tab-tool .panel-body li span{
    margin-right: 5px;
    color: #31708f;
}
#tab-tool-input{
    padding: 2px 5px;
}
#tab-tool-input li{
    padding:0 5px;
}
#tab-tool-input li input,#tab-tool-input li select{
    line-height: 24px;
    height: 28px;
    width: 148px;
    padding: 2px;
}
#tab-tool-input li span{
    padding: 0;
    height: 28px;
    width: 74px;
    cursor:default;
}
#tab3 .panel{
    height:86px;
}
.modal-body{
    padding-right: 10px;
}
#modal-input{
    padding: 0;
}
#modal-input li{
    margin: 5px;
    position: relative;
}
#modal-input li span{
    width: 85px;
    padding: 0;
}
#modal-input li input,#modal-input li select{
    width: 192px;
}
#input-group-btn{
    font-size: 12px;
    width: 32px;
    padding: 0;
    position: relative;
    top: 0px;
    background-color: #fff;
}
#input-group-btn:hover{
    background-color: #eee;
}
.error-tit{
    margin-right: 20px;
    font-size: 14px;
}
.modal-title{
    color: #31708f;
    text-align: left;
}
#box-2 .modal-body{
    padding: 0 15px;
}
#box-2 .panel{
    margin:5px 0 15px 0;
    height:320px;
    overflow: auto;
}
#box-2 .panel-body{
    padding: 0;
}
#box-2 #modal-input span,#box-2 #modal-input input,#box-2 #modal-input select{
    height: 28px;
    padding-top: 0;
    padding-bottom: 0;
}
#box-2 #modal-input input,#box-2 #modal-input select{
    width: 120px;
}
#box-2 #modal-input span{
    width: 74px;
}
#box-2 #modal-input{
    float: left;
    padding-top: 4px;
}
#box-2 #tab-tool-menu{
    float: left;
    padding-top: 8px;
    margin-right:20px;
}
.tab-table{
    margin:0;
}
#box-3 #modal-input input,#box4 #modal-input select{
    width: 168px;
}
#box-3 span{
    width: 82px;
}
#box-3 h2{
    width:32px;
    padding: 0;
    text-align:center;
    line-height: 32px;
}

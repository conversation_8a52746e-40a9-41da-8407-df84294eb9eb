/*
* @Author: <PERSON>
* @Date:   2016-03-29 15:03:21
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-03-30 14:48:43
* @main.scss
*/
// 引用
@import "./bootstrap/css/bootstrap.min.css";
@import "./FontAwesome/css/font-awesome.min.css";
@import "./hhq.scss";
@import "./global.scss";
// 变量
	$mainColor:#09c;
	$mainColor2:#31b0d5;
	$mainColor3:#008fbf;
// 样式
ul,ol,li{
	list-style-type: none;
}
p,a,h1,h2,h3,h4,h5,h6,span,em,i,li,div,span{
	font-family:"Microsoft YaHei";
}
h1,h2,h3,h4,h5,h6{
	font-weight:100;
	margin: 0;
}

.h-menu-wrap{
	background-image: none;
}

.navTop{
	background-color: $mainColor;
	min-height: 41px;
	margin: 0;
	position: relative;
	z-index: 99999;
	padding-left: 0px;
	border:0px;
	box-shadow:0px 2px 5px #888;
}
.h-main{
	padding-top: 8px;
}
.menuStart{
	height:41px;
	border:0px;
	border-radius:0px;
	line-height: 41px;
	margin-left: 0px;
	padding: 0 15px;
	span{
		margin-right: 5px;

	}
}
.user{
	height:100%;
	li{
		line-height: 41px;
		height:100%;
		border-right:1px solid $mainColor2;
		text-align: center;
		a{
			display:block;
			float:left;
			height:100%;
			width: 100px;
			margin: 0 5px;
			padding: 0 5px;
			border-right:1px solid $mainColor2;
			padding: 0 10px;
			@include faded();
			&:hover,&:focus{
				text-decoration:none;
				background-color: $mainColor3;
			}
		}
	}
}
#switchLogin,#updatepassword,.user .hendle .login-out,.user .name{
	color:#fff;
	background-image: none;
	margin: 0;
	padding: 0 10px;
}
.nav-icon{
    margin-right: 6px;
    position: relative;
    top:2px;
    color:#fff;
}
.h-meun{
	dl a{
		padding: 0 9px 0 8px;
		font-size:13px;
		cursor:pointer;
		height:32px;
	}
	dl ul.lev3 a{
		padding: 0 9px 0 8px;
		font-size:13px;
		height:32px;
	}
	dl ul.lev3 a.Nav{
		padding: 0 9px 0 8px;
		font-size:13px;
		height:32px;
	}
	dl ul.lev3 a:hover{
		color: #FF7F24;
		font-weight:bold;
	}
	dl a.lev2 {
		font-size:15px;
		height:32px;
		color: #3A5FCD;
	}
	dl a.lev2 a:hover{
		color: #FF7F24;
	}
	dl a:hover{
		color: #FF7F24;
		text-decoration:none;

	}
}
#levmenu,#lev2menu{
	border-top:4px solid #3DA2E1;
	position:absolute;
	display: none;
	background-color: rgba(255,255,255,0.9);
	box-shadow: 2px 2px 3px #888;
	border:1px solid #888;
	min-height:400px;
	top:41px;
	a{
		font-weight:normal;
		&:hover{
			font-weight:normal;
			text-decoration:none;
		}
	}
	dt{
		cursor:pointer;
		span{
			display:none;
			float: right;
			margin-right: 10px;
		}
		&:hover{
			background-color: #EEE;
			span{
				display:block;
			}
		}
	}
}
#levmenu{
	left:0px;
	min-width:160px;
	dt{
	    height: 32px;
	    line-height: 32px;
	    padding-left:10px;
	}
}
#lev2menu{
	min-width:250px;
	max-width:500px;
	left:160px;
	dt{
	    min-height:32px;
	    padding-left:20px;
	    padding-right:40px;
	    line-height: 32px;
		background:#fff;
	}
}
.nav_subcats_div {
    background-color: #EDEDED;
    border-left: 1px solid #DEDEDE;
    border-right: 2px solid #F7F7F7;
    height: 100%;
    left: 0;
    padding: 0 2px 0 0;
    position: absolute;
    width: 0;
}

.dlCls {
	border-top: 1px solid #D4D4D4;
	position:absolute;
	width:110px;
	margin-left:10px;
	bottom:0px;
	padding-left:10px;
    height: 40px;
    line-height: 32px;
}

.lev3 {
	padding-left:10px;
}
.aaa {
	float: left;
	height: 30px;
	line-height: 16px;
	font-size: 12px;
	font-weight: normal;
}
// 响应
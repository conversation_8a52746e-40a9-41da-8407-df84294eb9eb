/**
 * Created by wimi on 2019/1/23.
 */
var searchJson={};

$(function () {
	initForm();
    // loadOrderDetail();
});
function search(){
	searchJson.orderdcds = $("#orderdcds").val().trim();
	if(searchJson.orderdcds){
		loadOrderDetail();
	}
}
$('#orderdcds').bind('keyup', function (event) {
        if (event.keyCode == "13") {
            search();
        }
    });
function initForm() {
    layui.use(['element','table','layer'], function() {
        var $ = layui.jquery, element = layui.element;
        var table = layui.table,layer = layui.layer;
        //监听折叠
        element.on('collapse(test)', function(data){
            var arr=[];
            var json = $(data.content).data("json");
            if (typeof (json) == "string") {
                json = json.replace(/'/g, '"');
                json = JSON.parse(json);
            }
            if(data.show&&json){
                arr.push(json);
                $(data.content).html(loadOne(arr));
            }
        });

        element.on('tab(Tab)', function(data){
            var orderdcd = $(this).data("orderdcd");
            var h1 = $(this).parents("li").prop("outerHTML");
            var h2 = $(this).parents("li").next().prop("outerHTML");
            var h3 = $(this).parents("li").next().next().prop("outerHTML");
            var html = loadSupply({orderdcd:orderdcd})+h1+h2+h3;
            $(".order-detail").html(html);
            initForm();
        })
    });
}

function loadEmpty(json){
    var html ='';
    if(!json.length){
        html='<p >订单编号/明细号：<span class="highlight">'+searchJson.orderdcd+'</span></p>' +
            '<div style="text-align: center;padding: 40px 0;font-size: 20px;font-weight: 200px">' +
            '<i class="layui-icon layui-icon-face-cry"></i>' +
            '<span style="margin-left: 10px;" class="text-red">查无此单据！</span>' +
            '</div>';
    }
    return html;
}
function loadBranch(dd) {
    var html="";
    var json = getDataWithDetail(dd[0],'select_search_branch');
    if(json.length){
        if(json.length>1){
            var h ="";
            $.each(json,function (index,val) {
                var sts ="";
                if(val.skusts==1){
                    sts='<span class="layui-badge layui-bg-yellow">待确认</span>';
                }
                if(val.skusts==2){
                    sts='<span class="layui-badge layui-bg-blue">待配货</span>';
                }
                if(val.skusts==3){
                    sts='<span class="layui-badge layui-bg-maroon">待发货</span>';
                }
                if(val.skusts==4){
                    sts='<span class="layui-badge layui-bg-orange">待收货</span>';
                }
                if(val.skusts==5){
                    sts='<span class="layui-badge layui-bg-green">已完成</span>';
                }
                if(val.skusts==10){
                    sts='<span class="layui-badge layui-bg-red">已拒收</span>';
                }
                if(val.skusts==8){
                    sts='<span class="layui-badge layui-bg-gray">已关闭</span>';
                }
                h+='<li '+(index==0?'class="layui-this"':'')+' data-orderdcd="'+val.orderdcd+'">明细号:'+val.orderdcd+sts+'</li>';
            });
            html+=loadSupply(json[0])+'<li class="layui-timeline-item">'+
                '<i class="layui-icon layui-timeline-axis"><i class="iconfont icon-alibabacloud"></i></i>'+
                '<div class="layui-timeline-content layui-text">'+
                '<h3 class="layui-timeline-title">'+(json[0].orderdate?json[0].orderdate:'')+' 订单拆分 </h3>' +
                '<div class="layui-tab layui-tab-brief" lay-filter="Tab">'+
                '<ul class="layui-tab-title">'+
                 h+
                '</ul>' +
                '</div>' +
                '</li>';
        }else{
            html+=loadSupply(dd[0]);
        }
    }
    return html;
}
function loadSupply(json) {
    var html="";
    var val = json;
    var shtml="",yhtml="",yhtmld="",supplyhtml='';
    var data = getDataWithDetail({orderdcd:val.orderdcd},'select_orderdetail_receipt');
    if(data.length){
        $.each(data,function (index,d) {
            yhtmld+='<tr>' +
                '<td><p class="focus">'+d.boxcode+'</p></td>' +
                '<td><p class="focus">'+d.lotno+'</p></td>' +
                '<td><p class="focus">'+d.expire+'</p></td>' +
                '<td><p class="focus">'+d.mfdate+'</p></td>' +
                '<td><p class="focus">'+d.supplyqty+'</p></td>' +
                '<td><p class="focus">'+d.acceptnum+'</p></td>' +
                '<td><p class="focus">'+d.refusenum+'</p></td>' +
                '<td><p class="focus">'+d.username+'</p></td>' +
                '<td><p class="focus">'+d.rcpdate+'</p></td>' +
                '<td><p class="focus">'+d.acceptways+'</p></td>' +
                '</tr>'
        });
        yhtml+=' <li class="layui-timeline-item"><i class="layui-icon layui-timeline-axis"><i class="iconfont icon-qrcode"></i></i>'+
            '<div class="layui-timeline-content layui-text">'+
            '<h3 class="layui-timeline-title">'+(data[0].rcpdate?data[0].rcpdate:'')+' 扫码验收</h3>' +
            '<p> ' +
            '<span>明细号：<span class="text-blue">'+val.orderdcd+'</span></span>' +
            '<span style="margin-left: 10px;">待验收：<span class="text-yellow">'+(data[0].supplyqtyt-data[0].acceptnumt-data[0].refusenumt)+'</span></span>' +
            '<span style="margin-left: 10px;">已验收：<span class="text-green">'+data[0].acceptnumt+'</span></span>' +
            '<span style="margin-left: 10px;">已拒收：<span class="text-red">'+data[0].refusenumt+'</span></span>' +
            '</p>'+
            '<table class="layui-table">'+
            '<tr>'+
            '<td>箱码</td>'+
            '<td>批号</td>'+
            '<td>效期</td>'+
            '<td>生产日期</td>'+
            '<td>配送数量</td>'+
            '<td>已验收</td>'+
            '<td>已拒收</td>'+
            '<td>验收人</td>'+
            '<td>验收时间</td>'+
            '<td>验收方式</td>'+
            '</tr>'+
            '<tbody>'+
            yhtmld+
            '</tbody>'+
            '</table>'+
            '</div>'+
            '</li>';
    }
    var supplyJson = getDataWithDetail({orderdcd:val.orderdcd},'select_search_supply');

    if(supplyJson.length){
        supplyhtml+='<li class="layui-timeline-item">'+
            '<i class="layui-icon layui-timeline-axis"><i class="iconfont icon-yunshu"></i></i>'+
            '<div class="layui-timeline-content layui-text">'+
            '<h3 class="layui-timeline-title">'+(supplyJson[0].supplydate?supplyJson[0].supplydate:'')+' 配送装箱</h3>' +
            '<p>' +
            '<span>明细号：<span class="text-blue">'+val.orderdcd+'</span></span>' +
            '<span style="margin-left: 10px;">已发货：<span class="text-green">'+supplyJson[0].supplyqtya+'</span></span>' +
            '<span style="margin-left: 10px;">待发货：<span class="text-yellow">'+supplyJson[0].supplyqtyd+'</span></span>' +
            '</p>' +
            '<table class="layui-table">' +
            '<tr>' +
            '<td>状态</td>' +
            '<td>配送单号</td>' +
            '<td>配送数量</td>' +
            '<td>配送单价</td>' +
            '<td>批号</td>' +
            '<td>生产日期</td>' +
            '<td>效期</td>' +
            '<td>配送日期</td>' +
            '<td>箱码</td>' +
            '</tr>';
        $.each(supplyJson,function (index,val) {
            supplyhtml+='<tr>' +
                '<td>'+(val.skusts==3?'<span class="layui-badge layui-bg-yellow">待发货</span>':'<span class="layui-badge layui-bg-green">已发货</span>')+'</td>' +
                '<td><p class="focus">'+val.supplyno+'</p></td>' +
                '<td><p class="focus">'+val.supplyqty+'</p></td>' +
                '<td><p class="focus">'+val.supplyprice+'</p></td>' +
                '<td><p class="focus">'+val.lotno+'</p></td>' +
                '<td><p class="focus">'+val.mfdate+'</p></td>' +
                '<td><p class="focus">'+val.expire+'</p></td>' +
                '<td><p class="focus">'+val.supplydate+'</p></td>' +
                '<td><p class="focus">'+(val.boxcode?val.boxcode:'<span class="layui-badge layui-bg-yellow">待装箱</span>')+'</p></td>' +
                '</tr>'
        });
        supplyhtml+='</table></li>'
    }
    html+=yhtml+supplyhtml;
    return html;
}
function loadOne(json) {
    var html='' +
        '<ul class="layui-timeline order-detail">';
    if(json.length){
        var val = json[0];
        html+=loadBranch(json)+
            (val.fbcd||val.skusts==8?'<li class="layui-timeline-item">'+
            '<i class="layui-icon layui-timeline-axis"><i class="iconfont icon-check-circle"></i></i>'+
            '<div class="layui-timeline-content layui-text">'+
            '<h3 class="layui-timeline-title">'+(val.fbcd?val.feedbackdate:val.canceldate)+' 确认反馈</h3>'+
            '<p>'+(val.fbcd?'订单确认 确认数：<span class="text-red">'+val.fbqtyt+'</span>':'订单关闭 关闭原因：'+val.remarks+'')+'</p>'+
            '</div>'+
            '</li>':'')+
            '<li class="layui-timeline-item">'+
            '<i class="layui-icon layui-timeline-axis"><i class="iconfont icon-purchase"></i></i>'+
            '<div class="layui-timeline-content layui-text">'+
            '<h3 class="layui-timeline-title">'+val.orderdate+' 订单生成 </h3>' +
            '<ul>' +
            '<li>OMS订单号：' +
            '<span class="focus">'+val.orderno+'</span> ' +
            '<span style="margin-left: 10px;">SPD单号：</span>' +
            '<span class="focus" style="margin-left: 10px;">'+val.upordercd+'</span> ' +
            '<dspan style="margin-left: 10px;">SPD明细号：</dspan>' +
            '<span class="focus" style="margin-left: 10px;">'+val.uporderdcd+'</span> ' +
            '</li>' +
            '<li>' +
            '<span>总采购数：<span class="text-red">'+val.purchqtyt+'</span></span>' +
            '<span style="margin-left: 10px;">采购单价：<span class="text-red">'+val.sellprice+'</span></span>' +
            '<span style="margin-left: 10px;">总采购金额：<span class="text-red">'+parseFloat(val.purchqtyt*val.sellprice).toFixed(2)+'</span></span>' +
            '</li>' +
            '<li>供应商：<a style="cursor: pointer" onclick="lookCompy('+val.scompycd+')">'+val.scompyname+'</a></li>' +
            '<li>采购方：<a style="cursor: pointer" onclick="lookCompy('+val.ccompycd+')">'+val.ccompyname+'-'+val.cdeptname+(val.cusername?'-'+val.cusername:'')+'</a></li>' +
            '<li>商品：<a style="cursor: pointer" onclick="lookGoods('+val.goodscd+')">'+val.goodsname+'</a></li>' +
            '</ul>'+
            '</div>'+
            '</li>';
    }
    html+='</ul>';
    return html;
}

function loadMore(json) {
    var html='';
    $.each(json,function (index,val) {
        html+='<div class="layui-collapse layui-card " lay-filter="test">' +
            '<div class="layui-colla-item">' +
            '<h2 class="layui-colla-title">订单编号:<span class="highlight">'+val.orderno+'</span> 明细号:<span class="highlight">'+val.orderdcd+'</span> ' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-green">总采购<span>('+val.purchqtyt+')</span></span>' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-yellow">待确认<span>('+val.dqr+')</span></span>' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-blue">待配货<span>('+val.dph+')</span></span>' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-maroon">待发货<span>('+val.dfh+')</span></span>' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-orange">待收货<span>('+val.dsh+')</span></span>' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-green">已验收<span>('+val.yys+')</span></span>' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-red">已拒收<span>('+val.yjs+')</span></span>' +
            '<span style="margin-left: 10px;" class="layui-badge layui-bg-gray">已关闭<span>('+val.ygb+')</span></span>' +
            '</h2>' +
            '<div class="layui-colla-content" data-json="'+ JSON.stringify(val).replace(/\"/g, "'")+'"></div>' +
            '</div>' +
            '</div>'
    });
    return html;
}

function getDataWithDetail(data, namingId, callback) {
	
	var json = window.parent.commonselect({
			name: namingId,
			sendData: { "param": JSON.stringify(data) }
		});
	if (callback) callback(json);
    return json;
}
function loadOrderDetail() {
	var json = getDataWithDetail(searchJson,'select_search_order');
	var html='';
        if(!json.length){
            html+=loadEmpty(json);
        }
        if(json.length==1){
            html+='<div class="layui-collapse layui-card ">' +
                '<div class="layui-colla-item">' +
                '<h2 class="layui-colla-title">订单编号/明细号：<span class="highlight">'+searchJson.orderdcd+'</span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-green">总采购<span>('+json[0].purchqtyt+')</span></span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-yellow">待确认<span>('+json[0].dqr+')</span></span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-blue">待配货<span>('+json[0].dph+')</span></span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-maroon">待发货<span>('+json[0].dfh+')</span></span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-orange">待收货<span>('+json[0].dsh+')</span></span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-green">已验收<span>('+json[0].yys+')</span></span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-red">已拒收<span>('+json[0].yjs+')</span></span>' +
                '<span style="margin-left: 10px;" class="layui-badge layui-bg-gray">已关闭<span>('+json[0].ygb+')</span></span>' +
                '</h2>' +
                '<div class="layui-colla-content layui-show order-detail-wrap" >';
            html+=loadOne(json);
            html+='</div></div></div>';

        }
        if(json.length>1){
            html+=loadMore(json);
        }
        $("#Container").html(html);
        initForm();
		layui.element.init();
}
<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getPatient" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******" state="stateless">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link0" name="link0" lineType="reference" isDefault="true" type="action" actionName="action0" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess1</targetNode>
      <process:validateRules errorPage="" onError="default"/>
      <process:inputParameters>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="userId" primitiveType="String"/>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="patienttype" primitiveType="String"/>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="patientid" primitiveType="String"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="60" y="134"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="登录界面" displayName="结束" type="end" contextPath="" method="forward" uri="/coframe/auth/login/login.jsp" variableUri="false">
    <targetConnections>link4</targetConnections>
    <location x="347" y="69"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="170"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="337" y="105"/>
    <figSize height="17" width="49"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end1" name="主页" displayName="结束" type="end" contextPath="" method="forward" uri="/coframe/auth/login/redirect.jsp">
    <targetConnections>link3</targetConnections>
    <location x="346" y="195"/>
    <size height="28" width="28"/>
    <nodeLabel>end1label</nodeLabel>
    <process:inputVariables/>
    <process:returns>
      <process:return id="0" name="patientId" type="query">patientid</process:return>
      <process:return id="1" name="patienttype" type="query">patienttype</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end1label" name="label" nodeType="label">
    <location x="348" y="231"/>
    <figSize height="17" width="25"/>
    <node>end1</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="验证登陆" displayName="loginById" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal"/>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="link3" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>end1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand>result</process:leftOperand>
          <process:rightOperand>1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="195" y="115"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="0" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginManager.loginByUserId</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="retCode" type="query" value="Int" valueType="Primitive">result</process:outputVariable>
        <process:outputVariable id="1" name="msg" type="query" value="String" valueType="Primitive"/>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="184" y="150"/>
    <figSize height="17" width="49"/>
    <node>subprocess1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="cuizhichao" createTime="2014-07-23 13:47:13" date="2014-07-23Z" description="" name="login" version="*******"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" name="result" primitiveType="Int"/>
  </process:variables>
  <process:inputParameters/>
</process:tPageFlow>

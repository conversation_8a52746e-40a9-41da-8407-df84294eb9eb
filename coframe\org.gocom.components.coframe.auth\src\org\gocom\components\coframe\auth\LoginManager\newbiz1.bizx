<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="newbiz1.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="密码解密" title="ouwen&#x9;21-10-11 上午10:52">
    <location x="45" y="315"/>
    <size height="100" width="163"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link3</targetConnections>
    <location x="400" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="String" name="pwd" type="query" valueType="Primitive">pwd</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="402" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="spring调用" displayName="decodePassword" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="239" y="165"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.rights.user.CapUserService.decodePassword</process:partner>
      <process:instance instanceName="CapUserBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="password" type="query" value="java.lang.String" valueType="Java" pattern="reference">pwd</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">pwd</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="222" y="201"/>
    <figSize height="17" width="62"/>
    <node>invokeSpring0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2019-07-24 20:03:39" date="2019-07-24Z" description="" name="newbiz1" version="*******"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="pwd" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="pwd" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

<?xml version="1.0" encoding="UTF-8"?>
<!-- author:ouwen -->
<sqlMap>
    <resultMap class="commonj.sdo.DataObject" id="tkxlresultMap">
        <result column="TOKEN" javaType="string" property="token"/>
        <result column="USERNAME" javaType="string" property="username"/>
    </resultMap>
    <select id="tkxlssologin" parameterClass="java.util.HashMap" resultMap="tkxlresultMap">
    <![CDATA[
 		SELECT TOKEN,USERNAME FROM (SELECT * FROM OA_SPD_USER ORDER BY ADDDATE DESC) WHERE USERNAME = '$username$' and ROWNUM=1
	]]></select>
	
    <resultMap class="commonj.sdo.DataObject" id="tktjresultMap">
        <result column="USERCODE" javaType="string" property="usercode"/>
    </resultMap>
    <select id="tktjssologin" parameterClass="java.util.HashMap" resultMap="tktjresultMap">
    <![CDATA[
 		SELECT USERCODE FROM THIS_LSUSER WHERE PTCODE = '$ptcode$' and ROWNUM=1
	]]></select>
</sqlMap>
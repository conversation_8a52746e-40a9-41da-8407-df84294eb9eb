<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="WEININGSSOLogin" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******" state="stateless">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="卫宁健康通用接口" title="admin&#x9;24-3-28 上午11:32">
    <location x="22" y="345"/>
    <size height="100" width="134"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link2" name="link2" lineType="reference" isDefault="true" type="action" actionName="action0" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:validateRules errorPage="" onError="default"/>
      <process:inputParameters>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="cipher" primitiveType="String"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="75" y="160"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="78" y="196"/>
    <figSize height="14" width="22"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="验证用户" displayName="loginByUserId" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link1" name="link1" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">result</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="311" y="160"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginManager.loginByUserId</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="retCode" type="query" value="Int" valueType="Primitive">result</process:outputVariable>
        <process:outputVariable id="1" name="msg" type="query" value="String" valueType="Primitive"/>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="303" y="195"/>
    <figSize height="14" width="43"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end1" name="登录界面" displayName="结束" collapsed="false" type="end" contextPath="" method="forward" uri="/coframe/auth/login/SSORedirect.jsp" variableUri="false">
    <targetConnections>link0</targetConnections>
    <targetConnections>link5</targetConnections>
    <location x="375" y="80"/>
    <size height="28" width="28"/>
    <nodeLabel>end1label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end1label" name="label" nodeType="label">
    <location x="368" y="116"/>
    <figSize height="14" width="43"/>
    <node>end1</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end2" name="主页" displayName="结束" collapsed="false" type="end" contextPath="" method="forward" uri="/coframe/auth/login/redirect.jsp">
    <targetConnections>link1</targetConnections>
    <location x="402" y="228"/>
    <size height="28" width="28"/>
    <nodeLabel>end2label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end2label" name="label" nodeType="label">
    <location x="405" y="264"/>
    <figSize height="14" width="22"/>
    <node>end2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="获取登录用户名" displayName="WeiNingSSOLogin" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>subprocess0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="link4" displayName="连接线" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NULLOREMPTY">
          <process:leftOperand type="query">userId</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="195" y="160"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="static" synchronization="true">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.SSOLoginService.WeiNingSSOLogin</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="ticket" type="query" value="java.lang.String" valueType="Java" pattern="reference">ticket</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">userId</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="169" y="196"/>
    <figSize height="14" width="75"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="用户名为空" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="257" y="80"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-8</process:from>
      <process:to type="query">retCode</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="244" y="116"/>
    <figSize height="14" width="54"/>
    <node>assign0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="ouwen" createTime="2021-09-11 16:06:57" date="2021-09-11Z" description="" name="ssoLogin" version="*******"/>
  <process:variables/>
</process:tPageFlow>

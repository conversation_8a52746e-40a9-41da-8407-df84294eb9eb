<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="removeDictType.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" collapsed="false" nodeType="note" type="note" content="删除业务字典类型&#xD;&#xA;&#xD;&#xA;说明：&#xD;&#xA;1.删除选择的字典类型和其子类型&#xD;&#xA;2.删除所有字典类型和子类型级联的业务字典项" title="陈鹏&#x9;13-11-29 下午3:33">
    <location x="30" y="394"/>
    <size height="139" width="247"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" collapsed="false" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link18" name="link18" displayName="link0" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>transactionbegin0</targetNode>
    </sourceConnections>
    <location x="155" y="146"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <location x="157" y="182"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" collapsed="false" nodeType="common" type="end">
    <targetConnections>link20</targetConnections>
    <targetConnections>link17</targetConnections>
    <location x="993" y="372"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.dict.dict.EosDictType[]" name="data" type="query" valueType="DataObject">data</process:return>
      <process:return id="1" language="String" name="status" type="query" valueType="Primitive">status</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <location x="995" y="408"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionBegin" id="transactionbegin0" name="事务开始" displayName="事务开始" collapsed="false" type="transactionbegin">
    <sourceConnections xsi:type="process:tLink" id="link22" name="link22" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactionbegin0</sourceNode>
      <targetNode>loopstart0</targetNode>
    </sourceConnections>
    <targetConnections>link18</targetConnections>
    <location x="256" y="146"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionbegin0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionbegin0label" name="label" nodeType="label">
    <location x="246" y="182"/>
    <figSize height="17" width="49"/>
    <node>transactionbegin0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionCommit" id="transactioncommit0" name="事务提交" displayName="事务提交" collapsed="false" type="transactioncommit">
    <sourceConnections xsi:type="process:tLink" id="link19" name="link19" displayName="link3" isDefault="true" type="transition">
      <sourceNode>transactioncommit0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link15</targetConnections>
    <location x="808" y="320"/>
    <size height="28" width="28"/>
    <nodeLabel>transactioncommit0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactioncommit0label" name="label" nodeType="label">
    <location x="798" y="356"/>
    <figSize height="17" width="49"/>
    <node>transactioncommit0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionRollback" id="transactionrollback0" name="事务回滚" displayName="事务回滚" collapsed="false" type="transactionrollback">
    <sourceConnections xsi:type="process:tLink" id="link21" name="link21" displayName="link9" isDefault="true" type="transition">
      <sourceNode>transactionrollback0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <targetConnections>link24</targetConnections>
    <targetConnections>link16</targetConnections>
    <location x="711" y="419"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionrollback0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionrollback0label" name="label" nodeType="label">
    <location x="701" y="455"/>
    <figSize height="17" width="49"/>
    <node>transactionrollback0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="link10" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link19</targetConnections>
    <location x="898" y="320"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">success</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="900" y="356"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link20" name="link20" displayName="link11" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link21</targetConnections>
    <location x="899" y="419"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">fail</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="901" y="455"/>
    <figSize height="17" width="25"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="model:GroupNode" id="group0" name="group" grouped="true" gstart="loopstart0" gend="loopend0">
    <location x="356" y="93"/>
    <size height="151" width="382"/>
    <chidren>assign4</chidren>
    <chidren>invokePojo1</chidren>
    <chidren>loopstart0</chidren>
    <chidren>loopend0</chidren>
    <chidren>subprocess0</chidren>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="赋值" displayName="赋值" collapsed="false" grouped="true" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link14" name="link14" displayName="link12" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <targetConnections>link12</targetConnections>
    <location x="440" y="146"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.dict.dict.EosDictType</process:from>
      <process:to type="query">criteria/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">parent/seqno</process:from>
      <process:to type="query">criteria/_expr[1]/seqno</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">like</process:from>
      <process:to type="query">criteria/_expr[1]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">end</process:from>
      <process:to type="query">criteria/_expr[1]/_likeRule</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="442" y="182"/>
    <figSize height="17" width="25"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="查询子类型" displayName="查询叶子节点" collapsed="false" grouped="true" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="link14" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>subprocess0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="530" y="146"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">children</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="511" y="182"/>
    <figSize height="17" width="61"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopstart0label" name="label" nodeType="label">
    <location x="356" y="180"/>
    <figSize height="17" width="25"/>
    <node>loopstart0</node>
  </nodes>
  <nodes xsi:type="process:tLoopStart" description="" id="loopstart0" name="循环" displayName="循环" grouped="true" type="loopstart" matchedName="loopend0" loopType="iterate">
    <sourceConnections xsi:type="process:tLink" id="link12" name="link12" displayName="link13" isDefault="true" type="transition">
      <sourceNode>loopstart0</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link22</targetConnections>
    <location x="356" y="148"/>
    <size height="24" width="24"/>
    <nodeLabel>loopstart0label</nodeLabel>
    <process:condition/>
    <process:iterate iterable="data" iterableElement="parent"/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopend0label" name="label" nodeType="label">
    <location x="701" y="180"/>
    <figSize height="17" width="49"/>
    <node>loopend0</node>
  </nodes>
  <nodes xsi:type="process:tLoopEnd" id="loopend0" name="循环结束" displayName="循环结束" grouped="true" type="loopend" matchedName="loopstart0">
    <sourceConnections xsi:type="process:tLink" id="link23" name="link23" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopend0</sourceNode>
      <targetNode>subprocess1</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="713" y="148"/>
    <size height="24" width="24"/>
    <nodeLabel>loopend0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="删除子类型" displayName="removeDictTypeCascade" grouped="true" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link13" name="link13" displayName="link15" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>loopend0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link24" name="link24" displayName="连接线" lineType="note" type="exception">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link11</targetConnections>
    <location x="615" y="146"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" transactionType="join" varArgs="false">
      <process:partner type="literal">this.removeDictTypeCascade</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="data" type="query" value="org.gocom.components.coframe.dict.dict.EosDictType[]" valueType="DataObject" pattern="reference">children</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="598" y="181"/>
    <figSize height="17" width="61"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="删除字典类型" displayName="removeDictTypeCascade" collapsed="false" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="link7" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>transactioncommit0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="link8" lineType="note" type="exception">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link23</targetConnections>
    <location x="711" y="320"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="0" transactionType="join" varArgs="false">
      <process:partner type="literal">this.removeDictTypeCascade</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="data" type="query" value="org.gocom.components.coframe.dict.dict.EosDictType[]" valueType="DataObject" pattern="reference">data</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="688" y="355"/>
    <figSize height="17" width="73"/>
    <node>subprocess1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-15 11:24:41" date="2013-12-15Z" description="" name="删除业务字典类型" version="6.3"/>
  <process:variables>
    <process:variable description="业务字典类型" historyStateLocation="client" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="children"/>
    <process:variable description="业务字典类型" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="parent"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="业务字典类型" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="data"/>
  </process:inputs>
  <process:outputs>
    <process:output description="业务字典类型" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="data"/>
    <process:output description="" isArray="false" name="status" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

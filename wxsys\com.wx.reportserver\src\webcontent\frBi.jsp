<%@ page language="java" contentType="text/html; charset=UTF-8"
pageEncoding="UTF-8" session="false" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <!-- 
  - Author(s): ouwen
  - Date: 2022-05-17 17:58:13
  - Description:
-->
    <head>
        <title>ReportServer</title>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
        <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
        <script src="<%= request.getContextPath()%>/coframe/auth/skin1/index/js/jquery.min.js"></script>
    </head>
    <body>
        <script type="text/javascript">
            var host = window.location.host.split(":")[0] + ":37799";
            
			var url = "http://"+ host +"/webroot/decision";
			 
			var viewUrl =
                url +
                "/v5/design/report/" +
                GetQueryValue("viewName") +
                "/view";

            loginFR();

            function loginFR() {
                jQuery.ajax({
                    url:
                        url +
                        "/login/cross/domain?fine_username=wx" +
                        "&fine_password=" +
                        encodeURIComponent("wx123!@#") +
                        "&validity=-1",
                    type: "GET",
                    dataType: "jsonp",
                    timeout: 10000,
                    success: function (data) {
                      window.location.href = viewUrl;
                    },
                    error: function () {
                        alert("调用Bi服务异常，请检查配置是否正确!");
                    },
                });
            }

			function GetQueryValue(queryName) {
                var query = decodeURI(window.location.search.substring(1));
                var vars = query.split("&");
                for (var i = 0; i < vars.length; i++) {
                    var pair = vars[i].split("=");
                    if (pair[0] == queryName) {
                        return pair[1];
                    }
                }
                return null;
            }
        </script>
    </body>
</html>

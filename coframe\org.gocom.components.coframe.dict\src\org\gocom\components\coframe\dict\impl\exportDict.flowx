<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="exportDict.flowx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3" state="stateless">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link0" name="link0" lineType="reference" isDefault="true" type="action" actionName="exportDict" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
      <process:validateRules errorPage="" onError="default"/>
      <process:inputParameters>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="dicttypeid" primitiveType="String"/>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="dicttypename" primitiveType="String"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" nodeType="common" type="end" contextPath="" method="forward" uri="/coframe/dict/export_dict.jsp">
    <targetConnections>link1</targetConnections>
    <location x="355" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="357" y="96"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="导出业务字典" displayName="exportDict" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="208" y="61"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.dict.DictManager.exportDict</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="dicttypeid" type="query" value="String" valueType="Primitive" pattern="reference">dicttypeid</process:inputVariable>
        <process:inputVariable id="1" name="dicttypename" type="query" value="String" valueType="Primitive" pattern="reference">dicttypename</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="data" type="query" value="Byte[]" valueType="Primitive">data</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="185" y="96"/>
    <figSize height="17" width="73"/>
    <node>subprocess0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-17 13:53:44" date="2013-12-17Z" description="" name="导出业务字典" version="6.3"/>
  <process:variables/>
</process:tPageFlow>

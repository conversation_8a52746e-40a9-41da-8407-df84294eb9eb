<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="storePartyAuth" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node1" name="注释1" displayName="注释" collapsed="false" nodeType="note" type="note" content="给角色批量实体规则授权" title="lijuntao 13-4-28 上午10:02">
    <location x="91" y="180"/>
    <size height="100" width="171"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="91" y="90"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link1</targetConnections>
    <location x="391" y="90"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Boolean" name="saveResult" type="query" valueType="Primitive">saveResult</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="93" y="126"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="393" y="126"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="实体批量授权" displayName="authBatch" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="237" y="90"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.entityauth.EntityAuthService.authBatch</process:partner>
      <process:instance instanceName="entityAuthServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="party" type="query" value="com.primeton.cap.party.Party" valueType="Java" pattern="reference">party</process:inputVariable>
      <process:inputVariable id="1" name="authRuleIds" type="query" value="java.lang.String[]" valueType="Java" pattern="reference">partyWithAuth</process:inputVariable>
      <process:inputVariable id="2" name="delRuleIds" type="query" value="java.lang.String[]" valueType="Java" pattern="reference">partyNoAuth</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="boolean" valueType="Java">saveResult</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="215" y="126"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="liuzn" createTime="2013-03-14 19:25:06" date="2013-03-14Z" description="保存Party授权信息" name="保存Party授权信息" version="6.3"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" name="delSuccess" primitiveType="Boolean"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="saveSuccess" primitiveType="Boolean"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input anyType="com.primeton.cap.party.Party" description="" isArray="false" name="party"/>
    <process:input description="" isArray="true" name="partyWithAuth" primitiveType="String"/>
    <process:input description="" isArray="true" name="partyNoAuth" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="saveResult" primitiveType="Boolean"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

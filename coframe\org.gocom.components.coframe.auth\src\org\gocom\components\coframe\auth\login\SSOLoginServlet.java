package org.gocom.components.coframe.auth.login;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.eos.engine.component.ILogicComponent;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.logging.Logger;
import com.primeton.ext.engine.component.LogicComponentFactory;

/**
 * 东华单点登录认证
 * 
 * 
 * <AUTHOR>
 * 
 */
public class SSOLoginServlet extends HttpServlet {

	private static final long serialVersionUID = -2187695682372304972L;

	private static Logger logger = TraceLoggerFactory
			.getLogger(SSOLoginServlet.class);

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		this.doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		try {
			logger.info("东华调用SPD接口-单点登录");
			// 设置响应的字符集和类型
			resp.setContentType("application/json;charset=utf-8");
			resp.setCharacterEncoding("utf-8");

			// 判断是否有授权码
			String code = req.getParameter("code");
			// 判断是否有logoutRequest传参，有该参数代表需要退出并清除session
			String logoutRequest = req.getParameter("logoutRequest");
			if (logoutRequest != null && logoutRequest.length() > 0) {
				resp.sendRedirect(req.getContextPath()
						+ "/coframe/auth/login/logout.jsp");
			}

			if (req.getSession().getAttribute("userObject") == null) {
				StringBuffer bufferURL = new StringBuffer();
				bufferURL
						.append("http://172.16.60.65:8099/DTH_SSO/oauth2.0/authorize")
						.append("?client_id=")
						.append("7kw5FlnRWozD")
						.append("&redirect_uri=")
						.append("http://172.16.61.53:8085/vxspd/SSOLogin.do")
						.append("&response_type=").append(code)
						.append("&state=123");

				if (StringUtils.isBlank(code)) {
					// 到统一认证请求获取授权码
					resp.sendRedirect(bufferURL.toString());
					return;
				} else {
					// 用java JDK自带的URL去请求
					StringBuffer bufferURL2 = new StringBuffer();
					BufferedReader reader = null;
					StringBuffer sbf = new StringBuffer();
					JSONObject json = null;
					bufferURL2
							.append("http://172.16.60.65:8099/DTH_SSO/oauth2.0/accessToken?")
							.append("client_id=")
							.append("7kw5FlnRWozD")
							.append("&client_secret=")
							.append("E2serjEdhgEs")
							.append("&grant_type=")
							.append("authorization_code")
							.append("&redirect_uri=")
							.append("http://172.16.61.53:8085/vxspd/SSOLogin.do")
							.append("&code=").append(code);
					URL url = new URL(bufferURL2.toString());
					HttpURLConnection connection = (HttpURLConnection) url
							.openConnection();
					connection.setRequestMethod("POST");
					connection.setRequestProperty("Accept", "*/*");
					connection.connect();
					InputStream is = connection.getInputStream();
					reader = new BufferedReader(new InputStreamReader(is,
							"UTF-8"));
					String strRead = null;
					while ((strRead = reader.readLine()) != null) {
						sbf.append(strRead);
					}
					reader.close();
					json = JSONObject.parseObject(sbf.toString());
					logger.info("Access Token: " + json.get("access_token"));
					logger.info("Expires In: " + json.get("expires"));
					// 通过access_token获取userInfo
					StringBuffer bufferURL3 = new StringBuffer();
					bufferURL3
							.append("http://172.16.60.65:8099/DTH_SSO/oauth2.0/profile")
							.append("?access_token=")
							.append(json.get("access_token"));

					URL url2 = new URL(bufferURL3.toString());
					HttpURLConnection connection2 = (HttpURLConnection) url2
							.openConnection();
					connection2.setRequestMethod("GET");
					connection2.setRequestProperty("Accept", "*/*");
					connection2.connect();
					InputStream is2 = connection.getInputStream();
					reader = new BufferedReader(new InputStreamReader(is2,
							"UTF-8"));
					String strRead2 = null;
					while ((strRead2 = reader.readLine()) != null) {
						sbf.append(strRead);
					}
					reader.close();
					json = JSONObject.parseObject(sbf.toString());
					// 逻辑构件名称
					String componentName = "org.gocom.components.coframe.auth.LoginManager.componentType";
					// 逻辑流名称
					String operationName = "loginByUserId";
					ILogicComponent logicComponent = LogicComponentFactory
							.create(componentName);

					int size = 1;
					// 逻辑流的输入参数
					Object[] params = new Object[size];
					params[0] = json.get("userCode");
					Object[] result = logicComponent.invoke(operationName,
							params);
					// 判断登录接口返回值
					if ("1".equals(result[0])) {
						req.getRequestDispatcher(
								req.getContextPath()
										+ "/coframe/auth/skin1/index.jsp")
								.forward(req, resp);
						return;
					}
				}
			}
		} catch (Throwable e) {
			e.printStackTrace();
		}
		resp.sendRedirect(req.getContextPath() + "/coframe/auth/login/login.jsp");
		return;
	}
}

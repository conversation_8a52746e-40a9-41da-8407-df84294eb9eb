<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="queryMenuTreeNode.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="link4" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NULLOREMPTY">
          <process:leftOperand type="query">nodeId</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link9" name="link9" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeId</process:leftOperand>
          <process:rightOperand type="literal">root</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="30" y="120"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link7</targetConnections>
    <targetConnections>link3</targetConnections>
    <targetConnections>link11</targetConnections>
    <location x="690" y="120"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="java.util.List" name="data" type="query" valueType="Java">data</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="32" y="156"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="692" y="156"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="树初始化" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="152" y="45"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">null</process:from>
      <process:to type="query">criteria/_expr[1]/appMenu.menuid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">null</process:from>
      <process:to type="query">criteria/_expr[1]/_op</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="142" y="81"/>
    <figSize height="12" width="49"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="查询菜单" displayName="queryAppMenus" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="308" y="45"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppMenuService.queryAppMenus</process:partner>
      <process:instance instanceName="AppMenuBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteriaType" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppMenu[]" valueType="Java">appmenus</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="298" y="81"/>
    <figSize height="12" width="49"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="构造根和一级节点" displayName="getMenuRoot" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="480" y="45"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppMenuService.getMenuRoot</process:partner>
      <process:instance instanceName="AppMenuBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="menus" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appmenus</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.List&lt;java.util.Map>" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="446" y="81"/>
    <figSize height="12" width="97"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="展开子节点" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>invokeSpring2</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="152" y="120"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">nodeId</process:from>
      <process:to type="query">criteria/_expr[1]/appMenu.menuid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="136" y="156"/>
    <figSize height="12" width="61"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="查询菜单1" displayName="queryAppMenus" collapsed="false" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link11" name="link11" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NULLOREMPTY">
          <process:leftOperand type="query">appmenus</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>invokeSpring3</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="307" y="120"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppMenuService.queryAppMenus</process:partner>
      <process:instance instanceName="AppMenuBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteriaType" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppMenu[]" valueType="Java">appmenus</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="294" y="156"/>
    <figSize height="12" width="55"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="构造子菜单" displayName="getMenuNode" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <targetConnections>link2</targetConnections>
    <location x="480" y="216"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppMenuService.getMenuNode</process:partner>
      <process:instance instanceName="AppMenuBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="menus" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appmenus</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.List&lt;java.util.Map>" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="464" y="252"/>
    <figSize height="12" width="61"/>
    <node>invokeSpring3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="展开根" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link10" name="link10" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokeSpring5</targetNode>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="152" y="216"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">null</process:from>
      <process:to type="query">criteria/_expr[1]/appMenu.menuid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">null</process:from>
      <process:to type="query">criteria/_expr[1]/_op</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="148" y="252"/>
    <figSize height="12" width="37"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring5" name="查询菜单2" displayName="queryAppMenus" collapsed="false" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="link6" isDefault="true" type="transition">
      <sourceNode>invokeSpring5</sourceNode>
      <targetNode>invokeSpring3</targetNode>
    </sourceConnections>
    <targetConnections>link10</targetConnections>
    <location x="309" y="216"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring5label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppMenuService.queryAppMenus</process:partner>
      <process:instance instanceName="AppMenuBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteriaType" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppMenu[]" valueType="Java">appmenus</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring5label" name="label" nodeType="label">
    <location x="296" y="252"/>
    <figSize height="12" width="55"/>
    <node>invokeSpring5</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-07 19:05:31" date="2013-03-07Z" description="" name="queryMenuTreeNode" version="6.3"/>
  <process:variables>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppMenu" description="" historyStateLocation="client" isArray="true" name="appmenus"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="nodeId" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="java.util.List" description="" isArray="false" name="data"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

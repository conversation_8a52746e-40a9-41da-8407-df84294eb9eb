package org.gocom.components.coframe.auth.login;

import com.eos.access.http.OnlineUserManager;
import com.eos.das.entity.criteria.CriteriaType;
import com.eos.data.datacontext.DataContextManager;
import com.eos.data.datacontext.ISessionMap;
import com.eos.data.datacontext.IUserObject;
import com.eos.data.datacontext.UserObject;
import com.eos.foundation.database.DatabaseUtil;
import com.primeton.cap.auth.MenuTree;
import com.primeton.ext.common.muo.MUODataContextHelper;
import commonj.sdo.DataObject;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.http.HttpSession;
import org.gocom.components.coframe.auth.DefaultAuthManagerService;
import org.gocom.components.coframe.init.UserObjectInit;

public class LoginService {
	public void login(UserObject userObject) {
		OnlineUserManager.login((IUserObject) userObject);
	}

	public void initEmpObject(UserObject userObject, DataObject obj) {
		userObject.put("EmpObject", obj);
	}

	public UserObject initUserObject(String userId) {
		return UserObjectInit.INSTANCE.init(userId);
	}

	public static void sessionPut(String key, String value) {
		ISessionMap sessionMap = DataContextManager.current().getSessionCtx();
		if (sessionMap == null)
			sessionMap = MUODataContextHelper.getMapContextFactory().getSessionMap();
		if (sessionMap != null) {
			Object rootObject = sessionMap.getRootObject();
			if (rootObject != null && rootObject instanceof HttpSession) {
				HttpSession session = (HttpSession) rootObject;
				session.setAttribute(key, value);
				session.getServletContext().setAttribute(key, value);
			}
			return;
		}
	}

	public static void setApplication(String key, String value) {
		ISessionMap sessionMap = DataContextManager.current().getSessionCtx();
		if (sessionMap == null)
			sessionMap = MUODataContextHelper.getMapContextFactory().getSessionMap();
		if (sessionMap != null) {
			Object rootObject = sessionMap.getRootObject();
			if (rootObject != null && rootObject instanceof HttpSession) {
				HttpSession session = (HttpSession) rootObject;
				session.getServletContext().setAttribute(key, value);
			}
			return;
		}
	}

	public static void removeAttribute(String key) {
		ISessionMap sessionMap = DataContextManager.current().getSessionCtx();
		if (sessionMap == null)
			sessionMap = MUODataContextHelper.getMapContextFactory().getSessionMap();
		if (sessionMap != null) {
			Object rootObject = sessionMap.getRootObject();
			if (rootObject != null && rootObject instanceof HttpSession) {
				HttpSession session = (HttpSession) rootObject;
				session.removeAttribute(key);
			}
		}
	}

	public static String getSessionAttribute(String key) {
		String value = null;
		ISessionMap sessionMap = DataContextManager.current().getSessionCtx();
		if (sessionMap == null)
			sessionMap = MUODataContextHelper.getMapContextFactory().getSessionMap();
		if (sessionMap != null) {
			Object rootObject = sessionMap.getRootObject();
			if (rootObject != null && rootObject instanceof HttpSession) {
				HttpSession session = (HttpSession) rootObject;
				value = (session.getAttribute(key) == null) ? value : session.getAttribute(key).toString();
			}
		}
		return value;
	}

	public static void sessionPut(HashMap<String, String> keyvalue) {
		ISessionMap sessionMap = DataContextManager.current().getSessionCtx();
		if (sessionMap == null)
			sessionMap = MUODataContextHelper.getMapContextFactory().getSessionMap();
		if (sessionMap != null) {
			Object rootObject = sessionMap.getRootObject();
			if (rootObject != null && rootObject instanceof HttpSession) {
				Iterator<Entry<String, String>> iter = keyvalue.entrySet().iterator();
				HttpSession session = (HttpSession) rootObject;
				while (iter.hasNext()) {
					Map.Entry entry = iter.next();
					session.setAttribute(String.valueOf(entry.getKey()), String.valueOf(entry.getValue()));
				}
			}
			return;
		}
	}

	public List<MenuTree.MenuTreeNode> getUserMenuTree() throws FileNotFoundException {
		List<MenuTree.MenuTreeNode> l = null;
		try {
			DefaultAuthManagerService defaultAuthManagerService = new DefaultAuthManagerService();
			l = defaultAuthManagerService.getUserMenuTree().getMenuTreeRootNodeList();
		} catch (Exception e) {
			e.printStackTrace();
			PrintWriter write = new PrintWriter(new File("D:\\menulog.txt"));
			write.println(e.getMessage());
			write.print(e.toString());
			write.close();
		}
		return l;
	}

	public List<MenuTree.MenuTreeNode> getUserMenuTreeByAppCode(String appCode) {
		DefaultAuthManagerService defaultAuthManagerService = new DefaultAuthManagerService();
		return defaultAuthManagerService.getUserMenuTreeByAppCode(appCode).getMenuTreeRootNodeList();
	}

	public UserObject logout(String uniqueId) {
		return (UserObject) OnlineUserManager.logoutByUniqueId(uniqueId);
	}

	public DataObject getEmpCompany(DataObject obj) {
		String orgseq = obj.getString("orgseq");
		if (orgseq != null) {
			String[] orgids = orgseq.split("\\.");
			if (orgids.length > 1) {
				String orgid = orgids[1];
				CriteriaType criteriaType = (CriteriaType) CriteriaType.FACTORY.create();
				criteriaType.set_entity("org.gocom.components.coframe.org.dataset.OrgOrganization");
				criteriaType.set("_expr[1]/orgid", orgid);
				criteriaType.set("_expr[1]/_op", "=");
				DataObject[] orgArray = DatabaseUtil.queryEntitiesByCriteriaEntity("default",
						(DataObject) criteriaType);
				if (orgArray != null && orgArray.length == 1)
					obj.set("Company", orgArray[0]);
			}
		}
		return obj;
	}

	public static String getAppCode() {
		// 初始化appcode为空字符串
		String appcode = "";
		// 尝试获取userobj对象，注意这里可能返回null
		IUserObject userobj = DataContextManager.current().getMUODataContext().getUserObject();
		if (userobj != null) { // 检查userobj是否为null
			// 获取EmpObject前进行类型转换和null检查
			Object empObjectObj = userobj.getAttributes().get("EmpObject");
			if (empObjectObj instanceof DataObject) {
				DataObject EmpObject = (DataObject) empObjectObj;

				// 获取LoginUserInfo前进行null检查
				Object loginUserInfoObj = EmpObject.get("LoginUserInfo");
				if (loginUserInfoObj instanceof HashMap) {
					HashMap LoginUserInfo = (HashMap) loginUserInfoObj;

					// 最终检查S_DEPTAPPS是否存在且非空
					Object deptAppsObj = LoginUserInfo.get("S_DEPTAPPS");
					appcode = (deptAppsObj != null) ? deptAppsObj.toString() : "";
				}
			}
		}
		return appcode;
	}
}

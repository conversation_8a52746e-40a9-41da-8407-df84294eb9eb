<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="exportDict.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" collapsed="false" nodeType="common" type="end">
    <targetConnections>link3</targetConnections>
    <location x="905" y="107"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Byte[]" name="data" type="query" valueType="Primitive">data</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <location x="907" y="143"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="查询字典类型" displayName="" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link6" name="link6" displayName="link2" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link8" name="link8" displayName="连接线" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>loopstart0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NOTNULLANDEMPTY">
          <process:leftOperand type="query">dictTypeLists</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="320" y="107"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">typeCriteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">dictTypeLists</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="295" y="143"/>
    <figSize height="12" width="73"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="link4" displayName="link10" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NOTNULLANDEMPTY">
          <process:leftOperand type="query">dictType</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="186" y="107"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.dict.dict.DictExport</process:from>
      <process:to type="query">exportCriteria/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference"> </process:from>
      <process:to type="query">ids</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.dict.dict.EosDictType</process:from>
      <process:to type="query">typeCriteria/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">dicttypeid</process:from>
      <process:to type="query">typeCriteria/_expr[1]/dicttypeid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">like</process:from>
      <process:to type="query">typeCriteria/_expr[1]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">all</process:from>
      <process:to type="query">typeCriteria/_expr[1]/_likeRule</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">dicttypename</process:from>
      <process:to type="query">typeCriteria/_expr[2]/dicttypename</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">like</process:from>
      <process:to type="query">typeCriteria/_expr[2]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">all</process:from>
      <process:to type="query">typeCriteria/_expr[2]/_likeRule</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="188" y="143"/>
    <figSize height="12" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="查询导出数据" displayName="查询业务字典导出Excel数据集" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="link4" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <targetConnections>link6</targetConnections>
    <location x="613" y="107"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">exportCriteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">dictList</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="588" y="143"/>
    <figSize height="12" width="73"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值" displayName="赋值" grouped="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="link7" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>loopend0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="427" y="231"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">eosDictType/dicttypeid</process:from>
      <process:to type="query">id</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">ids + id + &quot;,&quot;</process:from>
      <process:to type="query">ids</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="429" y="267"/>
    <figSize height="12" width="25"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tLoopEnd" id="loopend0" name="循环结束" displayName="循环结束" grouped="true" type="loopend" matchedName="loopstart0">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopend0</sourceNode>
      <targetNode>assign2</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="523" y="233"/>
    <size height="24" width="24"/>
    <nodeLabel>loopend0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopend0label" name="label" nodeType="label">
    <location x="511" y="265"/>
    <figSize height="12" width="49"/>
    <node>loopend0</node>
  </nodes>
  <nodes xsi:type="process:tLoopStart" description="" id="loopstart0" name="循环" displayName="循环" grouped="true" type="loopstart" matchedName="loopend0" loopType="iterate">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="link6" isDefault="true" type="transition">
      <sourceNode>loopstart0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="320" y="233"/>
    <size height="24" width="24"/>
    <nodeLabel>loopstart0label</nodeLabel>
    <process:condition/>
    <process:iterate iterable="dictTypeLists" iterableElement="eosDictType"/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopstart0label" name="label" nodeType="label">
    <location x="320" y="265"/>
    <figSize height="12" width="25"/>
    <node>loopstart0</node>
  </nodes>
  <nodes xsi:type="model:GroupNode" id="group0" name="group" grouped="true" gstart="loopstart0" gend="loopend0">
    <location x="320" y="200"/>
    <size height="106" width="230"/>
    <chidren>assign1</chidren>
    <chidren>loopstart0</chidren>
    <chidren>loopend0</chidren>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="link9" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="610" y="230"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="expression" pattern="reference">ids.substring(1, ids.length() - 1)</process:from>
      <process:to type="query">ids</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">dicttypeid</process:from>
      <process:to type="query">exportCriteria/_expr[1]/_property</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">in</process:from>
      <process:to type="query">exportCriteria/_expr[1]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">ids</process:from>
      <process:to type="query">exportCriteria/_expr[1]/_value</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="612" y="266"/>
    <figSize height="12" width="25"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" collapsed="false" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="link0" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="80" y="107"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <location x="82" y="143"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="导出业务字典" displayName="dictExport" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="link1" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="752" y="107"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.dict.impl.DictService.dictExport</process:partner>
      <process:instance instanceName="DictServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="dictdata" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">dictList</process:inputVariable>
      <process:inputVariable id="1" name="exportInfo" type="expression" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">null</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="byte[]" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="730" y="143"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-17 10:16:02" date="2013-12-17Z" description="" name="导出业务字典" version="6.3"/>
  <process:variables>
    <process:variable description="查询导出Excel数据" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="exportCriteria"/>
    <process:variable description="" historyStateLocation="client" isArray="true" modelType="org.gocom.components.coframe.dict.dict.DictExport" name="dictList"/>
    <process:variable description="业务字典类型ID" historyStateLocation="client" isArray="false" name="id" primitiveType="String"/>
    <process:variable description="业务字典类型ID拼装" historyStateLocation="client" isArray="false" name="ids" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="eosDictType"/>
    <process:variable description="" historyStateLocation="client" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="dictTypeLists"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="typeCriteria"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="dicttypeid" primitiveType="String"/>
    <process:input description="" isArray="false" name="dicttypename" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="true" name="data" primitiveType="Byte"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

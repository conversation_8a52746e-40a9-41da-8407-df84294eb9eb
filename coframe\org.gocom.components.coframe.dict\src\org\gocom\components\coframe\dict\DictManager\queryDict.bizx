<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="queryDict.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" collapsed="false" nodeType="note" type="note" content="根据查询条件查询业务字典项&#xD;&#xA;&#xD;&#xA;查询条件：&#xD;&#xA;业务字典类型ID&#xD;&#xA;&#xD;&#xA;说明：默认parentid，sortno升序排列" title="陈鹏&#x9;13-11-29 下午3:33">
    <location x="30" y="375"/>
    <size height="140" width="226"/>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" collapsed="false" nodeType="common" type="end">
    <targetConnections>link12</targetConnections>
    <location x="675" y="285"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.dict.dict.EosDictEntry[]" name="data" type="query" valueType="DataObject">data</process:return>
      <process:return id="1" language="Int" name="total" type="query" valueType="Primitive">total</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <location x="677" y="321"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="查询数目" displayName="查询GRID数目" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="默认排序" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link10" name="手动排序" displayName="连接线" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:complexCondition>
          <process:code>sortField != null &amp;&amp; sortOrder != null</process:code>
        </process:complexCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="422" y="31"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.count</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">total</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="409" y="67"/>
    <figSize height="17" width="49"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="link1" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="301" y="31"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.dict.dict.EosDictEntry</process:from>
      <process:to type="query">criteria/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">dicttypeid</process:from>
      <process:to type="query">criteria/_expr[1]/eosDictType.dicttypeid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="303" y="67"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" collapsed="false" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="查询" displayName="link0" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link13" name="展开节点" displayName="连接线" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NOTNULL">
          <process:leftOperand type="query">dictid</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="90" y="31"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <location x="92" y="67"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="564" y="31"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">parentid</process:from>
      <process:to type="query">criteria/_orderby[1]/_property</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">asc</process:from>
      <process:to type="query">criteria/_orderby[1]/_sort</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">sortno</process:from>
      <process:to type="query">criteria/_orderby[2]/_property</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">asc</process:from>
      <process:to type="query">criteria/_orderby[2]/_sort</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="566" y="67"/>
    <figSize height="17" width="25"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>invokePojo2</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link10</targetConnections>
    <location x="419" y="165"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">sortField</process:from>
      <process:to type="query">criteria/_orderby[1]/_property</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">sortOrder</process:from>
      <process:to type="query">criteria/_orderby[1]/_sort</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="421" y="201"/>
    <figSize height="17" width="25"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="查询数据" displayName="Query with page according to criteria entity" nodeType="common" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <targetConnections>link11</targetConnections>
    <targetConnections>link6</targetConnections>
    <location x="567" y="165"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseExt.queryEntitiesByCriteriaEntityWithPage</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">criteria</process:inputVariable>
      <process:inputVariable id="2" name="pagecond" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">page</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="554" y="201"/>
    <figSize height="17" width="49"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link14" name="link14" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>invokePojo3</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="90" y="285"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">parenttypeid</process:from>
      <process:to type="query">parent/eosDictType/dicttypeid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">dictid</process:from>
      <process:to type="query">parent/dictid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="92" y="321"/>
    <figSize height="17" width="25"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="查询子项" displayName="查询叶子节点" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link17</targetConnections>
    <location x="421" y="285"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="408" y="321"/>
    <figSize height="17" width="49"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo3" name="查询字典项" displayName="Query according to the dataObject" nodeType="common" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo3</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="190" y="285"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo3label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.expandEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">parent</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java"></process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo3label" name="label" nodeType="label">
    <location x="171" y="321"/>
    <figSize height="17" width="61"/>
    <node>invokePojo3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link16</targetConnections>
    <location x="300" y="285"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.dict.dict.EosDictEntry</process:from>
      <process:to type="query">criteria/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">parent/seqno</process:from>
      <process:to type="query">criteria/_expr[1]/seqno</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">like</process:from>
      <process:to type="query">criteria/_expr[1]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">end</process:from>
      <process:to type="query">criteria/_expr[1]/_likeRule</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">parent/dictid</process:from>
      <process:to type="query">criteria/_expr[2]/parentid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="302" y="321"/>
    <figSize height="17" width="25"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="检查叶子节点" displayName="checkLeaf" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link12" name="link12" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <targetConnections>link15</targetConnections>
    <location x="565" y="285"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.dict.impl.DictService.checkLeaf</process:partner>
      <process:instance instanceName="DictServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="data" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="543" y="321"/>
    <figSize height="17" width="73"/>
    <node>invokeSpring0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-02 10:16:52" date="2013-12-02Z" description="" name="查询业务字典项" version="6.3"/>
  <process:variables>
    <process:variable description="父节点" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="parent"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
    <process:input description="" isArray="false" modelType="com.eos.foundation.PageCond" name="page"/>
    <process:input description="" isArray="false" name="sortField" primitiveType="String"/>
    <process:input description="" isArray="false" name="sortOrder" primitiveType="String"/>
    <process:input description="类型代码" isArray="false" name="dicttypeid" primitiveType="String"/>
    <process:input description="父节点ID" isArray="false" name="dictid" primitiveType="String"/>
    <process:input description="父节点类型ID" isArray="false" name="parenttypeid" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="data"/>
    <process:output description="" isArray="false" name="total" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

package org.gocom.components.coframe.auth;

import java.io.IOException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpSession;

import org.apache.commons.codec.binary.Base64;

import com.eos.data.datacontext.DataContextManager;
import com.eos.data.datacontext.ISessionMap;
import com.eos.system.annotation.Bizlet;
import com.primeton.ext.common.muo.MUODataContextHelper;

public class RSAServlet {

	@Bizlet("获取公钥")
	public String doGetRsaKey() throws ServletException, IOException {
		Map<Integer, String> keyMap = null;
		// 生成公钥和私钥
		try {
			keyMap = this.genKeyPair();
			this.sessionPut("RSAKEY", keyMap);
			System.out.println("密钥对" + keyMap);
			System.out.println("随机生成的公钥为:" + keyMap.get(0));
			return keyMap.get(0);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			return null;
		}
	}

	public void sessionPut(String key, Map<Integer, String> keyMap) {
		ISessionMap sessionMap = DataContextManager.current().getSessionCtx();
		if (sessionMap == null) {
			sessionMap = MUODataContextHelper.getMapContextFactory().getSessionMap();
		}

		if (sessionMap != null) {
			Object rootObject = sessionMap.getRootObject();
			if ((rootObject != null) && (rootObject instanceof HttpSession)) {
				HttpSession session = (HttpSession) rootObject;

				session.setAttribute(key, keyMap);
				System.out.println("RSA:sessionid=====" + session.getId());
			}
			return;
		}
	}

	/**
	 * 随机生成密钥对
	 * 
	 * @throws NoSuchAlgorithmException
	 */
	public Map<Integer, String> genKeyPair() throws NoSuchAlgorithmException {
		Map<Integer, String> keyMap = new HashMap<Integer, String>(); // 用于封装随机产生的公钥与私钥
		// KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
		KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
		// 初始化密钥对生成器，密钥大小为96-1024位
		keyPairGen.initialize(1024, new SecureRandom());
		// 生成一个密钥对，保存在keyPair中
		KeyPair keyPair = keyPairGen.generateKeyPair();
		RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate(); // 得到私钥
		RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic(); // 得到公钥
		String publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
		// 得到私钥字符串
		String privateKeyString = new String(Base64.encodeBase64((privateKey.getEncoded())));
		// 将公钥和私钥保存到Map
		keyMap.put(0, publicKeyString); // 0表示公钥
		keyMap.put(1, privateKeyString); // 1表示私钥
		return keyMap;
	}

}

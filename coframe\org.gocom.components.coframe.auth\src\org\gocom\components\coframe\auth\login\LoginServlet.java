package org.gocom.components.coframe.auth.login;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 登录请求
 * 
 * <AUTHOR>
 * 
 */
public class LoginServlet extends HttpServlet {
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {

		String name = (String) req.getParameter("userId");
		String pass = (String) req.getParameter("password");

		req.getRequestDispatcher(
				"/org.gocom.components.coframe.auth.login.login.flow?userId="
						+ name + "&password=" + pass).forward(req, resp);
	}
}
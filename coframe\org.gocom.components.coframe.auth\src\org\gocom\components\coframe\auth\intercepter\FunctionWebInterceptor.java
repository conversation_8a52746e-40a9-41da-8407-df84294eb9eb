/*
 * Copyright 2013 Primeton Technologies Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gocom.components.coframe.auth.intercepter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.gocom.components.coframe.auth.login.LoginService;
import org.gocom.components.coframe.auth.menu.DefaultMenuAuthService;
import org.gocom.components.coframe.flowconfig.authconfig.AuthInspect;
import org.gocom.components.coframe.framework.constants.IAppConstants;
import org.gocom.components.coframe.tools.IAuthConstants;
import org.gocom.components.coframe.tools.IConstants;

import com.eos.access.http.IWebInterceptor;
import com.eos.access.http.IWebInterceptorChain;
import com.eos.data.datacontext.DataContextManager;
import com.eos.data.datacontext.IMUODataContext;
import com.eos.data.datacontext.IUserObject;
import com.eos.data.datacontext.UserObject;
import com.eos.foundation.database.DatabaseExt;
import com.eos.system.utility.StringUtil;
import com.primeton.cap.TenantManager;
import com.primeton.cap.management.resource.IManagedResource;
import com.primeton.cap.management.resource.manager.ResourceRuntimeManager;
import com.primeton.cap.party.Party;
import com.primeton.ext.access.http.processor.MultipartResolver;
import com.primeton.ext.common.muo.MUODataContextHelper;

import commonj.sdo.DataObject;

/**
 * 功能WebInterceptor
 * 
 * <AUTHOR>
 * 
 */
public class FunctionWebInterceptor implements IWebInterceptor {

	public void doIntercept(HttpServletRequest request, HttpServletResponse response, IWebInterceptorChain chain)
			throws IOException, ServletException {
		// System.out.println(MultipartResolver.getEncoding());
		request.setCharacterEncoding(MultipartResolver.getEncoding());
		// System.out.println(request.getAttribute("是否过滤"+UserLoginWebInterceptor.IS_FILTER_FUNCTION_KEY));
		if ("true".equals(request.getAttribute(UserLoginWebInterceptor.IS_FILTER_FUNCTION_KEY))) {
			chain.doIntercept(request, response);
			return;
		}

		String token = request.getParameter("token");
		if (!"U2FsdGVkX1/K7/3AYhYvqYIQz5d2L00r".equals(token)) {
			chain.doIntercept(request, response);
			return;
		}

		IMUODataContext muo = MUODataContextHelper.create(request.getSession());
		DataContextManager.current().setMUODataContext(muo);
		String resId = request.getParameter(IAuthConstants.FUNCTION_PARAN_RESOURCE_ID);

		String resType = request.getParameter(IAuthConstants.FUNCTION_PARAM_REAOURCE_TYPE);
		if (StringUtil.isNotEmpty(resType) && !IAuthConstants.FUNCTION_TO_RESOURCE_TYPE.equals(resType)) {
			chain.doIntercept(request, response);
			return;
		}
		IManagedResource managedResource = null;
		if (StringUtils.isEmpty(resId) || StringUtils.isEmpty(resType)) {
			Object rt = getManagedResourceNew(request);
			// Object rt = getManagedResource(request);//Object rt =
			// getManagedResourceNew(request);
			if (rt instanceof String && "-1".equals(String.valueOf(rt))) {
				request.getRequestDispatcher("/coframe/auth/noAuth.jsp").forward(request, response);
			} else if (rt instanceof IManagedResource) {
				managedResource = (IManagedResource) rt;
			} else {
				managedResource = null;
			}
		} else {
			managedResource = ResourceRuntimeManager.getInstance().getManagedResource(resId, resType);
		}

		if (hasPermission(managedResource, request)) {
			chain.doIntercept(request, response);
		} else {
			request.getRequestDispatcher("/coframe/auth/noAuth.jsp").forward(request, response);
		}
	}

	/**
	 * 判断是否拥有访问该角色的权限
	 * 
	 * @param managedResource
	 * @param request
	 * @return
	 */
	public boolean hasPermission(IManagedResource managedResource, HttpServletRequest request) {
		if (managedResource == null) {
			return true;
		} else if (managedResource != null
				&& "0".equals(managedResource.getAttribute(IAppConstants.FUNCTION_IS_CHECK))) {
			return true;
		}
		UserObject userObject = (UserObject) request.getSession().getAttribute("userObject");
		String roleIds = (String) userObject.get(IConstants.ROLE_LIST);
		List<Party> roles = new ArrayList<Party>();
		if (roleIds != null) {
			String[] roleIdArr = roleIds.split(com.primeton.cap.auth.IConstants.SPLIET);
			for (String roleId : roleIdArr) {
				if (!StringUtils.isEmpty(roleId)) {
					roles.add(new Party(IConstants.ROLE_PARTY_TYPE_ID, roleId, roleId, roleId));
				}
			}
		}
		String funcCode = managedResource.getResourceID();
		DefaultMenuAuthService menuAuthService = new DefaultMenuAuthService(roles);
		if (menuAuthService.canAccessFunction(funcCode)) {
			return AuthInspect.inspectCode(request);
			// return true;
		}
		return false;
	}

	/**
	 * 获得功能资源
	 * 
	 * @param request
	 * @return
	 */
	public IManagedResource getManagedResource(HttpServletRequest request) {
		// System.out.println("getManagedResource");
		String servletPath = request.getServletPath();
		List<IManagedResource> resources = ResourceRuntimeManager.getInstance()
				.getRootManagedResourceListByType(IAuthConstants.FUNCTION_TO_RESOURCE_TYPE);
		String tenantId = TenantManager.getCurrentTenantID();
		// System.out.println("tenantId=="+tenantId);
		// String appcode = SessionUtil.getDeptType();
		IMUODataContext ss = com.eos.data.datacontext.DataContextManager.current().getMUODataContext();
		// System.out.println("获取用户信息-----"+ss.getUserObject());
		DataObject EmpObject = ((DataObject) ss.getUserObject().getAttributes().get("EmpObject"));
		HashMap LoginUserInfo = (HashMap) EmpObject.get("LoginUserInfo");
		String appcode = LoginUserInfo.get("S_DEPTTYPE") == null ? "" : LoginUserInfo.get("S_DEPTTYPE").toString();
		List<IManagedResource> nresources = new ArrayList<IManagedResource>();
		for (IManagedResource resource : resources) {
			// 根据不带参数的url匹配 如果有其他链接相同 则会出现匹配错误 增加 当前菜单appCode 与用户appcode 匹配 相对应才匹配url
			// 如果不属于当前用户就忽略
			if (resource.getAttribute("appCode").equals(appcode)) {
				nresources.add(resource);
			}
		}
		for (IManagedResource resource : nresources) {
			String funcAction = resource.getAttribute(IAppConstants.FUNCTION_URL);
			String funcURI = StringUtil.substringBefore(funcAction, "?");

			if (StringUtil.isNotEmpty(funcAction)) {

				IUserObject userObject = DataContextManager.current().getMUODataContext().getUserObject();
				if (userObject == null)
					continue; // 在注销的时候，此处会是null，by zgr 2016-02-29
				// 如果拿不到功能，说明功能不可用，直接跳过
				// 根据不带参数的url匹配 如果有其他链接相同 则会出现匹配错误 增加 当前菜单appCode 与用户appcode 匹配 相对应才匹配url
				// 如果不属于当前用户就忽略
				if ((resource == null || !resource.getAttribute("appCode").equals(appcode)) && appcode != null) {
					continue;
				}
				// String funcURI = StringUtil.substringBefore(funcAction, "?");
				String funcTenant = resource.getTenantID();
				if (StringUtil.equal(tenantId, funcTenant) && StringUtil.equal(servletPath, funcURI)) {
					String funcParam = resource.getAttribute(IAppConstants.FUNCTION_PARA_INFO);
					Map paramMap = request.getParameterMap();
					if (funcParam == null) {
						return resource;
					} else if (isContain(paramMap, funcParam)) {
						return resource;
					}
				}
			}
		}
		return null;
	}

	/**
	 * 获得功能资源 NEW 由于比对权限是根据url来操作 一个账户 下不同部门有相同的url 同一个浏览器 多个选项卡 在切换部门之后
	 * 切换前的界面存留界面url 会导致匹配错误
	 * 
	 * @param request
	 * @return
	 */
	public Object getManagedResourceNew(HttpServletRequest request) {
		String servletPath = request.getServletPath();
		List<IManagedResource> resources = ResourceRuntimeManager.getInstance()
				.getRootManagedResourceListByType(IAuthConstants.FUNCTION_TO_RESOURCE_TYPE);
		String tenantId = TenantManager.getCurrentTenantID();
		String appcode = LoginService.getAppCode();
		/*
		 * List<IManagedResource> nresources = new ArrayList<IManagedResource>(); for
		 * (IManagedResource resource : resources) { //根据不带参数的url匹配 如果有其他链接相同 则会出现匹配错误
		 * 增加 当前菜单appCode 与用户appcode 匹配 相对应才匹配url 如果不属于当前用户就忽略
		 * if(resource.getAttribute("appCode").equals(appcode)){
		 * nresources.add(resource); } }
		 */
		String rts = "0";
		for (IManagedResource resource : resources) {
			String funcAction = resource.getAttribute(IAppConstants.FUNCTION_URL);
			String funcURI = StringUtil.substringBefore(funcAction, "?");
			if (StringUtil.isNotEmpty(funcAction)) {
				IUserObject userObject = DataContextManager.current().getMUODataContext().getUserObject();
				if (userObject == null)
					continue;
				// 在注销的时候，此处会是null，by zgr 2016-02-29
				// 如果拿不到功能，说明功能不可用，直接跳过
				// 根据不带参数的url匹配 如果有其他链接相同 则会出现匹配错误 增加 当前菜单appCode 与用户appcode 匹配 相对应才匹配url
				// 如果不属于当前用户就忽略

				String funcTenant = resource.getTenantID();
				if (StringUtil.equal(tenantId, funcTenant) && StringUtil.contains(servletPath, funcURI)) {
					String funcParam = resource.getAttribute(IAppConstants.FUNCTION_PARA_INFO);
					Map paramMap = getParameterMap(request);
					String menuSeq = String.valueOf(paramMap.get("menuSeq"));
					if (!menuSeq.equals("null")) {
						Object[] results = DatabaseExt.queryByNamedSql("default",
								"org.gocom.components.coframe.auth.coframeUserInit.select_currurl_appcode", paramMap);
						if (results.length == 0) {
							return "-1";
						} else {
							String urlappcode = ((Map<String, String>) results[0]).get("APPCODE");
							String urlfunccode = ((Map<String, String>) results[0]).get("FUNCCODE");
							String funcCode = resource.getResourceID();
							if (!funcCode.equals(urlfunccode)) {
								rts = "-1";
								continue;
							}
						}
					}

					if (funcParam == null) {
						return resource;
					} else if (isContain(paramMap, funcParam)) {
						return resource;
					}
				}
			}
		}
		if (rts.equals("-1")) {
			return rts;
		}
		return null;
	}

	public static Map getParameterMap(HttpServletRequest request) {
		// 参数Map
		Map properties = request.getParameterMap();
		// 返回值Map
		Map returnMap = new HashMap();
		Iterator entries = properties.entrySet().iterator();
		Map.Entry entry;
		String name = "";
		String value = "";
		while (entries.hasNext()) {
			entry = (Map.Entry) entries.next();
			name = (String) entry.getKey();
			Object valueObj = entry.getValue();
			if (null == valueObj) {
				value = "";
			} else if (valueObj instanceof String[]) {
				String[] values = (String[]) valueObj;
				for (int i = 0; i < values.length; i++) {
					value = values[i] + ",";
				}
				value = value.substring(0, value.length() - 1);
			} else {
				value = valueObj.toString();
			}
			returnMap.put(name, value);
		}
		return returnMap;
	}

	/**
	 * 判断用户请求某功能的参数中是否包含该功能所必需参数
	 * 
	 * @param paramMap  用户请求参数Map
	 * @param funcParam 功能必需参数
	 * @return true 包含
	 */
	public boolean isContain(Map paramMap, String funcParam) {
		String[] funcParams = funcParam.split("&");
		boolean bool = true;
		for (int i = 0; i < funcParams.length; i++) {
			if (!StringUtil.isBlank(funcParams[i])) {
				bool = false;
				String[] params = funcParams[i].split("=");
				if (!StringUtil.isBlank(params[0])) {
					String paramValue = (String) paramMap.get(params[0]);
					if (StringUtil.isBlank(paramValue)) {
						if (paramValue.equals(params[1])) {
							bool = true;
						}
					} else if (StringUtil.isBlank(params[1])) {
						bool = true;
					}
				} else {
					bool = true;
				}
			}
		}
		return bool;
	}
}
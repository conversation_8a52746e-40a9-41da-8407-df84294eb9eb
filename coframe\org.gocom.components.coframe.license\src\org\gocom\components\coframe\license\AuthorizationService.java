package org.gocom.components.coframe.license;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.gocom.components.coframe.auth.login.LoginService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.eos.foundation.common.utils.FileUtil;
import com.eos.foundation.eoscommon.ConfigurationUtil;
import com.eos.runtime.core.ApplicationContext;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;
import com.primeton.ext.access.http.IUploadFile;
import com.wx.license.client.LicenseClient;
import com.wx.license.client.LicenseEntity;
import com.wx.license.client.LicenseException;

/**
 * 授权注册服务类
 * 
 * <AUTHOR>
 * 
 */
@Bizlet
public class AuthorizationService {

	static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtg7lLuGYej+Lw2xX+kEjZ/jaOKPD66EmKbUyQixqxrBZKJMhOuj/7yWrZ5bDommzkmWdzJaV7hUX1Mgjr7D2FTbT8eSd/bh0GzuxNSunomROe+vt+yav7a7b9dIq4fHoBxG0PcIWbMMVG/9HrBpwmleQfle9nqonMLveGck1OMQIDAQAB";

	private static Logger logger = TraceLoggerFactory.getLogger(AuthorizationService.class);

	@Bizlet("校验应用是否授权保存到session")
	public static void checkandSetsession() {
		JSONObject json = new JSONObject();
		if (AuthorizationService.ischeck().equals("expire")) {
			json.put("ISAUTH", "EXPIRE");
			json.put("MACADDRESS", AuthorizationUtils.getMac());
			json.put("MACHINECODE", AuthorizationUtils.getMachineCode());
			json.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
			json.put("COUNTDOWNDATE", AuthorizationService.ischeck().equals("true") ? AuthorizationService.getExpireDay() : "");
			json.put("EXPRIEDATE", AuthorizationUtils.getExpireTime());
		} else {
			json.put("ISAUTH", AuthorizationService.ischeck().equals("true") ? "TRUE" : "FALSE");
			json.put("MACADDRESS", AuthorizationUtils.getMac());
			json.put("MACHINECODE", AuthorizationUtils.getMachineCode());
			json.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
			json.put("COUNTDOWNDATE", AuthorizationService.ischeck().equals("true") ? AuthorizationService.getExpireDay() : "");
			json.put("EXPRIEDATE", AuthorizationUtils.getExpireTime());

		}
		logger.info(json.toString());
		LoginService.setApplication("ISAUTH", json.toString());
	}

	public static String authResultString = null;

	public static String getExpireDay() {
		Long expireTime = AuthorizationUtils.getExpireTime();
		if (expireTime != null) {
			int days = (int) ((expireTime - new Date().getTime()) / (1000 * 3600 * 24));
			return String.valueOf(days + 1);
		}
		return String.valueOf(-1);
	}

	/**
	 * 校验应用是否授权
	 * 
	 * @return
	 */
	public static String ischeck() {
		String filePath = ApplicationContext.getInstance().getApplicationConfigPath() + File.separator + "vanxsoft.lic";
		if (authResultString == null) {
			authResultString = AuthorizationService.activateLicFile(filePath);
		}
		return authResultString;
	}

	public static void checkAuth() {
		String filePath = ApplicationContext.getInstance().getApplicationConfigPath() + File.separator + "vanxsoft.lic";
		authResultString = AuthorizationService.activateLicFile(filePath);
	}

	@Bizlet
	public static HashMap<String, String> getAuthInfo() {
		HashMap<String, String> map = new HashMap<>();
		String version = ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "version");
		if (AuthorizationService.ischeck().equals("expire")) {
			map.put("ISAUTH", "EXPIRE");
			map.put("MACADDRESS", AuthorizationUtils.getMac());
			map.put("MACHINECODE", AuthorizationUtils.getMachineCode());
			map.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
			map.put("COUNTDOWNDATE", AuthorizationService.ischeck().equals("true") ? AuthorizationService.getExpireDay() : "");
			map.put("VERSION", version);
			map.put("EXPRIEDATE", String.valueOf(AuthorizationUtils.getExpireTime()));
		} else {
			map.put("ISAUTH", AuthorizationService.ischeck().equals("true") ? "TRUE" : "FALSE");
			map.put("MACADDRESS", AuthorizationUtils.getMac());
			map.put("MACHINECODE", AuthorizationUtils.getMachineCode());
			map.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
			map.put("COUNTDOWNDATE", AuthorizationService.ischeck().equals("true") ? AuthorizationService.getExpireDay() : "");
			map.put("VERSION", version);
			map.put("EXPRIEDATE", String.valueOf(AuthorizationUtils.getExpireTime()));
		}
		return map;
	}

	@Bizlet("获取机器信息")
	public static Map<String, String> getMachineInfo() {
		Map<String, String> machineInfo = new HashMap<>();
		machineInfo.put("macAddress", AuthorizationUtils.getMac());
		machineInfo.put("machineCode", AuthorizationUtils.getMachineCode());
		String projectName = AuthorizationUtils.getVersionInfo("projectName");
		if (StringUtils.isNotEmpty(projectName)) {
			machineInfo.put("projectName", projectName);
		}
		return machineInfo;
	}

	static String licRootPath = ApplicationContext.getInstance().getApplicationConfigPath();
	static String regInfoPath = licRootPath + "/registrationInfo.rif";
	static String licTempPath = licRootPath + "/licTemp";

	@Bizlet("获取注册信息")
	public static String generateRequestCode(String license) {
		FileOutputStream fileOutputStream = null;
		try {
			JSONObject jsonObj = JSONObject.parseObject(license);
			// 添加项目名称信息
			if (AuthorizationUtils.setVersionInfo("projectName", jsonObj.getString("projectName"))) {
				AuthorizationService.checkandSetsession();
			}
			// 添加本地版本信息
			jsonObj.put("version", AuthorizationUtils.getVersionInfo("fullVersion"));
			// 添加授权信息（若有）
			String licenseInfo = AuthorizationUtils.getLicenseInfo();
			if (licenseInfo != null) {
				jsonObj.put("activationCode", licenseInfo);
			}
			fileOutputStream = new FileOutputStream(regInfoPath);
			fileOutputStream.write(Base64.getEncoder().encodeToString(jsonObj.toJSONString().getBytes("UTF-8")).getBytes(StandardCharsets.UTF_8));
			fileOutputStream.close();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return regInfoPath;
	}

	@Bizlet("保存Licnese文件")
	public static String saveLicenseFile(IUploadFile uploadFile) {
		String filePath = licTempPath + File.separator + uploadFile.getFileName();
		FileUtil.moveFileToDir(uploadFile.getFilePath(), licTempPath);
		String newFileName = "vanxsoft.lic";
		String newFilePath = licTempPath + File.separator + newFileName;
		File file = new File(newFilePath);
		// 若所在文件夹不存在，则创建
		if (!file.getParentFile().exists()) {
			file.getParentFile().mkdirs();
		}
		// 如果存在同名文件，则删除
		if (file.exists()) {
			FileUtil.deleteFile(newFilePath);
		}
		FileUtil.renameFile(filePath, newFileName);
		return newFilePath;
	}

	@Bizlet("验证Licnese文件")
	public static String activateLicFile(String filePath) {
		LicenseClient client = new LicenseClient();
		try {
			LicenseEntity licenseEntity = client.loadLicense(new FileInputStream(filePath),
					Base64.getDecoder().decode(publicKey));
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("macAddress", AuthorizationUtils.getMac());
			jsonObject.put("version", AuthorizationUtils.getVersionInfo("fullVersion"));
			jsonObject.put("machineCode", AuthorizationUtils.getMachineCode());
			JSONObject parseObject = JSON.parseObject(new String(licenseEntity.getData(), "UTF-8"));
			if (compareJSONObject(jsonObject, parseObject)) {
				AuthorizationUtils.setExpireTime(licenseEntity.getExpireTime());
				logger.info("licenseEntity ==>" + JSONObject.toJSONString(licenseEntity));
				// 授权码已过期
				if (licenseEntity.getExpireTime() < System.currentTimeMillis()) {
					logger.info("licenseEntity.getExpireTime() ==>" + licenseEntity.getExpireTime());
					logger.info("System.currentTimeMillis() ==>" + System.currentTimeMillis());
					return "expire";
				}
				// 将lic文件从temp目录移入正式目录
				replaceLicFile(filePath);
				AuthorizationService.authResultString = "true";
				JSONObject json = new JSONObject();
				json.put("ISAUTH", "TRUE");
				json.put("MACADDRESS", AuthorizationUtils.getMac());
				json.put("MACHINECODE", AuthorizationUtils.getMachineCode());
				json.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
				json.put("COUNTDOWNDATE",
						AuthorizationService.ischeck().equals("true") ? AuthorizationService.getExpireDay() : "");
				json.put("EXPRIEDATE", licenseEntity.getExpireTime());
				AuthorizationUtils.setExpireTime(licenseEntity.getExpireTime());
				LoginService.setApplication("ISAUTH", json.toString());
				return "true";
			} else if ("BACKDOOR".equals(parseObject.get("machineCode"))) {
				// 将lic文件从temp目录移入正式目录
				replaceLicFile(filePath);
				AuthorizationService.authResultString = "true";
				JSONObject json = new JSONObject();
				json.put("ISAUTH", "TRUE");
				json.put("MACADDRESS", AuthorizationUtils.getMac());
				json.put("MACHINECODE", AuthorizationUtils.getMachineCode());
				json.put("PROJECTNAME", AuthorizationUtils.getVersionInfo("projectName"));
				json.put("COUNTDOWNDATE",
						AuthorizationService.ischeck().equals("true") ? AuthorizationService.getExpireDay() : "");
				json.put("EXPRIEDATE", licenseEntity.getExpireTime());
				LoginService.setApplication("ISAUTH", json.toString());
				AuthorizationUtils.setExpireTime(licenseEntity.getExpireTime());
				return "true";
			}
			logger.info("授权未通过 ===> licInfo ==>" + parseObject.toJSONString());
			logger.info("授权未通过 ===> myInfo ==>" + jsonObject.toJSONString());
		} catch (LicenseException e) {
			logger.error(e.getMessage());
			return "expire";
		} catch (UnsupportedEncodingException e) {
			logger.error(e.getMessage());
			return "false";
		} catch (FileNotFoundException e) {
			logger.error(e.getMessage());
			return "false";
		}
		return "false";
	}

	// 验证license文件通过，将temp目录下的lic文件替换到正式目录
	public static void replaceLicFile(String tempPath) {
		if (tempPath.contains("licTemp")) {
			String licPath = licRootPath + File.separator + "vanxsoft.lic";
			// 删除旧的lic文件
			File lic = new File(licPath);
			if (lic.exists()) {
				FileUtil.deleteFile(licPath);
			}
			// 将temp目录中的lic文件移动过来
			FileUtil.moveFileToDir(tempPath, licRootPath);
		}
	}

	public static boolean compareJSONObject(JSONObject obj, JSONObject targetObj) {
		Set<String> keys = obj.keySet();
		for (String key : keys) {
			if (!targetObj.get(key).equals(obj.get(key))) {
				return false;
			}
		}
		return true;
	}
}

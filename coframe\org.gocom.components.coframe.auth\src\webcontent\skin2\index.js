/**
 * 
 */
//监听二级菜单区域鼠标移上移下事件
var gkm_lev2menu = [ ];
function gkm_menu_load(data){
	if(data){
		var gkmmenu = "";
		for(var i = 0; i < data.length; i++){
			var dlid="gkm_levmenu"+i.toString();
			var menuName = data[i].menuName;
			var linkAction = data[i].linkAction ? data[i].linkAction: "";
			var menuPrimeKey = data[i].menuPrimeKey;
			var menuSeq = data[i].menuSeq;
			if(linkAction == ""){
				gkmmenu += "<ul class='gkm_menu'><dl><dt onmouseover='gkm_showLev2Menu(" + i + ")'><a url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></dt></dl><dl id='"+dlid+"' class='gkm_levmenu'></dl></ul>";
			}else{
				gkmmenu += "<ul class='gkm_menu'><dl><dt onmouseover='gkm_showLev2Menu(" + i + ")'><a url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></dt></dl><dl id='"+dlid+"' class='gkm_levmenu'></dl></ul>";
			}
			var secondChilds = "";
			if(data[i].childrenMenuTreeNodeList){
				var Lev2childrens = data[i].childrenMenuTreeNodeList;
				for(var j = 0; j < Lev2childrens.length; j++){
					var menuName = Lev2childrens[j].menuName;
					var linkAction = Lev2childrens[j].linkAction ? Lev2childrens[j].linkAction : "";
					var menuPrimeKey = Lev2childrens[j].menuPrimeKey;
					var menuSeq = Lev2childrens[j].menuSeq;
					var otherChildrens = getThirdOrMoreChild(Lev2childrens[j]);
					if(otherChildrens && (otherChildrens != "")){
						otherChildrens = "<ul class='lev3'>" + otherChildrens + "</ul>";
						secondChilds += "<dt class='gkm_levmenu_dt'><ul><a class='lev2' url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></ul>" + otherChildrens + "</dt>";
					}else{
						secondChilds += "<dt class='gkm_levmenu_dt'><ul class='lev2'><a href='#' url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></ul></dt>";
					}
				}
			}
			gkm_lev2menu[i] = secondChilds;
		}
		insertGkmMenuToHtml(gkmmenu);
	}
}
function insertGkmMenuToHtml(gkmmenu){
	$("#gkm_menu_load").html(gkmmenu);
}
	
	
function gkm_showLev2Menu(position){
	var gkm_levmenu="gkm_levmenu"+position;
	document.getElementById(gkm_levmenu).style.display = 'block';
	document.getElementById("mainField").style.zIndex = "-1";
	if(lev2menu){
		var lev2menuHtml = gkm_lev2menu[position];
		document.getElementById(gkm_levmenu).innerHTML = "<div class='nav_subcats_div'></div>" + lev2menuHtml;
	}
	//var lev1menuHeight = $("#levmenu").css("height");
	//var lev2menuHeight = $("#gkm_levmenu").css("height");
	//$("#gkm_levmenu").css("min-height", lev1menuHeight);
	//监听<a>标签点击事件，如果有url，则在主内容区展现url,如果有jspUrl，则跳转到该url界面
	$("a").click(function(){
    	if($(this).attr("url")){
	    	var url = $(this).attr("url") ? $(this).attr("url") : "";
	    	setIFrame(url);
	    	//document.getElementById("levmenu").style.display = 'none';
        	document.getElementById(gkm_levmenu).style.display = 'none';
        	document.getElementById("mainField").style.zIndex = "1";
    	}
    });
    
    //二级菜单监听鼠标事件，选中时，字体颜色，粗细有变化
    $(".gkm_levmenu_dt").hover(function(){
    	$(this).find("a:first").addClass("dtSelected");
    },function(){
    	$(this).find("a:first").removeClass("dtSelected");
    });
    //三级以及更多级菜单第一个左侧不展现boder
    if($(".lev3").children()){
		$(".lev3").each(function(){
			$(this).find("a").first().removeClass("Nav");
		});
	}
}
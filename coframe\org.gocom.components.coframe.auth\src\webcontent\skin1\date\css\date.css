body{background-color: #F3F3F3}
*{
  font-family:courier;
}
#content{
    width: 100%;margin: 0 auto;

}
.smlb {
    font-size: 20px; color: white; float: right; margin-right: 6%; margin-top: -33%;
}

.aorange {
   font-weight: bold; font-size: 20px; color: #ff5f07; width:57px;
}
.day {
   font-size: 18px; color: #000000; 
}
.day td{
  color:#9F9E9E;
}
.one {
   font-weight: normal; font-size: 20px; color: #000000; width:57px;
}
.bs {
   font-size: 12px; color: #9F9E9E;width:57px;
}
.lb {
   font-size: 16px; color: #0099ff
}
.sm {
   font-size: 14px; color: #333399
}
.smz {
   font-weight: bold; font-size: 14px; color: #0099ff
}


#cal-content{
   height:40;text-align: center;background-color:"#FAFBFB";

}
.detallu{
   width:100%; border:0; padding:2px; color: white;
   
}
.lnotice{
  margin-top: 34%;
}
#detail{
  margin-top: 12px;text-align: center;background-color: #3ba6dc;
}
#oTime{
  color: white ;text-align: center;font-family: 
}

.sm span{
  color: black;

}
.thead span{
   color: #2EBEB7;

}
#clock{
    font-family: courier;margin-top: -23%;padding-bottom: 5%;
  }



/*年份，月份切换*/
.year_select{
  display:inline-block;padding-right: 23px;
}
.home_select{
  display:inline-block; padding-right: 18px;
}
/*清除ie的默认选择框样式清除，隐藏下拉箭头*/
select::-ms-expand { display: none; }
.ch{
  background-color:#3ba6dc;
}
.day td{
  width:57px;color:white; text-align:center;
}
select {
  margin: 9px 0; appearance: none;-moz-appearance: none;-webkit-appearance: none;border: 0; border-radius: 2px; color: #000000; padding-top: 6px; background-color: white; padding-bottom: 2px;
}



/*日历头部*/

.table_head{
    background-color: #3ba6dc;width: 100%;text-align: center;
  }

.week{
   border-spacing: 0;   
}


div.tishishgen0{ 
  text-align:center; margin:50px 0 10px 0; color:#666
}

div.shenbox {
    margin-top: 20px;
}
 {
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-family: "microsoft yahei",Times New Roman;
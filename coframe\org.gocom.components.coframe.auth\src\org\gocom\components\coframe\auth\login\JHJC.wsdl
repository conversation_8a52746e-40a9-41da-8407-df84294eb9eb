<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:ns1="http://schemas.xmlsoap.org/soap/http" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://jhipwebsvc.bjgoodwill.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="JhipSsoWebsvcImplService" targetNamespace="http://jhipwebsvc.bjgoodwill.com/">
<wsdl:message name="getUserDetailInfoResponse">
<wsdl:part name="returnVal" type="xsd:string"> </wsdl:part>
</wsdl:message>
<wsdl:message name="getUserDetailInfo">
<wsdl:part name="inputPara" type="xsd:string"> </wsdl:part>
</wsdl:message>
<wsdl:portType name="JhipSsoWebsvc">
<wsdl:operation name="getUserDetailInfo">
<wsdl:input message="tns:getUserDetailInfo" name="getUserDetailInfo"> </wsdl:input>
<wsdl:output message="tns:getUserDetailInfoResponse" name="getUserDetailInfoResponse"> </wsdl:output>
</wsdl:operation>
</wsdl:portType>
<wsdl:binding name="JhipSsoWebsvcImplServiceSoapBinding" type="tns:JhipSsoWebsvc">
<soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
<wsdl:operation name="getUserDetailInfo">
<soap:operation soapAction="" style="rpc"/>
<wsdl:input name="getUserDetailInfo">
<soap:body namespace="http://jhipwebsvc.bjgoodwill.com/" use="literal"/>
</wsdl:input>
<wsdl:output name="getUserDetailInfoResponse">
<soap:body namespace="http://jhipwebsvc.bjgoodwill.com/" use="literal"/>
</wsdl:output>
</wsdl:operation>
</wsdl:binding>
<wsdl:service name="JhipSsoWebsvcImplService">
<wsdl:port binding="tns:JhipSsoWebsvcImplServiceSoapBinding" name="JhipssoWebsvcSOAP">
<soap:address location="http://************:8080/jhip/services/jhipsso"/>
</wsdl:port>
</wsdl:service>
</wsdl:definitions>

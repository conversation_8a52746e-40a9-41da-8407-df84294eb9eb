<?xml version="1.0" encoding="UTF-8"?>
<handlers>
	<!--add custom StartupListener here-->
	<!-- thw following three are executed as the order they appears:international resource loading,
	log configruation files registration,modules loading -->
	<!-- international resource loading -->
	<handler
		handle-class="com.primeton.ext.common.international.startup.AppResourceStartupRuntimeListener" />
	<!-- log configruation files registration -->
	<!-- reading log4j-trace.xml, config Trace log-->
	<!--
	Trace Log registration was moved to RuntimeJ2EEHost
	<handler
		handle-class="com.primeton.runtime.core.impl.startup.TraceLoggingStartupRuntimeListener" />
	 -->

	<!--reading log4j-sys.xml log4j-engine.xml-->
	<handler
		handle-class="com.primeton.ext.common.logging.startup.AppLoggingStartupRuntimeListener" />

	<!-- modules loading -->
	<handler
		handle-class="com.primeton.ext.common.config.startup.AppConfigStartupRuntimeListener" />

	<handler
		handle-class="com.primeton.ext.common.asyn.startup.AppAsynStartupRuntimeListener" />
	<!-- 
	<handler
		handle-class="com.primeton.workflow.api.listener.WFAppConfigStartupRuntimeListener" />
 	-->
	<!--Listener that start DataContext,will register serializer and deserializer of DataContext -->
	<handler
		handle-class="com.primeton.ext.data.datacontext.startup.DataContextStartupListener" />

	<!--start enginge-->
	<handler handle-class="com.primeton.engine.core.impl.process.EngineStartUpListener"/>

	<!--start DAS_ENTITY-->
	<handler handle-class="com.primeton.ext.das.common.DASCommonStartupListener"/>

	<!-- register application's classloader to EJB-->
	<handler
		handle-class="com.primeton.ext.access.client.startup.AppClientStartupRuntimeListener" />

	<!--read configruation of runtime enviroment from sys-config.xml（[moudle name="Runtime"]）-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ResourceConfigLoaderStartUpListener"/>

	<!--start WebUI-->
	<handler handle-class="com.primeton.web.startup.WebUIStartUpListener"/>

	<!--load SCA resource register-->
	<handler handle-class="com.primeton.sca.host.impl.SCAStartUpListener"/>

	<!--load Connection releaser-->
	<handler handle-class="com.primeton.ext.common.connection.startup.ConnectionStartupListener"/>

	<!-- load xpath cache -->
	<handler handle-class="com.primeton.ext.data.xpath.startup.XPathStartupListener"/>

	<!--
	构件运行环境（应用级）启动监听器，系统默认会在启动用户定义的Listener之后启动
	-->

	<!--以下是系统级的配置Startup，不能修改-->

	<!--加载Contribution的Listener，处理config下handler-contribution.xml文件-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ContributionListenerRegisterStartUpListener"/>

	<!--加载所有的IModelLoader，处理所有ClassPath下的/META-INF/modelloader.conf文件-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ModelLoaderRegisterStartUpListener"/>

	<!--加载所有的IResourceLoadListener，处理所有ClassPath下的/META-INF/handler-resourceload.xml文件-->
	<!--此配置主要是针对SCA资源类型来处理的 -->
	<!--<handler handle-class="com.primeton.ext.runtime.resource.startup.ResourceLoaderListenerStartUpListener"/> -->

	<!--所有Contribution资源加载-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ResourceLoaderStartUpListener"/>

	<!--Schedule启动加载-->
	<handler handle-class="com.primeton.ext.common.schedule.startup.AppScheduleStartupRuntimeListener"/>

	<!--启动资源热更新线程-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ResourceMonitorStartUpListener"/>

	<!--应用启动后注册deploy接口-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ContributionDeployStartUpListener"/>

	<!--应用启动后注册线程管理接口-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ThreadManagerStartUpListener"/>

	<!--应用启动完毕后通知事件-->
	<handler handle-class="com.primeton.ext.runtime.resource.startup.ApplicationStartedNotifyListener"/>

	<!--start Access_Http-->
	<handler handle-class="com.primeton.ext.access.http.startup.AccessHttpStartupListener"/>

	<!--
	<handler handle-class="com.primeton.ext.das.entity.EagerLoadHbmRuntimeListener"/>
	-->
	
	<!--OLAP 引擎监听器，启动缓存等-->
	<handler handle-class="com.primeton.bps.statistics.olap.OLAPEngineListener"/>
	
	<!--启动WebService安全-->
	<handler handle-class="com.primeton.workflow.api.webservice.security.SecurityServiceRegistry"/>
</handlers>
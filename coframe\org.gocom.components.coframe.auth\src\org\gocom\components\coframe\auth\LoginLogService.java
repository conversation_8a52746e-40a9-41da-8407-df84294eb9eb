package org.gocom.components.coframe.auth;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

import org.gocom.components.coframe.flowconfig.authconfig.AuthConfig;

import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;

public class LoginLogService {

	private static Logger logger = TraceLoggerFactory.getLogger(LoginLogService.class);
	int missdate = 600000;
	SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 设置日期格式

	@Bizlet("保存登陆记录/记录错误次数")
	public void saveLoginLog(String loginflag, String userid) {
		if (userid == null)
			return;
		LoginLog log = new LoginLog();
		log.setUserid(userid);
		log.setFlag(loginflag);
		log.save();
		if ("1".equals(loginflag)) {
			this.doLoginMiss(userid);
		} else {
			this.LoginUpdate(userid);
		}
	}

	public void doLoginMiss(String userid) {
		LoginMiss loginmiss = new LoginMiss();
		HashMap missmap = loginmiss.querymiss(userid);
		loginmiss.setUserid(userid);
		loginmiss.setMissNum(Integer.valueOf(missmap.get("MISS_NUMBER").toString()) + 1);
		int i = loginmiss.getMissNum();
		String md = this.getMissDate(loginmiss.getMissNum());
		if (md == null) {
			loginmiss.setMissFlag(missmap.get("MISS_FLAG").toString());
			loginmiss.setMissTime(missmap.get("MISS_TIME") == null ? null : missmap.get("MISS_TIME").toString());
		} else {
			loginmiss.setMissFlag("1");
			loginmiss.setMissTime(md);
		}
		loginmiss.updateMiss();
	}

	/**
	 * 判断是否可以登陆
	 * 
	 * @param userid
	 * @return
	 */
	@Bizlet("判断处于限制限制登陆")
	public boolean checklog(String userid) {
		boolean flag = true;
		HashMap<String, String> map = this.checkloginnum(userid);
		String isflag = map.get("loginflag");
		if ("FALSE".equals(isflag))
			return false;
		return flag;
	}

	/**
	 * 判断密码是否已过期
	 * 
	 * @param userid
	 * @return
	 * @throws ParseException 
	 */
	@Bizlet("判断密码是否已过期")
	public boolean checkPasswordInvaldate(String userid) throws ParseException {
		LoginMiss loginmiss = new LoginMiss();
		HashMap rsMap = loginmiss.queryPasswordInvaldate(userid);
		Object invaldateObj = rsMap.get("INVALDATE");
		if (invaldateObj == null) {
			return true;
		}
		Date invaldate = df.parse(rsMap.get("INVALDATE").toString());
		return invaldate.getTime() > new Date().getTime();
	}

	@Bizlet
	public boolean ishtmlupdate() {
		AuthConfig cfg = new AuthConfig();
		return cfg.checkIndex();
	}

	/**
	 * 检测是否限制登陆
	 * 
	 * @param userid
	 * @return
	 */
	@Bizlet("检测是否限制登陆")
	public HashMap<String, String> checkloginnum(String userid) {
		if (userid == null)
			return null;
		;
		HashMap<String, String> map = new HashMap<String, String>();
		LoginMiss loginmiss = new LoginMiss();
		@SuppressWarnings("rawtypes")
		HashMap rsMap = loginmiss.querymiss(userid);
		logger.info("rsMap => " + rsMap);
		String flag = rsMap.get("MISS_FLAG").toString();
		Date date = new Date();
		try {
			date = rsMap.get("MISS_TIME") == null ? new Date() : df.parse(rsMap.get("MISS_TIME").toString());
		} catch (ParseException e) {
			e.printStackTrace();
		}
		if (date.getTime() > new Date().getTime() && "1".equals(flag) && SysConfig.queryConfig("LOGINCONTROL")) {
			map.put("loginflag", "FALSE");
			map.put("missnum", rsMap.get("MISS_NUMBER").toString());
			map.put("misstime", rsMap.get("MISS_TIME").toString());
		} else {
			map.put("loginflag", "TRUE");
			map.put("missnum", rsMap.get("MISS_NUMBER").toString());
			map.put("misstime", rsMap.get("MISS_TIME") == null ? "" : rsMap.get("MISS_TIME").toString());
		}
		if ("".equals(map.get("misstime"))) {
			map.put("misstime", String.valueOf(df.format(date)));
		}
		return map;
	}

	public void LoginUpdate(String userid) {
		LoginMiss loginmiss = new LoginMiss();
		loginmiss.setUserid(userid);
		loginmiss.setMissFlag("0");
		loginmiss.setMissNum(0);
		loginmiss.setMissTime(df.format(new Date()));
		loginmiss.updateMiss();
	}

	/**
	 * 获取等待时间
	 * 
	 * @return
	 */
	public String getMissDate(int missnum) {
		if (missnum >= 3) {
			int a = missnum % 3;
			if (a != 0)
				return null;
			return df.format(new Date().getTime() + (missnum / 3) * missdate);
		}
		return null;
	}

}

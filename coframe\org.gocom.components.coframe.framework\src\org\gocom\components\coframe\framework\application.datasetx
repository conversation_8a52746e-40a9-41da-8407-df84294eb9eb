<?xml version="1.0" encoding="UTF-8"?>
<model:DataSetDiagram xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://www.primeton.com/datamodel" name="application" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" displayName="application" author="Administrator">
  <nodes xsi:type="model:PEntityNode" id="node0" name="AppFuncgroup" nodeType="table" targetConnections="association1 association3" displayName="AppFuncgroup" author="蔡述尧" allUseViewModel="true" instanceClass="org.gocom.components.coframe.framework.application.impl.AppFuncgroupImpl" table="app_funcgroup" genStrategy="assigned">
    <sourceConnections xsi:type="model:AssociationElement" id="association1" name="FK_F_FUNG_FUNG" lineType="reference" sourceNode="node0" targetNode="node0" displayName="app_funcgroup_app_funcgroup" associationType="2" srcCascadeType="11" srcLazyLoad="false" SrcRoleName="AppFuncgroup" TgtRoleName="AppFuncgroup" mockSrcNum="n" mockTgtNum="1">
      <bendPoint heightToEnd="-173" heightToStart="-173" widthToEnd="0" widthToStart="0"/>
      <bendPoint heightToEnd="-173" heightToStart="-173" widthToEnd="105" widthToStart="105"/>
      <bendPoint heightToEnd="0" heightToStart="0" widthToEnd="105" widthToStart="105"/>
      <fKAssociationField srcProperty="property2" tgtProperty="property10"/>
    </sourceConnections>
    <sourceConnections xsi:type="model:AssociationElement" id="association0" name="FK_F_APP_FUNCTION" lineType="reference" sourceNode="node0" targetNode="node4" displayName="app_funcgroup_app_application" associationType="2" srcCascadeType="11" srcLazyLoad="false" SrcRoleName="AppFuncgroup" TgtRoleName="AppApplication" mockSrcNum="n" mockTgtNum="1">
      <fKAssociationField srcProperty="property0" tgtProperty="property50"/>
    </sourceConnections>
    <location x="570" y="60"/>
    <size height="286" width="150"/>
    <figSize height="20" width="213"/>
    <entPojoProperty id="assproperty0" name="appFuncgroup" propertyType="EntityProperty" association="association1" refId="org.gocom.components.coframe.framework.application.AppFuncgroup" isAssociationSource="true">
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.subcount" id="appFuncgroup.subcount" label="subcount">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.grouplevel" id="appFuncgroup.grouplevel" label="grouplevel">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.tenant_id" id="appFuncgroup.tenant_id" label="tenantId">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.app_id" id="appFuncgroup.app_id" label="appId">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.funcgroupseq" id="appFuncgroup.funcgroupseq" label="funcgroupseq">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.isleaf" id="appFuncgroup.isleaf" label="isleaf">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.funcgroupid" id="appFuncgroup.funcgroupid" label="funcgroupid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.appid" id="appFuncgroup.appid" label="appid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.funcgroupname" id="appFuncgroup.funcgroupname" label="funcgroupname">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.parentgroup" id="appFuncgroup.parentgroup" label="parentgroup">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
    </entPojoProperty>
    <entPojoProperty id="assproperty1" name="appApplication" propertyType="EntityProperty" association="association0" refId="org.gocom.components.coframe.framework.application.AppApplication" isAssociationSource="true">
      <entViewField dict="false" dictTypeId="" entityField="appApplication.appdesc" id="appApplication.appdesc" label="应用程序描述">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.manarole" id="appApplication.manarole" label="manarole">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.app_id" id="appApplication.app_id" label="应用信息">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.ipport" id="appApplication.ipport" label="应用程序端口">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.isopen" id="appApplication.isopen" label="是否开通">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.opendate" id="appApplication.opendate" label="开通日期">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="date"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr name="maxValue"/>
          <attr name="srcFormat"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr name="defaultNull"/>
          <attr name="allowNull"/>
          <attr name="styleClass"/>
          <attr name="submitFormat"/>
          <attr attrValue="" name="readonly"/>
          <attr name="minValue"/>
          <attr name="style"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr name="format"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.apptype" id="appApplication.apptype" label="应用程序类型">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.appid" id="appApplication.appid" label="应用程序编号">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.appcode" id="appApplication.appcode" label="应用程序代码">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.appname" id="appApplication.appname" label="应用程序名称">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.ipaddr" id="appApplication.ipaddr" label="应用程序IP">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.iniwp" id="appApplication.iniwp" label="iniwp">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.maintenance" id="appApplication.maintenance" label="maintenance">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.intaskcenter" id="appApplication.intaskcenter" label="intaskcenter">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.url" id="appApplication.url" label="应用上下文">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.tenant_id" id="appApplication.tenant_id" label="租户信息">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appApplication.demo" id="appApplication.demo" label="demo">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
    </entPojoProperty>
    <columnProperty id="property10" name="funcgroupid" displayName="功能组编号" columnName="FUNCGROUPID" nullAble="false" PK="true" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funcgroupid" entityField="funcgroupid" id="funcgroupid" label="功能组编号" name="funcgroupid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funcgroupid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property0" name="appid" displayName="应用程序编号" association="association0" columnName="APPID" FK="true" nullAble="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appid" entityField="appid" id="appid" label="应用程序编号" name="appid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property1" name="funcgroupname" displayName="功能组名称" columnName="FUNCGROUPNAME" nullAble="true" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funcgroupname" entityField="funcgroupname" id="funcgroupname" label="功能组名称" name="funcgroupname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funcgroupname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property2" name="parentgroup" displayName="父功能组" association="association1" columnName="PARENTGROUP" FK="true" nullAble="true" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="parentgroup" entityField="parentgroup" id="parentgroup" label="父功能组" name="parentgroup" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="parentgroup" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property3" name="grouplevel" displayName="功能组层次" columnName="GROUPLEVEL" nullAble="true" columnType="INT" eosDataType="Int" length="11" dasType="int" overWriteDefaultColumnType="false" showType="Int" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="grouplevel" entityField="grouplevel" id="grouplevel" label="功能组层次" name="grouplevel" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="grouplevel" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property4" name="funcgroupseq" displayName="功能组序号" columnName="FUNCGROUPSEQ" nullAble="true" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funcgroupseq" entityField="funcgroupseq" id="funcgroupseq" label="功能组序号" name="funcgroupseq" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funcgroupseq" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property5" name="isleaf" displayName="是否为叶子" columnName="ISLEAF" nullAble="true" columnType="CHAR" eosDataType="String" length="1" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="isleaf" entityField="isleaf" id="isleaf" label="是否为叶子" name="isleaf" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="isleaf" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property6" name="subcount" displayName="subcount" columnName="SUBCOUNT" nullAble="true" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="subcount" entityField="subcount" id="subcount" label="subcount" name="subcount" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="subcount" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property7" name="app_id" displayName="应用信息" columnName="APP_ID" nullAble="true" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appId" entityField="app_id" id="app_id" label="应用信息" name="appId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property8" name="tenant_id" displayName="租户信息" columnName="TENANT_ID" nullAble="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenant_id" id="tenant_id" label="租户信息" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node1" name="AppMenu" nodeType="table" targetConnections="association2" displayName="AppMenu" author="蔡述尧" allUseViewModel="true" instanceClass="org.gocom.components.coframe.framework.application.impl.AppMenuImpl" table="app_menu" genStrategy="assigned">
    <sourceConnections xsi:type="model:AssociationElement" id="association2" name="FK_F_MENU_MENU" lineType="reference" sourceNode="node1" targetNode="node1" displayName="app_menu_app_menu" associationType="2" srcCascadeType="11" srcLazyLoad="false" SrcRoleName="AppMenu" TgtRoleName="AppMenu" mockSrcNum="n" mockTgtNum="1">
      <bendPoint heightToEnd="-252" heightToStart="-252" widthToEnd="0" widthToStart="0"/>
      <bendPoint heightToEnd="-252" heightToStart="-252" widthToEnd="105" widthToStart="105"/>
      <bendPoint heightToEnd="0" heightToStart="0" widthToEnd="105" widthToStart="105"/>
      <fKAssociationField srcProperty="property21" tgtProperty="property9"/>
    </sourceConnections>
    <location x="75" y="45"/>
    <size height="444" width="150"/>
    <figSize height="20" width="150"/>
    <entPojoProperty id="assproperty2" name="appMenu" propertyType="EntityProperty" association="association2" refId="org.gocom.components.coframe.framework.application.AppMenu" isAssociationSource="true">
      <entViewField dict="false" dictTypeId="" entityField="appMenu.menulevel" id="appMenu.menulevel" label="menulevel">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.rootid" id="appMenu.rootid" label="rootid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.menuid" id="appMenu.menuid" label="menuid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.funccode" id="appMenu.funccode" label="funccode">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.isleaf" id="appMenu.isleaf" label="isleaf">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.tenant_id" id="appMenu.tenant_id" label="tenantId">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.appid" id="appMenu.appid" label="appid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.menuaction" id="appMenu.menuaction" label="menuaction">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.parentsid" id="appMenu.parentsid" label="parentsid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.subcount" id="appMenu.subcount" label="subcount">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.uientry" id="appMenu.uientry" label="uientry">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.menulabel" id="appMenu.menulabel" label="menulabel">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.openmode" id="appMenu.openmode" label="openmode">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.expandpath" id="appMenu.expandpath" label="expandpath">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.parameter" id="appMenu.parameter" label="parameter">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.imagepath" id="appMenu.imagepath" label="imagepath">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.menucode" id="appMenu.menucode" label="menucode">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.menuseq" id="appMenu.menuseq" label="menuseq">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.menuname" id="appMenu.menuname" label="menuname">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.app_id" id="appMenu.app_id" label="appId">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appMenu.displayorder" id="appMenu.displayorder" label="displayorder">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </entViewField>
    </entPojoProperty>
    <columnProperty id="property9" name="menuid" displayName="菜单编号" columnName="MENUID" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="menuid" entityField="menuid" id="menuid" label="菜单编号" name="menuid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="menuid" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property11" name="menuname" displayName="菜单名称" columnName="MENUNAME" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="menuname" entityField="menuname" id="menuname" label="菜单名称" name="menuname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="menuname" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property12" name="menulabel" displayName="菜单显示名称" columnName="MENULABEL" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="menulabel" entityField="menulabel" id="menulabel" label="菜单显示名称" name="menulabel" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="menulabel" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property13" name="menucode" displayName="菜单代码" columnName="MENUCODE" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="menucode" entityField="menucode" id="menucode" label="菜单代码" name="menucode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="menucode" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property14" name="isleaf" displayName="是否叶子菜单" columnName="ISLEAF" nullAble="true" PK="false" columnType="CHAR" eosDataType="String" length="1" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="isleaf" entityField="isleaf" id="isleaf" label="是否叶子菜单" name="isleaf" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="isleaf" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property15" name="menuaction" displayName="菜单url" persistent="false" columnName="MENUACTION" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" default="">
      <comment>冗余字段</comment>
      <viewField dict="false" dictTypeId="" displayName="menuaction" entityField="menuaction" id="menuaction" label="菜单url" name="menuaction" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="menuaction" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property16" name="parameter" displayName="菜单参数" columnName="PARAMETER" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>冗余字段</comment>
      <viewField dict="false" dictTypeId="" displayName="parameter" entityField="parameter" id="parameter" label="菜单参数" name="parameter" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="parameter" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property17" name="uientry" displayName="uientry" columnName="UIENTRY" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>针对EXT模式提供，例如abf_auth/function/module.xml</comment>
      <viewField dict="false" dictTypeId="" displayName="uientry" entityField="uientry" id="uientry" label="uientry" name="uientry" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="uientry" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property18" name="menulevel" displayName="菜单层次" columnName="MENULEVEL" nullAble="true" PK="false" columnType="SMALLINT" eosDataType="Short" length="6" dasType="short" overWriteDefaultColumnType="false" showType="Short" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="menulevel" entityField="menulevel" id="menulevel" label="菜单层次" name="menulevel" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="menulevel" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property19" name="rootid" displayName="rootid" columnName="ROOTID" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="rootid" entityField="rootid" id="rootid" label="rootid" name="rootid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="rootid" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property21" name="parentsid" displayName="父菜单id" association="association2" columnName="PARENTSID" FK="true" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="parentsid" entityField="parentsid" id="parentsid" label="父菜单id" name="parentsid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="parentsid" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property22" name="displayorder" displayName="显示顺序" columnName="DISPLAYORDER" nullAble="true" PK="false" columnType="SMALLINT" eosDataType="Short" length="6" dasType="short" overWriteDefaultColumnType="false" showType="Short" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="displayorder" entityField="displayorder" id="displayorder" label="显示顺序" name="displayorder" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="displayorder" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property23" name="imagepath" displayName="菜单闭合图片路径" columnName="IMAGEPATH" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="100" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="imagepath" entityField="imagepath" id="imagepath" label="菜单闭合图片路径" name="imagepath" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="imagepath" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property24" name="expandpath" displayName="菜单展开图片路径" columnName="EXPANDPATH" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="100" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="expandpath" entityField="expandpath" id="expandpath" label="菜单展开图片路径" name="expandpath" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="expandpath" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property25" name="menuseq" displayName="菜单序号" columnName="MENUSEQ" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="menuseq" entityField="menuseq" id="menuseq" label="菜单序号" name="menuseq" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="menuseq" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property26" name="openmode" displayName="打开方式" columnName="OPENMODE" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>主窗口打开、弹出窗口打开...</comment>
      <viewField dict="false" dictTypeId="" displayName="openmode" entityField="openmode" id="openmode" label="打开方式" name="openmode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="openmode" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property27" name="subcount" displayName="子菜单数" columnName="SUBCOUNT" nullAble="true" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="subcount" entityField="subcount" id="subcount" label="子菜单数" name="subcount" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="subcount" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property28" name="appid" displayName="应用程序编号" columnName="APPID" nullAble="true" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appid" entityField="appid" id="appid" label="应用程序编号" name="appid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="appid" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property29" name="funccode" displayName="功能代码" columnName="FUNCCODE" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funccode" entityField="funccode" id="funccode" label="功能代码" name="funccode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="funccode" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property30" name="app_id" displayName="应用信息" columnName="APP_ID" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appId" entityField="app_id" id="app_id" label="应用信息" name="appId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr defaultValue="" name="iterateId"/>
          <attr defaultValue="" name="scope"/>
          <attr defaultValue="" name="propertyType"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr defaultValue="" name="property"/>
          <attr defaultValue="" name="modelField"/>
          <attr defaultValue="" name="filter"/>
          <attr name="ignore"/>
        </fieldView>
        <fieldEdit name="appId" showAllAttr="false">
          <type name="text"/>
          <attr defaultValue="" name="onvalidation"/>
          <attr defaultValue="" name="borderStyle"/>
          <attr defaultValue="true" name="visible"/>
          <attr defaultValue="true" name="validateOnChanged"/>
          <attr defaultValue="请输入整数" name="intErrorText"/>
          <attr defaultValue="" name="emptyText"/>
          <attr defaultValue="请输入URL格式" name="urlErrorText"/>
          <attr defaultValue="" name="destroy"/>
          <attr defaultValue="" name="id"/>
          <attr defaultValue="请输入邮件格式" name="emailErrorText"/>
          <attr defaultValue="" name="height"/>
          <attr defaultValue="请输入数字" name="floatErrorText"/>
          <attr defaultValue="true" name="allowInput"/>
          <attr defaultValue="" name="style"/>
          <attr defaultValue="" name="maxLength"/>
          <attr defaultValue="数字不能小于 {0}" name="minErrorText"/>
          <attr defaultValue="" name="onblur"/>
          <attr defaultValue="" name="name"/>
          <attr defaultValue="不能少于 {0} 个字符" name="minLengthErrorText"/>
          <attr defaultValue="不能超过 {0} 个字符" name="maxLengthErrorText"/>
          <attr defaultValue="请输入日期格式 {0}" name="dateErrorText"/>
          <attr defaultValue="" name="value"/>
          <attr defaultValue="false" name="required"/>
          <attr defaultValue="false" name="selectOnFocus"/>
          <attr defaultValue="true" name="enabled"/>
          <attr defaultValue="数字必须在 {0} 到 {1} 之间" name="rangeErrorText"/>
          <attr defaultValue="" name="cls"/>
          <attr defaultValue="" name="requiredErrorText"/>
          <attr defaultValue="" name="width"/>
          <attr defaultValue="" name="inputStyle"/>
          <attr defaultValue="" name="onkeyup"/>
          <attr defaultValue="" name="onenter"/>
          <attr defaultValue="nui-textbox" name="class"/>
          <attr defaultValue="" name="onkeydown"/>
          <attr defaultValue="" name="onfocus"/>
          <attr defaultValue="editor" name="property"/>
          <attr defaultValue="" name="vtype"/>
          <attr defaultValue="数字不能大于 {0}" name="maxErrorText"/>
          <attr defaultValue="字符数必须在 {0} 到 {1} 之间" name="rangeLengthErrorText"/>
          <attr defaultValue="true" name="validateOnLeave"/>
          <attr defaultValue="icon" name="errorMode"/>
          <attr defaultValue="" name="onvaluechanged"/>
          <attr defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property31" name="tenant_id" displayName="租户信息" columnName="TENANT_ID" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenant_id" id="tenant_id" label="租户信息" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" name="extAttr"/>
          <attr attrValue="" name="title"/>
          <attr attrValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" name="styleClass"/>
          <attr attrValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" name="validateAttr"/>
          <attr attrValue="" name="readonly"/>
          <attr attrValue="" name="disabled"/>
          <attr attrValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property68" name="icon" displayName="菜单图标" userDefined="true" columnName="ICON" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="">
      <viewField dict="false" dictTypeId="" displayName="property2" entityField="icon" id="icon" label="菜单图标" name="property2" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr name="maxLength"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="filter"/>
        </fieldView>
        <fieldEdit name="property2" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="true" defaultValue="true" name="enabled"/>
          <attr attrValue="" defaultValue="" name="borderStyle"/>
          <attr attrValue="" defaultValue="" name="cls"/>
          <attr attrValue="true" defaultValue="true" name="visible"/>
          <attr attrValue="" defaultValue="" name="width"/>
          <attr attrValue="" defaultValue="" name="inputStyle"/>
          <attr attrValue="nui-textbox" defaultValue="nui-textbox" name="class"/>
          <attr attrValue="" defaultValue="" name="emptyText"/>
          <attr attrValue="" defaultValue="" name="id"/>
          <attr attrValue="" defaultValue="" name="height"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="name"/>
          <attr attrValue="false" defaultValue="false" name="required"/>
          <attr attrValue="" defaultValue="" name="tooltip"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node2" name="AppFunction" nodeType="table" targetConnections="association4" displayName="AppFunction" author="蔡述尧" allUseViewModel="true" instanceClass="org.gocom.components.coframe.framework.application.impl.AppFunctionImpl" table="app_function" genStrategy="assigned">
    <sourceConnections xsi:type="model:AssociationElement" id="association3" name="FK_F_FUNGROUP_FUN" lineType="reference" sourceNode="node2" targetNode="node0" displayName="app_function_app_funcgroup" associationType="2" srcCascadeType="11" srcLazyLoad="false" SrcRoleName="AppFunction" TgtRoleName="AppFuncgroup" mockSrcNum="n" mockTgtNum="1">
      <fKAssociationField srcProperty="property32" tgtProperty="property10"/>
    </sourceConnections>
    <location x="570" y="378"/>
    <size height="250" width="150"/>
    <figSize height="20" width="189"/>
    <entPojoProperty id="assproperty3" name="appFuncgroup" propertyType="EntityProperty" association="association3" refId="org.gocom.components.coframe.framework.application.AppFuncgroup" isAssociationSource="true">
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.subcount" id="appFuncgroup.subcount" label="subcount">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.grouplevel" id="appFuncgroup.grouplevel" label="grouplevel">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.app_id" id="appFuncgroup.app_id" label="应用信息">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.funcgroupseq" id="appFuncgroup.funcgroupseq" label="funcgroupseq">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.isleaf" id="appFuncgroup.isleaf" label="isleaf">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.funcgroupid" id="appFuncgroup.funcgroupid" label="funcgroupid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.tenant_id" id="appFuncgroup.tenant_id" label="租户信息">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.appid" id="appFuncgroup.appid" label="appid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.funcgroupname" id="appFuncgroup.funcgroupname" label="funcgroupname">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFuncgroup.parentgroup" id="appFuncgroup.parentgroup" label="parentgroup">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
    </entPojoProperty>
    <columnProperty id="property20" name="funccode" displayName="功能编码" columnName="FUNCCODE" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funccode" entityField="funccode" id="funccode" label="功能编码" name="funccode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funccode" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property32" name="funcgroupid" displayName="功能组编号" association="association3" columnName="FUNCGROUPID" FK="true" nullAble="true" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funcgroupid" entityField="funcgroupid" id="funcgroupid" label="功能组编号" name="funcgroupid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funcgroupid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property33" name="funcname" displayName="功能名称" columnName="FUNCNAME" nullAble="false" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funcname" entityField="funcname" id="funcname" label="功能名称" name="funcname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funcname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property34" name="funcdesc" displayName="功能描述" columnName="FUNCDESC" nullAble="true" columnType="VARCHAR" eosDataType="String" length="512" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funcdesc" entityField="funcdesc" id="funcdesc" label="功能描述" name="funcdesc" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funcdesc" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property35" name="funcaction" displayName="功能URL" columnName="FUNCACTION" nullAble="true" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funcaction" entityField="funcaction" id="funcaction" label="功能URL" name="funcaction" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funcaction" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property36" name="parainfo" displayName="功能参数信息" columnName="PARAINFO" nullAble="true" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>需要定义参数规范</comment>
      <viewField dict="false" dictTypeId="" displayName="parainfo" entityField="parainfo" id="parainfo" label="功能参数信息" name="parainfo" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="parainfo" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property37" name="ischeck" displayName="ischeck" columnName="ISCHECK" nullAble="true" columnType="CHAR" eosDataType="String" length="1" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="ischeck" entityField="ischeck" id="ischeck" label="ischeck" name="ischeck" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="ischeck" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property38" name="functype" displayName="功能类型" columnName="FUNCTYPE" nullAble="true" defaultValue="'1'" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="1">
      <comment>前台功能、后台服务、报表功能...</comment>
      <viewField dict="false" dictTypeId="" displayName="functype" entityField="functype" id="functype" label="功能类型" name="functype" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="functype" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property39" name="ismenu" displayName="是否菜单" columnName="ISMENU" nullAble="true" columnType="CHAR" eosDataType="String" length="1" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>该功能是否可以作为菜单入口</comment>
      <viewField dict="false" dictTypeId="" displayName="ismenu" entityField="ismenu" id="ismenu" label="是否菜单" name="ismenu" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="ismenu" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property40" name="app_id" displayName="应用信息" columnName="APP_ID" nullAble="true" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appId" entityField="app_id" id="app_id" label="应用信息" name="appId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property41" name="tenant_id" displayName="租户信息" columnName="TENANT_ID" nullAble="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenant_id" id="tenant_id" label="租户信息" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node3" name="AppFuncresource" nodeType="table" displayName="AppFuncresource" author="蔡述尧" allUseViewModel="true" instanceClass="org.gocom.components.coframe.framework.application.impl.AppFuncresourceImpl" table="app_funcresource" genStrategy="assigned">
    <sourceConnections xsi:type="model:AssociationElement" id="association4" name="FK_F_FUN_RES" lineType="reference" sourceNode="node3" targetNode="node2" displayName="app_funcresource_app_function" associationType="2" srcCascadeType="11" srcLazyLoad="false" SrcRoleName="AppFuncresource" TgtRoleName="AppFunction" mockSrcNum="n" mockTgtNum="1">
      <fKAssociationField srcProperty="property43" tgtProperty="property20"/>
    </sourceConnections>
    <location x="315" y="405"/>
    <size height="196" width="150"/>
    <figSize height="20" width="177"/>
    <entPojoProperty id="assproperty4" name="appFunction" propertyType="EntityProperty" association="association4" refId="org.gocom.components.coframe.framework.application.AppFunction" isAssociationSource="true">
      <entViewField dict="false" dictTypeId="" entityField="appFunction.ismenu" id="appFunction.ismenu" label="ismenu">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.functype" id="appFunction.functype" label="functype">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.parainfo" id="appFunction.parainfo" label="parainfo">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.ischeck" id="appFunction.ischeck" label="ischeck">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.app_id" id="appFunction.app_id" label="应用信息">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.funccode" id="appFunction.funccode" label="funccode">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.funcgroupid" id="appFunction.funcgroupid" label="funcgroupid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.funcdesc" id="appFunction.funcdesc" label="funcdesc">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.funcname" id="appFunction.funcname" label="funcname">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.tenant_id" id="appFunction.tenant_id" label="租户信息">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="appFunction.funcaction" id="appFunction.funcaction" label="funcaction">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
    </entPojoProperty>
    <columnProperty id="property42" name="resid" displayName="资源编号" columnName="RESID" nullAble="false" PK="true" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="resid" entityField="resid" id="resid" label="资源编号" name="resid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="resid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property43" name="funccode" displayName="功能编号" association="association4" columnName="FUNCCODE" FK="true" nullAble="true" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="funccode" entityField="funccode" id="funccode" label="功能编号" name="funccode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="funccode" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property44" name="restype" displayName="资源类型" columnName="RESTYPE" nullAble="true" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>JSP、页面流、逻辑流等</comment>
      <viewField dict="false" dictTypeId="" displayName="restype" entityField="restype" id="restype" label="资源类型" name="restype" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="restype" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property45" name="respath" displayName="资源路径" columnName="RESPATH" nullAble="true" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="respath" entityField="respath" id="respath" label="资源路径" name="respath" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="respath" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property46" name="compackname" displayName="所属构件包" columnName="COMPACKNAME" nullAble="true" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="compackname" entityField="compackname" id="compackname" label="所属构件包" name="compackname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="compackname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property47" name="resname" displayName="资源名称" columnName="RESNAME" nullAble="true" columnType="VARCHAR" eosDataType="String" length="40" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="resname" entityField="resname" id="resname" label="资源名称" name="resname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="resname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property48" name="app_id" displayName="应用信息" columnName="APP_ID" nullAble="true" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appId" entityField="app_id" id="app_id" label="应用信息" name="appId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property49" name="tenant_id" displayName="租户信息" columnName="TENANT_ID" nullAble="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenant_id" id="tenant_id" label="租户信息" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node4" name="AppApplication" nodeType="table" targetConnections="association0" displayName="AppApplication" author="蔡述尧" allUseViewModel="true" instanceClass="org.gocom.components.coframe.framework.application.impl.AppApplicationImpl" table="app_application" genStrategy="assigned">
    <location x="330" y="15"/>
    <size height="354" width="150"/>
    <figSize height="20" width="153"/>
    <columnProperty id="property50" name="appid" displayName="应用程序编号" columnName="APPID" nullAble="false" PK="true" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appid" entityField="appid" id="appid" label="应用程序编号" name="appid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property51" name="appcode" displayName="应用程序代码" columnName="APPCODE" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="32" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appcode" entityField="appcode" id="appcode" label="应用程序代码" name="appcode" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appcode" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property52" name="appname" displayName="应用程序名称" columnName="APPNAME" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="50" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appname" entityField="appname" id="appname" label="应用程序名称" name="appname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property53" name="apptype" displayName="应用程序类型" columnName="APPTYPE" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="apptype" entityField="apptype" id="apptype" label="应用程序类型" name="apptype" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="apptype" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property54" name="isopen" displayName="是否开通" columnName="ISOPEN" nullAble="true" PK="false" columnType="CHAR" eosDataType="String" length="1" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="isopen" entityField="isopen" id="isopen" label="是否开通" name="isopen" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="isopen" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property55" name="opendate" displayName="开通日期" columnName="OPENDATE" nullAble="true" PK="false" columnType="DATE" eosDataType="Date" dasType="date" overWriteDefaultColumnType="false" showType="Date" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="opendate" entityField="opendate" id="opendate" label="开通日期" name="opendate" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="opendate" showAllAttr="false">
          <type name="date"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr name="maxValue"/>
          <attr name="srcFormat"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr name="defaultNull"/>
          <attr name="allowNull"/>
          <attr name="styleClass"/>
          <attr name="submitFormat"/>
          <attr attrValue="" name="readonly"/>
          <attr name="minValue"/>
          <attr name="style"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr name="format"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property56" name="url" displayName="应用上下文" columnName="URL" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="256" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="url" entityField="url" id="url" label="应用上下文" name="url" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="url" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property57" name="appdesc" displayName="应用程序描述" columnName="APPDESC" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="512" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appdesc" entityField="appdesc" id="appdesc" label="应用程序描述" name="appdesc" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appdesc" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property58" name="maintenance" displayName="maintenance" columnName="MAINTENANCE" nullAble="true" PK="false" columnType="DECIMAL" eosDataType="Decimal" length="10" dasType="big_decimal" overWriteDefaultColumnType="false" showType="Decimal" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="maintenance" entityField="maintenance" id="maintenance" label="maintenance" name="maintenance" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="maintenance" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property59" name="manarole" displayName="manarole" columnName="MANAROLE" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="manarole" entityField="manarole" id="manarole" label="manarole" name="manarole" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="manarole" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property60" name="demo" displayName="demo" columnName="DEMO" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="512" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="demo" entityField="demo" id="demo" label="demo" name="demo" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="demo" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property61" name="iniwp" displayName="iniwp" columnName="INIWP" nullAble="true" PK="false" columnType="CHAR" eosDataType="String" length="1" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>0:否,1:是</comment>
      <viewField dict="false" dictTypeId="" displayName="iniwp" entityField="iniwp" id="iniwp" label="iniwp" name="iniwp" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="iniwp" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property62" name="intaskcenter" displayName="intaskcenter" columnName="INTASKCENTER" nullAble="true" PK="false" columnType="CHAR" eosDataType="String" length="1" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <comment>0:否,1:是</comment>
      <viewField dict="false" dictTypeId="" displayName="intaskcenter" entityField="intaskcenter" id="intaskcenter" label="intaskcenter" name="intaskcenter" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="intaskcenter" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property63" name="ipaddr" displayName="应用程序IP" columnName="IPADDR" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="50" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="ipaddr" entityField="ipaddr" id="ipaddr" label="应用程序IP" name="ipaddr" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="ipaddr" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property64" name="ipport" displayName="应用程序端口" columnName="IPPORT" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="10" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="ipport" entityField="ipport" id="ipport" label="应用程序端口" name="ipport" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="ipport" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property65" name="app_id" displayName="应用信息" columnName="APP_ID" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="appId" entityField="app_id" id="app_id" label="应用信息" name="appId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="appId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property66" name="tenant_id" displayName="租户信息" columnName="TENANT_ID" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="tenantId" entityField="tenant_id" id="tenant_id" label="租户信息" name="tenantId" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="tenantId" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property67" name="protocoltype" displayName="协议类型" userDefined="true" columnName="protocol_type" nullAble="true" PK="false" columnType="varchar" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="">
      <viewField dict="false" dictTypeId="" displayName="property0" entityField="protocoltype" id="protocoltype" label="协议类型" name="property0" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="property0" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <topRuler/>
  <leftRuler/>
</model:DataSetDiagram>

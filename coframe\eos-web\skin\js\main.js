/*
* @Author: <PERSON>
* @Date:   2016-03-01 16:58:40
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-03-14 16:04:38
*/
/////////////////////////////////////////
$(document).ready(function(){////
////////////////////////////////////////////////////////////////////////////////////
    //侧边菜单
    $('.navbar-menu').click(function() {
        var w1 = $(window).width();
        var w2 = w1 -180;
        $(this).toggleClass('glyphicon-remove');
        $(this).toggleClass('glyphicon-menu-hamburger');
        $(this).toggleClass('active');
    // 左右滑动
    if($(".menu-left").css('left') == '0px'){
        $(".main-body").animate({
            marginLeft:0,
            width:w1
        },400);
        $(".main-body").animate({width:w1},400);
        $(".menu-left").animate({left:-180},400,function () {
            // var indexMenuWidth = (($('.main-body').width()-100)/3) ;
            // $(".index-menu").animate({width:indexMenuWidth},400);
        });
    }else{
        $(".main-body").animate({
            marginLeft:180,
            width:w2
        },400);
        $(".menu-left").animate({left:0},400);
            // var indexMenuWidth = (($('.main-body').width()-100-180)/3) ;
            // $(".index-menu").animate({width:indexMenuWidth},400);
        };
    });
    // 侧边菜单下拉
    $('.menu-list li h4').click(function() {
        $(this).children("span").toggleClass('glyphicon-menu-right');
        $(this).children("span").toggleClass('glyphicon-menu-down');
        $(this).parent().children('ul').slideToggle(200);
    });
    //切换部门下拉菜单
    $('#dropdown').hover(function() {
        $(this).children('ul').slideDown(200);
    }, function() {
        $(this).children('ul').slideUp(200);
    });
    //主体部分尺寸控制
    function windowSize () {
        var mainBodyHeight = $('.main-body').height();
        var tabNavHeight = $('.nav').height();
        if(csh == 1){
            $('.main-body').width($('.main-body').width()-180);
            csh = 2;
            // $('.main-body').css('margin-left'='270px');
        }else{
            $('.main-body').css({width: '100%'});
            if ($(".menu-left").css('left') == '0px') {
                $('.main-body').width($('.main-body').width()-180);
            } else{
                $('.main-body').width($('.main-body').width());
            };
        };
        $('.tab-body').height(mainBodyHeight-tabNavHeight-11);
        // $('.index-menu').width(($('.main-body').width()-104)/3);
        $('.tab-table1').height(mainBodyHeight-tabNavHeight-114-53);
        $('.tab-table2').height(mainBodyHeight-tabNavHeight-86-53);
    };
        // 初始
        var csh = 1;
        windowSize();
        //窗口变更自适应
        $(window).resize(function(){
            windowSize();
        });
    //tab切换
    $('.nav-tabs > li').click(function() {
        $('.nav-tabs > li').removeClass('active');
        $(this).addClass('active');
        var tabid = $(this).data("tabs")
        $('.tab-body').addClass('hidden');
        $("#"+tabid).removeClass('hidden');
    });
    // 二级菜单
//////////////////////////////////////////////////////////////////////////////////
///////
});/////
///////
// bootstrap模态框JS的使用方法
// .modal(options)

// 将页面中的某块内容作为模态框激活。接受可选参数 object。
// 复制
// $('#myModal').modal({
//   keyboard: false
// })
// .modal('toggle')

// 手动打开或关闭模态框。在模态框显示或隐藏之前返回到主调函数中（也就是，在触发 shown.bs.modal 或 hidden.bs.modal 事件之前）。
// 复制
// $('#myModal').modal('toggle')
// .modal('show')

// 手动打开模态框。在模态框显示之前返回到主调函数中 （也就是，在触发 shown.bs.modal 事件之前）。
// 复制
// $('#myModal').modal('show')
// .modal('hide')

// 手动隐藏模态框。在模态框隐藏之前返回到主调函数中 （也就是，在触发 hidden.bs.modal 事件之前）。
// 复制
// $('#myModal').modal('hide')
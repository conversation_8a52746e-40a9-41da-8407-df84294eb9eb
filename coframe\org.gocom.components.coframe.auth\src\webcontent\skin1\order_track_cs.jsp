﻿<%@page pageEncoding="UTF-8"%>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
	<%@include file="/coframe/tools/skins/common.jsp" %>

	<title>
		<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%>
	</title>
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/js/layui/css/layui.css" />
	<link rel="stylesheet" type="text/css" href="//at.alicdn.com/t/font_1031128_78ac0ihv97m.css"/>
	<style>
        .highlight{
            font-size: 20px;
            font-weight: 200;
            color: #ff0000;
        }
        .focus{
            font-weight: bold;
        }
        .text-red,.text-green,.text-yellow{
            font-weight: bold;
        }
        .layui-tab-title{
            height: 38px;
        }
		body {
			line-height: 24px;
			font: 14px Helvetica Neue,Helvetica,PingFang SC,Tahoma,Arial,sans-serif;
		}
		.text-red{
			color:#dd4b39 !important;
		}
		.text-green{
			color:#00a65a !important;
		}
		.text-default{
			color: #009688 !important;
		}
		.text-yellow{
			color:#f0ad4e !important;
		}
    </style>
	</head>

<body>
<div class="layui-fluid" style="margin-top: 5px;">
    <div class="layui-row layui-col-space15">
		<div class="layui-row">
			<div class="layui-col-xs9">
				<input type="text" id="orderdcd" placeholder="输入订单号/明细号..." autocomplete="off" class="layui-input layui-input-search">
			</div>
			<div class="layui-col-xs3">
				<button class="layui-btn layui-btn-normal" onclick="search()">搜索</button>
			</div>
			
		</div>
        <div class="layui-col-md12" id="Container" style="background-color: #ffffff">
		
        </div>
    </div>
</div>
<script src="<%=contextPath%>/coframe/auth/skin1/js/jquery.min.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/layui/layui.js"></script>
<script src="<%=contextPath%>/coframe/auth/skin1/js/order_track_cs.js"></script>
<script>
	layui.use('element',function(){
		var element = layui.element;
	});
</script>
</body>
	

	</html>
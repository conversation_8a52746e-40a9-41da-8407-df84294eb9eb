<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="removeDictTypeCascade.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>loopstart0</targetNode>
    </sourceConnections>
    <location x="105" y="121"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link7</targetConnections>
    <location x="913" y="121"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="107" y="157"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="915" y="157"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tLoopStart" description="" id="loopstart0" name="循环" displayName="循环" grouped="true" type="loopstart" matchedName="loopend0" loopType="iterate">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopstart0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="225" y="123"/>
    <size height="24" width="24"/>
    <nodeLabel>loopstart0label</nodeLabel>
    <process:iterate iterable="data" iterableElement="dicttype"/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopstart0label" name="label" nodeType="label">
    <location x="225" y="155"/>
    <figSize height="17" width="25"/>
    <node>loopstart0</node>
  </nodes>
  <nodes xsi:type="process:tLoopEnd" id="loopend0" name="循环结束" displayName="循环结束" grouped="true" type="loopend" matchedName="loopstart0">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopend0</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="696" y="123"/>
    <size height="24" width="24"/>
    <nodeLabel>loopend0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopend0label" name="label" nodeType="label">
    <location x="684" y="155"/>
    <figSize height="17" width="49"/>
    <node>loopend0</node>
  </nodes>
  <nodes xsi:type="model:GroupNode" id="group0" name="group" grouped="true" gstart="loopstart0" gend="loopend0">
    <location x="225" y="90"/>
    <size height="106" width="496"/>
    <chidren>loopend0</chidren>
    <chidren>loopstart0</chidren>
    <chidren>assign0</chidren>
    <chidren>invokePojo1</chidren>
    <chidren>invokePojo2</chidren>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" collapsed="false" grouped="true" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="330" y="121"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.dict.dict.EosDictEntry</process:from>
      <process:to type="query">criteria/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">dicttype/dicttypeid</process:from>
      <process:to type="query">criteria/_expr[1]/eosDictType.dicttypeid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="332" y="157"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="查询关联字典项" displayName="queryEntitiesByCriteriaEntity" grouped="true" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="450" y="121"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">dicts</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="419" y="157"/>
    <figSize height="17" width="85"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="删除字典项" displayName="deleteEntityBatch" grouped="true" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>loopend0</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="570" y="121"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.deleteEntityBatch</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObjects" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">dicts</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="551" y="157"/>
    <figSize height="17" width="61"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="删除字典类型" displayName="删除字典项" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="810" y="121"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.deleteEntityBatch</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObjects" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="785" y="157"/>
    <figSize height="17" width="73"/>
    <node>invokePojo0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-15 11:30:39" date="2013-12-15Z" description="" name="级联删除字典类型" version="6.3"/>
  <process:variables>
    <process:variable description="业务字典类型" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="dicttype"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
    <process:variable description="业务字典项" historyStateLocation="client" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="dicts"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="业务字典类型" isArray="true" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="data"/>
  </process:inputs>
  <process:outputs/>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

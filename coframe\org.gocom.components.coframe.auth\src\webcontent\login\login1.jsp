<%@page pageEncoding="UTF-8"%>
<%@page import="com.primeton.cap.AppUserManager"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge"><!-- 使IE浏览器使用 edge 内核渲染页面 -->
	<meta name="renderer" content="webkit"><!-- 使360等国产浏览器使用 webkit 内核渲染页面 -->
	<meta name="viewport" content="width=device-width, initial-scale=1"><!-- 响应式布局初始化 -->
	<!-- 以上标签必须在最顶端 -->
<html>
<head>
<title>SPD院内物流资源信息管理系统-登录</title>
<%
   String contextPath = request.getContextPath();
%>

 <script>
	//直接访问login.jsp之后的跳转
	//location.href="<%=contextPath %>" + "/core/login.jsp";
</script>
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/skin/css/bootstrap/css/bootstrap.min.css" />
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/skin/css/style.css" />
<link rel="stylesheet" type="text/css" href="<%=contextPath %>/coframe/skin/css/login.css" />
<script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
<script src="<%=contextPath%>/coframe/skin/js/jquery.min.js"></script>
<script src="<%=contextPath %>/coframe/skin/css/bootstrap/js/bootstrap.min.js"></script>
<!-- <link rel="stylesheet" type="text/css" href="../../skin/css/bootstrap/css/bootstrap.min.css" />
<link rel="stylesheet" type="text/css" href="../../skin/css/style.css" />
<link rel="stylesheet" type="text/css" href="../../skin/css/login.css" />
<script type="text/javascript" src="../../../nui/nui.js"></script>
<script src="../../skin/css/bootstrap/js/bootstrap.min.js"></script> -->
<script type="text/javascript">
    if(top!=window){
        top.location.href=top.location.href;
        top.location.reload;
    }
</script>
</head>
<%
	String original_url=null;
	Object objAttr=request.getAttribute("original_url");
	if(objAttr != null){
		original_url=(String)objAttr;
	}
 %>
<body class="login distouch" style="font-family:'Microsoft YaHei';">
    <div id="warpper" class="wrap">
        <div class="main mg-auto">
            <div class="login-title">
                <img src="<%=contextPath %>/coframe/skin/images/logo.png" class="mg-auto" />
                <h3>SPD院内物流资源信息管理系统</h3>
            </div>
            <div id="form1" class="login-box mg-auto">
                    <div class="input-group" id="input-text">
                        <em class="input-group-addon"><img src="<%=contextPath %>/coframe/skin/images/userName-icon.png" /></em>
                        <input class="form-control mini-textbox" id="userId" name="userId" placeholder="请输入用户名" onvalidation="onCheckUserId"/>
                    </div>
                    <div class="input-group" id="input-text">
                        <em class="input-group-addon"><img src="<%=contextPath %>/coframe/skin/images/password-icon.png" /></em>
                        <input name="password" class="form-control mini-password" id="userPassword" type="password" placeholder="请输入密码" vtype="minLength:6" minLengthErrorText="密码不能少于6个字符"onenter="keyboardLogin" onvalidation="onCheckPassword"/>
                    </div>
                    <input class="btn btn-info faded" type="button" onclick="login();" value="登 录" />
                <div style="width:1px;height:1px;display:none" >
                <iframe id="mainframe"
                src=""
                frameborder="0" name="main"
                style="display:none;" border="0"></iframe>
                </div>
            </div>
        </div>
        <div class="foot">
            <p>Copyright © 2016 - 2016  上海万序计算机科技有限公司 版权所有<span></span></p>
        </div>
    </div>
    <div id="window" class="mini-window" title="选择部门" style="width:400px;height:250px;display:none;"
        showCollapseButton="false" showFooter="true" allowResize="false" allowDrag="true">
        <div id="datagrid1" class="mini-datagrid" style="width:100%;height:100%;"
            allowResize="false" multiSelect="false"
            url="org.gocom.components.coframe.org.employee.queryEmpOfOrg.biz.ext"
            dataField="orgs" totalField="total" sizeList="[10,20,50,100]">
            <div property="columns">
                <div type="checkcolumn" ></div>
                <div field="orgcode" width="80" headerAlign="center" allowSort="true">部门编号</div>
                <div field="orgname" width="120" headerAlign="center" allowSort="true">部门名称</div>
            </div>
        </div>
        <div property="footer" style="text-align:right;padding:5px;padding-right:15px;">
            <input type='button' value='确定' onclick="confirm" style='vertical-align:middle;'/>
            <input type='button' value='取消' onclick="cancel" style='vertical-align:middle;'/>
        </div>
    </div>
    <!-- 弹窗-信息 -->
    <div class="modal fade" id="msgBox">
        <div class="modal-dialog" role="document">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header" >
                        <h4 class="modal-title" ><span class="glyphicon glyphicon-info-sign font-icon"></span>提示</h4>
                    </div>
                    <div class="modal-body">
                        <p class="msg-text">用户名密码不能为空</p>
                    </div>
                    <div class="modal-footer">
                        <a class="btn btn-default faded" data-dismiss="modal">确定</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
   <script type="text/javascript">
   window.onload = function () {
       $('#userId,#userPassword').attr('style', 'border-width:1px;');
       $('#userId > span > input').attr('placeholder', '请输入用户名');
       $('#userPassword > span > input').attr('placeholder', '请输入密码');
   };
     nui.parse();
     var form = new nui.Form("#form1");

     nui.get("userId").focus();

     function login(){


       form.validate();
       if(form.isValid()==false) return;
       var data = form.getData();
       var json = nui.encode(data);

       nui.ajax({
         url:"org.gocom.components.coframe.auth.LoginManager.login.biz.ext",
         type:'post',
         data:json,
         success:function(text){
            var o = nui.decode(text);
            if(o.exception==null){
	           var ret = o.retCode;
	           if(ret==1){
	           	 var a="http://*************:8080/login.aspx?username="+data.userId+"&password="+data.password;
	   			// document.getElementById('mainframe').src = a;
	   			/* OpenWindow=window.open(a, "newwin", "height=1, width=1,toolbar=no ,menubar=no");
	   			 OpenWindow.document.write("<TITLE>例子</TITLE>")
			　　OpenWindow.document.write("<BODY BGCOLOR=#ffffff>")
			　　OpenWindow.document.write("<h1>Hello!</h1>")
			　　OpenWindow.document.write("New window opened!")
			　　OpenWindow.document.write("</BODY>")
			　　OpenWindow.document.write("</HTML>")
			　　OpenWindow.document.close()
			*/   //OrgOfEmp(data.userId);
	             location.href="<%=request.getContextPath() %>/coframe/auth/login/redirect.jsp?original_url=<%=original_url %>";
	           }else if(ret==0){
	             msgBox("输入密码错误");
	           }else if(ret==-2){
	           	 msgBox("用户无权限登录，请联系系统管理员");
	           }else{
	             msgBox("用户名不存在");
	           }
            }else{
                 msgBox("登录系统出错");
            }
         }
       });
     }
// msgBox
    function msgBox(text) {
        $(".msg-text").html(text);
        $('#msgBox').modal('show');
    };
     function reset(){
       form.reset();
     }

     function onCheckUserId(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "用户名不能为空";
           $('#userId > span > input').attr('placeholder', '用户名不能为空');
           // e.isValid = false;
         }
       }
     }
     function onCheckPassword(e){
       if (e.isValid) {
         if(e.value==""){
           e.errorText = "密码不能为空";
           $('#userPassword > span > input').attr('placeholder', '密码不能为空');
           // e.isValid = false;
         }
       }
     }
     //获取键盘 Enter 键事件并响应登录
     function keyboardLogin(e){
       login();
     }

     function OrgOfEmp(userid){
     	mini.get("window").show();
		var data={
			"userid":userid
		};
        mini.get("datagrid1").load(data);
     }
     function confirm(){
     }
     function cancel(){
     	mini.get("window").hide();
     }
   </script>
 </body>
</html>

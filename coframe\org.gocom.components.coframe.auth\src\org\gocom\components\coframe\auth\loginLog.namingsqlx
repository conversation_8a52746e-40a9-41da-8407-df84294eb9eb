<?xml version="1.0" encoding="UTF-8"?>
<!-- author:Administrator -->
<sqlMap>
    <insert id="addLoginLog" parameterClass="java.util.HashMap">
    	insert into spd_login_log(id,userid,flag,ip_address) values(SPD_lOGIN_LOG_seq.Nextval,'$userid$','$flag$','$ipAddress$')
    </insert>
    <select id="queryMissInfoByuserid" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
    	select a.id,a.userid,a.miss_number,a.miss_time,a.miss_flag from spd_login_miss  a where a.userid=#userid#
    </select>
    <insert id="addLoginMiss" parameterClass="java.util.HashMap">
    insert into spd_login_miss(id,userid) values(SPD_lOGIN_LOG_seq.Nextval,'$userid$')</insert>
    <update id="updateLoginMiss" parameterClass="java.util.HashMap">
    	update spd_login_miss a set a.miss_number= '$missNum$',a.miss_time='$missTime$',a.miss_flag='$missFlag$' where  a.userid = #userid#
    </update>
    <select id="queryPasswordInvaldate" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
    select invaldate from cap_user  a where a.user_id=#userid#
    </select>
</sqlMap>
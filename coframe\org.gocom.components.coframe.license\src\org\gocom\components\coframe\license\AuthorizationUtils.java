package org.gocom.components.coframe.license;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Properties;

import org.apache.commons.io.IOUtils;

import com.eos.runtime.core.ApplicationContext;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;

/**
 * 注册工具类
 * 
 * <AUTHOR> QQ:**********
 * @create 2022-01-09 18:18
 */
@Bizlet
public class AuthorizationUtils {

	static String versionFilePath = ApplicationContext.getInstance().getApplicationConfigPath() + "/version.properties";

	public static String licFilePath = ApplicationContext.getInstance().getApplicationConfigPath() + "/vanxsoft.lic";

	private static Long expireTime = null;
	
	private static Logger logger = TraceLoggerFactory.getLogger(AuthorizationUtils.class);

	public static String getLicenseInfo() {
		
		String licenseInfo = null;
		try {
			FileInputStream fileInputStream = new FileInputStream(licFilePath);
			if (fileInputStream != null) {
				licenseInfo = IOUtils.toString(fileInputStream, StandardCharsets.UTF_8);
				fileInputStream.close();
			}
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		
		return licenseInfo;
	}

	public synchronized static Properties readVersionInfo() {
		
		Properties props = new Properties();
		BufferedReader bf = null;
		try {
			bf = new BufferedReader(new InputStreamReader(new FileInputStream(versionFilePath), "UTF-8"));
			props.load(bf);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (bf != null) {
				try {
					bf.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		
		return props;
	}

	public static String getVersionInfo(String key) {
		Properties props = readVersionInfo();
		return props.getProperty(key) != null ? props.getProperty(key).trim() : props.getProperty(key);
	}

	// 添加属性到版本信息文件
	public synchronized static boolean setVersionInfo(String key, String value)
			throws UnsupportedEncodingException {
		
		Properties props = readVersionInfo();
		value = java.net.URLDecoder.decode(value, "UTF-8");
		String filePath = ApplicationContext.getInstance().getApplicationConfigPath() + "/version.properties";
		try {

			File file = new File(filePath);
			if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
			}
			if (file.exists()) {
				file.delete();
			}
			FileOutputStream oFile = new FileOutputStream(file, false);
			props.setProperty(key, value);
			// 保存属性到properties文件
			props.store(oFile, null);
			oFile.close();
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
		return true;
	}

	// 删除属性到版本信息文件
	public synchronized static boolean deleteVersionInfo(String key,
			String value) {
		
		Properties props = readVersionInfo();
		String filePath = ApplicationContext.getInstance().getApplicationConfigPath() + "/version.properties";
		try {
			File file = new File(filePath);
			if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
			}
			FileOutputStream oFile = new FileOutputStream(file, false);
			props.remove(key);
			// 保存属性到properties文件
			props.store(oFile, null);
			oFile.close();
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		
		return true;
	}
	
	private static List<String> result = new ArrayList<>();
	public static String mac = null;
	
	static {
        Properties props = System.getProperties();
        result.add(props.getProperty("java.version"));
        result.add(props.getProperty("java.vm.version"));
        result.add(props.getProperty("os.version"));
        result.add(props.getProperty("user.name"));
        result.add(props.getProperty("java.home"));
        mac = getMac();
        result.add(mac);
    }

	/**
	 * 获取机器码：Java 运行时环境版本 + Java 虚拟机实现版本 + 操作系统的版本 + 用户的账户名称 + Java 安装目录
	 * 
	 * @return
	 * @throws NoSuchAlgorithmException
	 */
	public static String getMachineCode() {
		byte[] code = md5Digest(result.toString().getBytes()).getBytes();
		return Base64.getEncoder().encodeToString(code);
	}

	/**
	 * 获取本地mac地址
	 * 
	 * @return
	 */
	public static String getMac() {
		if (mac != null) {
			 return mac;
		}
		String macAddress = MacAddressUtil.getFirstActiveMacAddress();
		logger.info("获取到的mac地址: " + macAddress);
		return macAddress;
	}

	public static String transBytesToStr(byte[] bytes) {
		StringBuffer buffer = new StringBuffer();
		for (int i = 0; i < bytes.length; i++) {
			if (i != 0) {
				buffer.append("-");
			}
			int intMac = bytes[i] & 0xff;
			StringBuffer str = new StringBuffer(Integer.toHexString(intMac));
			if (str.length() < 2) {
				for (int j = 0; j < 2 - str.length(); j++) {
					str.insert(0, "0");
				}
			}
			buffer.append(str);
		}
		return buffer.toString().toUpperCase();
	}

	private static String md5Digest(byte[] bytes) {
		MessageDigest md5;
		try {
			md5 = MessageDigest.getInstance("MD5");
			byte[] md5Bytes = md5.digest(bytes);
			StringBuffer hexValue = new StringBuffer();
			for (int i = 0; i < md5Bytes.length; i++) {
				int val = (md5Bytes[i]) & 0xff;
				if (val < 16)
					hexValue.append("0");
				hexValue.append(Integer.toHexString(val));
			}
			return hexValue.toString();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static Long getExpireTime() {
		return expireTime;
	}

	public static void setExpireTime(Long expireTime) {
		AuthorizationUtils.expireTime = expireTime;
	}
}
alter table app_funcgroup drop constraint FK_F_FUNG_FUNG;
alter table app_funcgroup drop constraint FK_F_APP_FUNCTION;
alter table app_funcresource drop constraint FK_F_FUN_RES;
alter table app_function drop constraint FK_F_FUNGROUP_FUN;
alter table app_menu drop constraint FK_F_MENU_MENU;
alter table cap_partyauth drop constraint CapPartyauth_CapRole;
alter table org_empgroup drop constraint OrgEmpgroup_OrgEmployee;
alter table org_emporg drop constraint OrgEmporg_OrgEmployee;
alter table org_empposition drop constraint OrgEmpposition_OrgEmployee;
alter table org_empposition drop constraint OrgEmpposition_OrgPosition;
alter table org_group drop constraint FK_F_GROUP_GROUP;
alter table org_groupposi drop constraint OrgGroupposi_OrgPosition;
alter table org_groupposi drop constraint FK_F_GROUP_POS;
alter table org_organization drop constraint OrgOrganization_OrgOrganization;
alter table org_position drop constraint OrgPosition_OrgDuty;
alter table org_position drop constraint OrgPosition_OrgOrganization;
alter table org_position drop constraint OrgPosition_OrgPosition;
drop table cap_partyauth;
drop table CAP_RESAUTH;
drop table CAP_ROLE;
drop table COMP_IP_ACCESS_RULES;
drop table COMP_WIN7_AUTO_START;
drop table COMP_WIN7_CONFIG;
drop table COMP_WIN7_CUSTOM_PICTURES;
drop table COMP_WIN7_ICONS;
drop table app_funcresource;
drop table app_function;
drop table app_funcgroup;
drop table app_application;
drop table app_menu;
drop table cap_ssouser;
drop table cap_user;
drop table org_emporg;
drop table org_empposition;
drop table org_empgroup;
drop table org_employee;
drop table org_group;
drop table org_groupposi;
drop table org_position;
drop table org_duty;
drop table org_organization;
drop table org_recent_visit;
create table CAP_RESAUTH (PARTY_ID varchar(64) not null, PARTY_TYPE varchar(64) not null, RES_ID varchar(255) not null, RES_TYPE varchar(64) not null, TENANT_ID varchar(64), RES_STATE lvarchar(512) not null, PARTY_SCOPE varchar(1) default '0', CREATEUSER varchar(64), CREATETIME datetime year to fraction(5), primary key (PARTY_ID, PARTY_TYPE, RES_ID, RES_TYPE));
create table CAP_ROLE (ROLE_ID varchar(64) not null, TENANT_ID varchar(64) not null, ROLE_CODE varchar(64) not null, ROLE_NAME varchar(64), ROLE_DESC varchar(255), CREATEUSER varchar(64), CREATETIME datetime year to fraction(5), primary key (ROLE_ID));
create table COMP_IP_ACCESS_RULES (RULES_ID varchar(255) not null, START_IP varchar(255), END_IP varchar(255), RULES_TYPE varchar(255), REMARK varchar(255), MAKERS_ID varchar(255), ADD_DATE varchar(255), ENABLED varchar(255), primary key (RULES_ID));
create table COMP_WIN7_AUTO_START (START_ID varchar(255) not null, MENU_ID varchar(255), START_DESC varchar(255), USER_ID varchar(255), primary key (START_ID));
create table COMP_WIN7_CONFIG (CONFIG_ID varchar(255) not null, BG_PICTURE_PATH varchar(255), USER_ID varchar(255), CONFIG_DATA clob, OPEN_TYPE varchar(255), DEFAULT_MAX smallint, DEFAULT_WIDTH integer, DEFAULT_HEIGHT integer, DESK_STYLE varchar(255), EXT1 varchar(255), EXT2 varchar(255), EXT3 varchar(255), primary key (CONFIG_ID));
create table COMP_WIN7_CUSTOM_PICTURES (CUSTOM_ID varchar(255) not null, FILE_NAME varchar(255), USER_ID varchar(255), UPLOAD_TIME varchar(255), primary key (CUSTOM_ID));
create table COMP_WIN7_ICONS (ICON_ID varchar(255) not null, ICON_NAME varchar(255), ICON_TEXT varchar(255), ICON_PATH varchar(255), ICON_TITLE varchar(255), MENU_ID varchar(255), ICON_INDEX varchar(255), ICON_DESC varchar(255), USER_ID varchar(255), primary key (ICON_ID));
create table app_application (APPID decimal not null, APPCODE varchar(32), APPNAME varchar(50), APPTYPE varchar(255), ISOPEN varchar(1), OPENDATE date, URL lvarchar(256), APPDESC lvarchar(512), MAINTENANCE decimal, MANAROLE varchar(64), DEMO lvarchar(512), INIWP varchar(1), INTASKCENTER varchar(1), IPADDR varchar(50), IPPORT varchar(10), APP_ID varchar(64), TENANT_ID varchar(64) not null, protocol_type varchar(64), primary key (APPID));
create table app_funcgroup (FUNCGROUPID decimal not null, FUNCGROUPNAME varchar(40), GROUPLEVEL integer, FUNCGROUPSEQ lvarchar(256), ISLEAF varchar(1), SUBCOUNT decimal, APP_ID varchar(64), TENANT_ID varchar(64) not null, PARENTGROUP decimal, APPID decimal not null, primary key (FUNCGROUPID));
create table app_funcresource (RESID decimal not null, RESTYPE varchar(255), RESPATH lvarchar(256), COMPACKNAME varchar(40), RESNAME varchar(40), APP_ID varchar(64), TENANT_ID varchar(64) not null, FUNCCODE varchar(255), primary key (RESID));
create table app_function (FUNCCODE varchar(255) not null, FUNCNAME varchar(128) not null, FUNCDESC lvarchar(512), FUNCACTION lvarchar(256), PARAINFO lvarchar(256), ISCHECK varchar(1), FUNCTYPE varchar(255) default '1', ISMENU varchar(1), APP_ID varchar(64), TENANT_ID varchar(64) not null, FUNCGROUPID decimal, primary key (FUNCCODE));
create table app_menu (MENUID varchar(40) not null, MENUNAME varchar(40) not null, MENULABEL varchar(40) not null, MENUCODE varchar(40), ISLEAF varchar(1), PARAMETER lvarchar(256), UIENTRY lvarchar(256), MENULEVEL smallint, ROOTID varchar(40), DISPLAYORDER smallint, IMAGEPATH varchar(100), EXPANDPATH varchar(100), MENUSEQ lvarchar(256), OPENMODE varchar(255), SUBCOUNT decimal, APPID decimal, FUNCCODE varchar(255), APP_ID varchar(64), TENANT_ID varchar(64) not null, PARENTSID varchar(40), primary key (MENUID));
create table cap_partyauth (ROLE_TYPE varchar(64) not null, PARTY_ID varchar(64) not null, PARTY_TYPE varchar(64) not null, ROLE_ID varchar(64) not null, TENANT_ID varchar(64) not null, CREATEUSER varchar(64), CREATETIME datetime year to fraction(5) not null, primary key (ROLE_TYPE, PARTY_ID, PARTY_TYPE, ROLE_ID));
create table cap_ssouser (OPERATOR_ID varchar(64) not null, TENANT_ID varchar(64), USER_ID varchar(64) not null, PASSWORD varchar(100), USER_NAME varchar(64), EMAIL varchar(128), STATUS varchar(16), UNLOCKTIME datetime year to fraction(5), LASTLOGIN datetime year to fraction(5) not null, ERRCOUNT decimal, MACCODE varchar(255), IPADDRESS varchar(255), CREATEUSER varchar(64), CREATETIME datetime year to fraction(5) not null, primary key (OPERATOR_ID));
create table cap_user (OPERATOR_ID decimal not null, TENANT_ID varchar(64) not null, USER_ID varchar(64) not null, PASSWORD varchar(100), INVALDATE date, USER_NAME varchar(64), AUTHMODE varchar(255), STATUS varchar(16), UNLOCKTIME datetime year to fraction(5) not null, MENUTYPE varchar(255), LASTLOGIN datetime year to fraction(5) not null, ERRCOUNT decimal, STARTDATE date, ENDDATE date, VALIDTIME varchar(255), MACCODE varchar(128), IPADDRESS varchar(128), EMAIL varchar(255), CREATEUSER varchar(64), CREATETIME datetime year to fraction(5) not null, primary key (OPERATOR_ID));
create table org_duty (DUTYID decimal not null, DUTYCODE varchar(20), DUTYNAME varchar(30), PARENTDUTY decimal, DUTYLEVEL integer, DUTYSEQ lvarchar(256), DUTYTYPE varchar(255), ISLEAF varchar(10), SUBCOUNT decimal, REMARK lvarchar(256), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (DUTYID));
create table org_empgroup (GROUPID decimal not null, EMPID decimal not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (GROUPID, EMPID));
create table org_employee (EMPID decimal not null, EMPCODE varchar(30), OPERATORID decimal, USERID varchar(30), EMPNAME varchar(50), REALNAME varchar(50), GENDER varchar(255), BIRTHDATE date, POSITION decimal, EMPSTATUS varchar(255), CARDTYPE varchar(255), CARDNO varchar(20), INDATE date, OUTDATE date, OTEL varchar(12), OADDRESS varchar(255), OZIPCODE varchar(10), OEMAIL varchar(128), FAXNO varchar(14), MOBILENO varchar(14), QQ varchar(16), HTEL varchar(12), HADDRESS varchar(128), HZIPCODE varchar(10), PEMAIL varchar(128), PARTY varchar(255), DEGREE varchar(255), MAJOR decimal, SPECIALTY lvarchar(1024), WORKEXP lvarchar(512), REGDATE date, CREATETIME date not null, LASTMODYTIME date not null, ORGIDLIST varchar(128), ORGID decimal, REMARK lvarchar(512), TENANT_ID varchar(64) not null, APP_ID varchar(64), WEIBO varchar(255), primary key (EMPID));
create table org_emporg (ORGID decimal not null, EMPID decimal not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (ORGID, EMPID));
create table org_empposition (POSITIONID decimal not null, EMPID decimal not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (POSITIONID, EMPID));
create table org_group (GROUPID decimal not null, ORGID decimal, GROUPLEVEL integer, GROUPNAME varchar(50), GROUPDESC lvarchar(512), GROUPTYPE varchar(255), GROUPSEQ lvarchar(256), STARTDATE date, ENDDATE date, GROUPSTATUS varchar(255), MANAGER varchar(30), CREATETIME datetime year to fraction(5) not null, LASTUPDATE date, UPDATOR decimal, ISLEAF varchar(1), SUBCOUNT decimal, TENANT_ID varchar(64) not null, APP_ID varchar(64), PARENTGROUPID decimal, primary key (GROUPID));
create table org_groupposi (GROUPID decimal not null, POSITIONID decimal not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (GROUPID, POSITIONID));
create table org_organization (ORGID decimal not null, ORGCODE varchar(32) not null, ORGNAME varchar(64), ORGLEVEL decimal default 1, ORGDEGREE varchar(255), ORGSEQ lvarchar(512), ORGTYPE varchar(12), ORGADDR lvarchar(256), ZIPCODE varchar(10), MANAPOSITION decimal, MANAGERID decimal, ORGMANAGER varchar(128), LINKMAN varchar(30), LINKTEL varchar(20), EMAIL varchar(128), WEBURL lvarchar(512), STARTDATE date, ENDDATE date, STATUS varchar(255), AREA varchar(30), CREATETIME datetime year to fraction(5) not null, LASTUPDATE datetime year to fraction(5) not null, UPDATOR decimal, SORTNO integer, ISLEAF varchar(1), SUBCOUNT decimal, REMARK lvarchar(512), TENANT_ID varchar(64) not null, APP_ID varchar(64), PARENTORGID decimal, primary key (ORGID));
create table org_position (POSITIONID decimal not null, POSICODE varchar(20), POSINAME varchar(128) not null, POSILEVEL decimal, POSITIONSEQ lvarchar(512) not null, POSITYPE varchar(255), CREATETIME date not null, LASTUPDATE date not null, UPDATOR decimal, STARTDATE date, ENDDATE date, STATUS varchar(255), ISLEAF varchar(1), SUBCOUNT decimal, TENANT_ID varchar(64) not null, APP_ID varchar(64), DUTYID decimal, MANAPOSI decimal, ORGID decimal, primary key (POSITIONID));
create table org_recent_visit (id varchar(32) not null, target_id varchar(32) not null, UserID varchar(32) not null, Frequency integer default 1 not null, LastTime datetime year to fraction(5) not null, target_type varchar(32) not null, primary key (id));

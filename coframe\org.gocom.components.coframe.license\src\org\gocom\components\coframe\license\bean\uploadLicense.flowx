<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="uploadLicense" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******" state="stateless">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link0" name="link0" lineType="reference" isDefault="true" type="action" actionName="action0" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:validateRules errorPage="" onError="default"/>
      <process:inputParameters>
        <process:parameter anyType="com.primeton.ext.access.http.IUploadFile" description="" historyStateLocation="client" isArray="false" name="file"/>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="projectName" primitiveType="String"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="成功" displayName="结束" type="end" contextPath="" method="forward" uri="/license/uploadSuccess.jsp" variableUri="false">
    <targetConnections>link1</targetConnections>
    <location x="640" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="642" y="96"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="208" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="210" y="96"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="保存lic文件" displayName="保存Licnese文件" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="356" y="60"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="static" synchronization="true">
      <process:partner type="literal">org.gocom.components.coframe.license.AuthorizationService.saveLicenseFile</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="uploadFile" type="query" value="com.primeton.ext.access.http.IUploadFile" valueType="Java" pattern="reference">file</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">filePath</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="337" y="96"/>
    <figSize height="17" width="61"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="验证lic文件" displayName="验证Licnese文件" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="link2" displayName="连接线" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">flag</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="link3" displayName="连接线" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">flag</process:leftOperand>
          <process:rightOperand type="literal">expire</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="498" y="60"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="static" synchronization="true">
      <process:partner type="literal">org.gocom.components.coframe.license.AuthorizationService.activateLicFile</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="filePath" type="query" value="java.lang.String" valueType="Java" pattern="reference">filePath</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">flag</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="479" y="96"/>
    <figSize height="17" width="61"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end1" name="失败" displayName="结束" type="end" contextPath="" method="forward" uri="/license/uploadError.jsp">
    <targetConnections>link2</targetConnections>
    <location x="640" y="121"/>
    <size height="28" width="28"/>
    <nodeLabel>end1label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end1label" name="label" nodeType="label">
    <location x="642" y="157"/>
    <figSize height="17" width="25"/>
    <node>end1</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end2" name="已过期" displayName="结束" collapsed="false" type="end" contextPath="" method="forward" uri="/license/uploadExpire.jsp">
    <targetConnections>link3</targetConnections>
    <location x="640" y="181"/>
    <size height="28" width="28"/>
    <nodeLabel>end2label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end2label" name="label" nodeType="label">
    <location x="636" y="217"/>
    <figSize height="17" width="37"/>
    <node>end2</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2016-10-27 16:51:26" date="2016-10-27Z" description="" name="uplodFileflow" version="*******"/>
  <process:variables/>
  <process:inputParameters/>
</process:tPageFlow>

<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:sca="http://www.springframework.org/schema/sca" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd   http://www.springframework.org/schema/sca http://www.osoa.org/xmlns/sca/1.0/spring-sca.xsd">

    <bean class="org.gocom.components.coframe.dict.impl.DictTypeService" id="DictTypeServiceBean">
        <property name="dataSource" ref="DefaultDataSource"/>
    </bean>

    <bean class="org.gocom.components.coframe.dict.impl.DictService" id="DictServiceBean">
		<property name="dataSource" ref="DefaultDataSource"/>
    </bean>
    
</beans>

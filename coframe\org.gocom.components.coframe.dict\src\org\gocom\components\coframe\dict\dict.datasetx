<?xml version="1.0" encoding="UTF-8"?>
<model:DataSetDiagram xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://www.primeton.com/datamodel" name="dict" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" displayName="dict" author="陈鹏">
  <nodes xsi:type="model:PEntityNode" id="node0" name="EosDictEntry" nodeType="table" displayName="EosDictEntry" author="陈鹏" instanceClass="org.gocom.components.coframe.dict.impl.EosDictEntryImpl" table="eos_dict_entry">
    <sourceConnections xsi:type="model:AssociationElement" id="association0" name="eos_dict_entry_ibfk_1" lineType="reference" sourceNode="node0" targetNode="node2" displayName="eos_dict_entry_eos_dict_type" associationType="2" srcCascadeType="11" srcLazyLoad="false" SrcRoleName="EosDictEntry" TgtRoleName="EosDictType" mockSrcNum="n" mockTgtNum="1">
      <fKAssociationField srcProperty="property0" tgtProperty="property14"/>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="228" width="196"/>
    <figSize height="20" width="196"/>
    <entPojoProperty id="assproperty0" name="eosDictType" propertyType="EntityProperty" association="association0" refId="org.gocom.components.coframe.dict.dict.EosDictType" isAssociationSource="true">
      <entViewField dict="false" dictTypeId="" entityField="eosDictType.rank" id="eosDictType.rank" label="rank">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="eosDictType.parentid" id="eosDictType.parentid" label="parentid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="eosDictType.dicttypeid" id="eosDictType.dicttypeid" label="dicttypeid">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="eosDictType.dicttypename" id="eosDictType.dicttypename" label="dicttypename">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
      <entViewField dict="false" dictTypeId="" entityField="eosDictType.seqno" id="eosDictType.seqno" label="seqno">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </entViewField>
    </entPojoProperty>
    <columnProperty id="property0" name="dicttypeid" displayName="dicttypeid" association="association0" columnName="DICTTYPEID" FK="true" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dicttypeid" entityField="dicttypeid" id="dicttypeid" label="dicttypeid" name="dicttypeid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypeid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property1" name="dictid" displayName="dictid" columnName="DICTID" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dictid" entityField="dictid" id="dictid" label="dictid" name="dictid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dictid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property2" name="dictname" displayName="dictname" columnName="DICTNAME" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dictname" entityField="dictname" id="dictname" label="dictname" name="dictname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dictname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property3" name="status" displayName="status" columnName="STATUS" nullAble="true" PK="false" columnType="INT" eosDataType="Int" length="11" dasType="int" overWriteDefaultColumnType="false" showType="Int" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="status" entityField="status" id="status" label="status" name="status" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="status" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property4" name="sortno" displayName="sortno" columnName="SORTNO" nullAble="true" PK="false" columnType="INT" eosDataType="Int" length="11" dasType="int" overWriteDefaultColumnType="false" showType="Int" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="sortno" entityField="sortno" id="sortno" label="sortno" name="sortno" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="sortno" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property5" name="rank" displayName="rank" columnName="RANK" nullAble="true" PK="false" columnType="INT" eosDataType="Int" length="11" dasType="int" overWriteDefaultColumnType="false" showType="Int" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="rank" entityField="rank" id="rank" label="rank" name="rank" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="rank" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property6" name="parentid" displayName="parentid" columnName="PARENTID" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="parentid" entityField="parentid" id="parentid" label="parentid" name="parentid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="parentid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property7" name="seqno" displayName="seqno" columnName="SEQNO" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="seqno" entityField="seqno" id="seqno" label="seqno" name="seqno" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="seqno" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property8" name="filter1" displayName="filter1" columnName="FILTER1" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="filter1" entityField="filter1" id="filter1" label="filter1" name="filter1" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="filter1" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property10" name="filter2" displayName="filter2" columnName="FILTER2" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="filter2" entityField="filter2" id="filter2" label="filter2" name="filter2" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="filter2" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node1" name="EosDictEntryI18n" nodeType="table" displayName="EosDictEntryI18n" author="陈鹏" instanceClass="org.gocom.components.coframe.dict.impl.EosDictEntryI18nImpl" table="eos_dict_entry_i18n">
    <location x="60" y="330"/>
    <size height="102" width="196"/>
    <figSize height="20" width="196"/>
    <columnProperty id="property9" name="dicttypeid" displayName="dicttypeid" columnName="DICTTYPEID" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dicttypeid" entityField="dicttypeid" id="dicttypeid" label="dicttypeid" name="dicttypeid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypeid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property11" name="dictid" displayName="dictid" columnName="DICTID" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dictid" entityField="dictid" id="dictid" label="dictid" name="dictid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dictid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property12" name="dictname" displayName="dictname" columnName="DICTNAME" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dictname" entityField="dictname" id="dictname" label="dictname" name="dictname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dictname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property13" name="locale" displayName="locale" columnName="LOCALE" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="10" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="locale" entityField="locale" id="locale" label="locale" name="locale" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="locale" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node2" name="EosDictType" nodeType="table" targetConnections="association0" displayName="EosDictType" author="陈鹏" instanceClass="org.gocom.components.coframe.dict.impl.EosDictTypeImpl" table="eos_dict_type" genStrategy="assigned">
    <location x="360" y="112"/>
    <size height="120" width="166"/>
    <figSize height="20" width="166"/>
    <columnProperty id="property14" name="dicttypeid" displayName="dicttypeid" columnName="DICTTYPEID" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dicttypeid" entityField="dicttypeid" id="dicttypeid" label="dicttypeid" name="dicttypeid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypeid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property15" name="dicttypename" displayName="dicttypename" columnName="DICTTYPENAME" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dicttypename" entityField="dicttypename" id="dicttypename" label="dicttypename" name="dicttypename" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypename" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property16" name="rank" displayName="rank" columnName="RANK" nullAble="true" PK="false" columnType="INT" eosDataType="Int" length="11" dasType="int" overWriteDefaultColumnType="false" showType="Int" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="rank" entityField="rank" id="rank" label="rank" name="rank" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="rank" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property17" name="parentid" displayName="parentid" columnName="PARENTID" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="parentid" entityField="parentid" id="parentid" label="parentid" name="parentid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="parentid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property18" name="seqno" displayName="seqno" columnName="SEQNO" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="seqno" entityField="seqno" id="seqno" label="seqno" name="seqno" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="seqno" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:PEntityNode" id="node3" name="EosDictTypeI18n" nodeType="table" displayName="EosDictTypeI18n" author="陈鹏" instanceClass="org.gocom.components.coframe.dict.impl.EosDictTypeI18nImpl" table="eos_dict_type_i18n">
    <location x="360" y="330"/>
    <size height="84" width="166"/>
    <figSize height="20" width="166"/>
    <columnProperty id="property19" name="dicttypeid" displayName="dicttypeid" columnName="DICTTYPEID" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="128" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dicttypeid" entityField="dicttypeid" id="dicttypeid" label="dicttypeid" name="dicttypeid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypeid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property20" name="dicttypename" displayName="dicttypename" columnName="DICTTYPENAME" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="dicttypename" entityField="dicttypename" id="dicttypename" label="dicttypename" name="dicttypename" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypename" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property21" name="locale" displayName="locale" columnName="LOCALE" nullAble="false" PK="true" columnType="VARCHAR" eosDataType="String" length="10" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" dictTypeId="" displayName="locale" entityField="locale" id="locale" label="locale" name="locale" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="value"/>
          <attr name="maxLength"/>
          <attr name="srcFormatPattern"/>
          <attr name="formatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="locale" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <nodes xsi:type="model:SPEntityNode" id="Entity200" name="DictExport" collapsed="false" nodeType="table" displayName="DictExport" author="" open="true" entType="SPEntity" allUseViewModel="true" instanceClass="" table="">
    <location x="600" y="60"/>
    <size height="286" width="181"/>
    <figSize height="20" width="181"/>
    <columnProperty id="property22" name="dicttypeid" displayName="dicttypeid" columnName="DICTTYPEID" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="128" queryId="true" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="dicttypeid" entityField="dicttypeid" id="dicttypeid" label="dicttypeid" name="dicttypeid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypeid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property23" name="dicttypename" displayName="dicttypename" columnName="DICTTYPENAME" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="dicttypename" entityField="dicttypename" id="dicttypename" label="dicttypename" name="dicttypename" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dicttypename" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property24" name="typeParentid" displayName="typeParentid" columnName="TYPE_PARENTID" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="typeParentid" entityField="typeParentid" id="typeParentid" label="typeParentid" name="typeParentid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="typeParentid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property25" name="typeRank" displayName="typeRank" columnName="TYPE_RANK" nullAble="true" PK="false" columnType="INTEGER" eosDataType="Int" length="11" dasType="int" showType="Int">
      <viewField dict="false" dictTypeId="" displayName="typeRank" entityField="typeRank" id="typeRank" label="typeRank" name="typeRank" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="typeRank" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property26" name="typeSeqno" displayName="typeSeqno" columnName="TYPE_SEQNO" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="typeSeqno" entityField="typeSeqno" id="typeSeqno" label="typeSeqno" name="typeSeqno" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="typeSeqno" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property27" name="dictid" displayName="dictid" columnName="DICTID" nullAble="false" PK="false" columnType="VARCHAR" eosDataType="String" length="128" queryId="true" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="dictid" entityField="dictid" id="dictid" label="dictid" name="dictid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dictid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property28" name="dictname" displayName="dictname" columnName="DICTNAME" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="dictname" entityField="dictname" id="dictname" label="dictname" name="dictname" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="dictname" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property29" name="entryParentid" displayName="entryParentid" columnName="ENTRY_PARENTID" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="entryParentid" entityField="entryParentid" id="entryParentid" label="entryParentid" name="entryParentid" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="entryParentid" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property30" name="status" displayName="status" columnName="STATUS" nullAble="true" PK="false" columnType="INTEGER" eosDataType="Int" length="11" dasType="int" showType="Int">
      <viewField dict="false" dictTypeId="" displayName="status" entityField="status" id="status" label="status" name="status" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="status" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property31" name="sortno" displayName="sortno" columnName="SORTNO" nullAble="true" PK="false" columnType="INTEGER" eosDataType="Int" length="11" dasType="int" showType="Int">
      <viewField dict="false" dictTypeId="" displayName="sortno" entityField="sortno" id="sortno" label="sortno" name="sortno" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="sortno" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property32" name="entryRank" displayName="entryRank" columnName="ENTRY_RANK" nullAble="true" PK="false" columnType="INTEGER" eosDataType="Int" length="11" dasType="int" showType="Int">
      <viewField dict="false" dictTypeId="" displayName="entryRank" entityField="entryRank" id="entryRank" label="entryRank" name="entryRank" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="entryRank" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property33" name="entrySeqno" displayName="entrySeqno" columnName="ENTRY_SEQNO" nullAble="true" PK="false" columnType="VARCHAR" eosDataType="String" length="255" dasType="string" showType="String">
      <viewField dict="false" dictTypeId="" displayName="entrySeqno" entityField="entrySeqno" id="entrySeqno" label="entrySeqno" name="entrySeqno" userDefined="true">
        <fieldView showAllAttr="false">
          <type name="write"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr name="maxLength"/>
          <attr name="value"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
        </fieldView>
        <fieldEdit name="entrySeqno" showAllAttr="false">
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="size"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <spEntitySqlView product="default" view="" useSql="true" schema="" needSave="true">
      <sql>SELECT B.DICTTYPEID AS DICTTYPEID,&#xD;
       B.DICTTYPENAME AS DICTTYPENAME,&#xD;
       B.PARENTID AS TYPE_PARENTID,&#xD;
       B.RANK AS TYPE_RANK,&#xD;
       B.SEQNO AS TYPE_SEQNO,&#xD;
       A.DICTID AS DICTID,&#xD;
       A.DICTNAME AS DICTNAME,&#xD;
       A.PARENTID AS ENTRY_PARENTID,&#xD;
       A.STATUS AS STATUS,&#xD;
       A.SORTNO AS SORTNO,&#xD;
       A.RANK AS ENTRY_RANK,&#xD;
       A.SEQNO AS ENTRY_SEQNO&#xD;
  FROM EOS_DICT_ENTRY A, EOS_DICT_TYPE B&#xD;
 WHERE A.DICTTYPEID = B.DICTTYPEID</sql>
    </spEntitySqlView>
    <spEntitySqlView product="DB2 UDB" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Informix" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="MySql" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Oracle" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="SQL Server" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
    <spEntitySqlView product="Sybase" view="" useSql="true" schema="">
      <sql></sql>
    </spEntitySqlView>
  </nodes>
  <topRuler/>
  <leftRuler/>
</model:DataSetDiagram>

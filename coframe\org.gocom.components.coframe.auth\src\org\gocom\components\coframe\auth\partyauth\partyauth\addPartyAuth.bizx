<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="addPartyAuth.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link1</targetConnections>
    <location x="400" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Boolean" name="result" type="query" valueType="Primitive">result</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="402" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="addOrUpdatePartyAuthBatch" displayName="addOrUpdatePartyAuthBatch" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="256" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.partyauth.PartyAuthService.addOrUpdatePartyAuthBatch</process:partner>
      <process:instance instanceName="PartyAuthServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="roleList" type="query" value="com.primeton.cap.party.Party[]" valueType="Java" pattern="reference">roleList</process:inputVariable>
      <process:inputVariable id="1" name="party" type="query" value="com.primeton.cap.party.Party" valueType="Java" pattern="reference">party</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="boolean" valueType="Java">result</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="186" y="186"/>
    <figSize height="17" width="169"/>
    <node>invokeSpring0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="yangyong" createTime="2013-03-12 18:01:50" date="2013-03-12Z" description="" name="addPartyAuth" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input anyType="com.primeton.cap.party.Party" description="" isArray="true" name="roleList"/>
    <process:input anyType="com.primeton.cap.party.Party" description="" isArray="false" name="party"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="result" primitiveType="Boolean"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

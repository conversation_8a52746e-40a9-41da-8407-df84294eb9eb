/*platform init sql*/
INSERT INTO app_funcgroup (FUNCGR<PERSON><PERSON><PERSON>,FUNCGROUPNAME,GROUPLE<PERSON>L,FUNCGR<PERSON>UPSEQ,ISLEAF,<PERSON><PERSON><PERSON>OUNT,APP_ID,TENANT_ID,PARENTGROUP,APPID) VALUES (4,'流程配置',1,'.4.','n',0,NULL,'default',NULL,1);
INSERT INTO app_funcgroup (FUNCGROUPID,FUNCGROUPNAME,GRO<PERSON><PERSON>VEL,FUNCGROUPSEQ,ISLEAF,SUBCOUNT,APP_ID,TENANT_ID,PARENTGROUP,APPID) VALUES (7,'工作流程',1,'.7.','n',0,NULL,'default',NULL,1);


INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('form_auth','表单授权',NULL,'/coframe/resource/form/form_auth.jsp',NULL,'1','page','0',NULL,'default',3);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('my_task','我的任务',NULL,'/gocom/cap/workflow/client/task/myTask.jsp',NULL,'1','page','1',NULL,'default',7);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('process_auth','规则列表',NULL,'/coframe/flowconfig/process_auth.jsp',NULL,'1','page','0',NULL,'default',4);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('process_form_auth','配置流程规则',NULL,'/coframe/flowconfig/process_form_auth.jsp',NULL,'1','page','0',NULL,'default',4);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('process_manager','查看流程列表',NULL,'/coframe/flowconfig/process_manager.jsp',NULL,'1','page','1',NULL,'default',4);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('queryAgent','代理设置',NULL,'/org.gocom.cap.workflow.client.agent.queryAgent.flow',NULL,'1','flow','1',NULL,'default',7);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('queryAgentByAgentTo','查看代理',NULL,'/org.gocom.cap.workflow.client.agent.queryAgentByAgentTo.flow',NULL,'1','flow','1',NULL,'default',7);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('startProcess','启动流程',NULL,'/org.gocom.cap.workflow.client.process.startProcess.flow',NULL,'1','flow','1',NULL,'default',7);
INSERT INTO app_function (FUNCCODE,FUNCNAME,FUNCDESC,FUNCACTION,PARAINFO,ISCHECK,FUNCTYPE,ISMENU,APP_ID,TENANT_ID,FUNCGROUPID) VALUES ('view_auth','视图授权',NULL,'/coframe/resource/view/view_auth.jsp',NULL,'1','page','0',NULL,'default',3);


INSERT INTO app_menu (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('9','工作流程','工作流程','menu_process','0',NULL,NULL,1,NULL,3,NULL,NULL,'.9.',NULL,4,NULL,NULL,NULL,'default',NULL);
INSERT INTO app_menu (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('10','流程配置','流程配置','menu_process_config','1',NULL,NULL,2,NULL,4,NULL,NULL,'.1.10.',NULL,0,NULL,'process_manager',NULL,'default','1');
INSERT INTO app_menu (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('11','我的任务','我的任务','menu_my_task','1',NULL,NULL,2,NULL,1,NULL,NULL,'.9.11.',NULL,0,NULL,'my_task',NULL,'default','9');
INSERT INTO app_menu (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('12','代理设置','代理设置','menu_proxy_setting','1',NULL,NULL,2,NULL,2,NULL,NULL,'.9.12.',NULL,0,NULL,'queryAgent',NULL,'default','9');
INSERT INTO app_menu (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('13','查看代理','查看代理','menu_query_proxy','1',NULL,NULL,2,NULL,3,NULL,NULL,'.9.13.',NULL,0,NULL,'queryAgentByAgentTo',NULL,'default','9');
INSERT INTO app_menu (MENUID,MENUNAME,MENULABEL,MENUCODE,ISLEAF,PARAMETER,UIENTRY,MENULEVEL,ROOTID,DISPLAYORDER,IMAGEPATH,EXPANDPATH,MENUSEQ,OPENMODE,SUBCOUNT,APPID,FUNCCODE,APP_ID,TENANT_ID,PARENTSID) VALUES ('14','启动流程','启动流程','menu_start_process','1',NULL,NULL,2,NULL,4,NULL,NULL,'.9.14.',NULL,0,NULL,'startProcess',NULL,'default','9');

INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','my_task','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','process_auth','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','process_form_auth','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','process_manager','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','queryAgent','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','queryAgentByAgentTo','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','startProcess','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','form_auth','function','default','1','0','sysadmin',null);
INSERT INTO CAP_RESAUTH (PARTY_ID,PARTY_TYPE,RES_ID,RES_TYPE,TENANT_ID,RES_STATE,PARTY_SCOPE,CREATEUSER,CREATETIME) VALUES ('1','role','view_auth','function','default','1','0','sysadmin',null);

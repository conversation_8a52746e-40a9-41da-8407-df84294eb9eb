<?xml version="1.0" encoding="UTF-8"?>
<model:DataSetDiagram xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://www.primeton.com/datamodel" name="flowconfigEntity" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" displayName="entity" author="wangwx">
  <nodes xsi:type="model:PEntityNode" id="node0" name="CapRule" nodeType="table" displayName="CapRule" author="wanghl" instanceClass="org.gocom.components.coframe.flowconfig.flowconfigEntity.impl.CapRuleImpl" table="cap_rule" genStrategy="assigned">
    <location x="435" y="93"/>
    <size height="178" width="150"/>
    <figSize height="20" width="206"/>
    <columnProperty id="property9" name="ruleId" displayName="ruleId" columnName="RULE_ID" nullAble="false" PK="true" defaultValue="''" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="">
      <viewField dict="false" displayName="ruleId" id="ruleId" label="ruleId" name="ruleId" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property10" name="ruleName" displayName="ruleName" columnName="RULE_NAME" nullAble="false" defaultValue="''" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="">
      <viewField dict="false" displayName="ruleName" id="ruleName" label="ruleName" name="ruleName" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property11" name="tenantId" displayName="tenantId" columnName="TENANT_ID" nullAble="false" defaultValue="''" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="">
      <viewField dict="false" displayName="tenantId" id="tenantId" label="tenantId" name="tenantId" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property12" name="ruleType" displayName="ruleType" columnName="RULE_TYPE" nullAble="false" defaultValue="''" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="">
      <viewField dict="false" displayName="ruleType" id="ruleType" label="ruleType" name="ruleType" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property13" name="namespace" displayName="namespace" columnName="NAMESPACE" nullAble="false" defaultValue="''" columnType="VARCHAR" eosDataType="String" length="512" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true" default="">
      <viewField dict="false" displayName="namespace" id="namespace" label="namespace" name="namespace" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property14" name="ruleExpression" displayName="ruleExpression" columnName="RULE_EXPRESSION" nullAble="true" columnType="BLOB" eosDataType="Bytes" length="65535" lazy="true" dasType="com.primeton.das.entity.impl.lob.type.BlobByteArrayType" overWriteDefaultColumnType="false" showType="BlobByteArray" update="true" insert="true">
      <viewField dict="false" displayName="ruleExpression" id="ruleExpression" label="ruleExpression" name="ruleExpression" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property15" name="createuser" displayName="createuser" columnName="CREATEUSER" nullAble="true" columnType="VARCHAR" eosDataType="String" length="64" dasType="string" overWriteDefaultColumnType="false" showType="String" update="true" insert="true">
      <viewField dict="false" displayName="createuser" id="createuser" label="createuser" name="createuser" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="text"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="extAttr"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readonly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr attrValue="" defaultValue="" name="style"/>
          <attr attrValue="" defaultValue="" name="styleClass"/>
          <attr attrValue="" defaultValue="" name="validateAttr"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
    <columnProperty id="property16" name="createtime" displayName="createtime" columnName="CREATETIME" nullAble="true" columnType="DATETIME" eosDataType="Date" dasType="date" overWriteDefaultColumnType="false" showType="Date" update="true" insert="true">
      <viewField dict="false" displayName="createtime" id="createtime" label="createtime" name="createtime" userDefined="true">
        <fieldView>
          <type name="write"/>
          <attr name="formatPattern"/>
          <attr name="srcFormatPattern"/>
          <attr name="maxLength"/>
          <attr attrValue="" defaultValue="" name="filter"/>
          <attr attrValue="" defaultValue="" name="maxLengthString"/>
          <attr name="value"/>
        </fieldView>
        <fieldEdit>
          <type name="date"/>
          <attr name="format"/>
          <attr name="srcFormat"/>
          <attr name="submitFormat"/>
          <attr attrValue="" defaultValue="" name="allowInput"/>
          <attr name="maxValue"/>
          <attr name="minValue"/>
          <attr name="defaultNull"/>
          <attr name="allowNull"/>
          <attr attrValue="" defaultValue="" name="value"/>
          <attr attrValue="" defaultValue="" name="tabindex"/>
          <attr attrValue="" defaultValue="" name="readOnly"/>
          <attr attrValue="" defaultValue="" name="disabled"/>
          <attr attrValue="" defaultValue="" name="size"/>
          <attr attrValue="" defaultValue="" name="maxlength"/>
          <attr attrValue="" defaultValue="" name="title"/>
          <attr name="width"/>
          <attr name="style"/>
          <attr name="styleClass"/>
        </fieldEdit>
      </viewField>
    </columnProperty>
  </nodes>
  <topRuler/>
  <leftRuler/>
</model:DataSetDiagram>

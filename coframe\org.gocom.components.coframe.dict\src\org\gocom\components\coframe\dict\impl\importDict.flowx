<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="importDict.flowx" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3" state="stateless">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link3" name="link3" lineType="reference" isDefault="true" type="action" actionName="importDict" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
      <process:validateRules errorPage="" onError="default"/>
      <process:inputParameters>
        <process:parameter anyType="com.primeton.ext.access.http.IUploadFile" description="" historyStateLocation="client" isArray="false" name="dict_file"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" nodeType="common" type="end" contextPath="" method="forward" uri="/coframe/dict/dict_manager.jsp">
    <targetConnections>link2</targetConnections>
    <location x="355" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="357" y="96"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="逻辑流" displayName="importDict" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="208" y="61"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.dict.DictManager.importDict</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="excelFile" type="query" value="String" valueType="Primitive" pattern="reference">dict_file/filePath</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="reCode" type="query" value="String" valueType="Primitive">retCode</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="203" y="96"/>
    <figSize height="17" width="37"/>
    <node>subprocess0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-17 16:35:54" date="2013-12-17Z" description="" name="导入业务字典" version="6.3"/>
  <process:variables/>
</process:tPageFlow>

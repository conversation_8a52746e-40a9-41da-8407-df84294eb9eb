﻿<%@page import="com.eos.data.datacontext.DataContextManager"%>
<%@page pageEncoding="UTF-8"%>
<!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@page import="com.primeton.cap.AppUserManager"%>
<%@page import="com.wx.utils.publicMemer.Session.SessionUtil"%>
<%   
					String yym = request.getContextPath()+"/";
					String uri = request.getRequestURI();
					String jmqm = uri.replace(yym,"");
				%>
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- 使IE浏览器使用 [edge] 内核渲染页面 -->
	<meta name="renderer" content="webkit">
	<!-- 使360等国产浏览器使用 [webkit] 内核渲染页面 -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<!-- 初始化响应式布局 -->
	<!-- 以上标签必须放在最顶端 -->
	<%@include file="/coframe/tools/skins/common.jsp" %>
	<script type="text/javascript" src="<%=request.getContextPath()%>/utils/public.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/utils/EnterNext.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath() %>/utils/SysRouteService.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath() %>/utils/widget.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath() %>/utils/Js/ui/ButtonEdit.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath() %>/utils/Js/ui/ComboBox.js"></script>
	<script type="text/javascript" src="<%=request.getContextPath() %>/utils/Js/base/CheckValidation.js"></script>
	
	<script type="text/javascript" src="<%=request.getContextPath() %>/lic/licjs.js"></script>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>
		<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%>
	</title>
	<link rel="stylesheet" type="text/css"
		href="<%=contextPath%>/coframe/auth/skin1/index/fonts/font-awesome.min.css" />
	<link rel="stylesheet" type="text/css"
		href="<%=contextPath%>/coframe/auth/skin1/index/css/jquery.mCustomScrollbar.css" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/index/css/menu.css" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/index/css/tabs.css" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/index/css/frame.css" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/index/css/index.css" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/common/css/restore.css">
	<script src="<%=contextPath%>/coframe/auth/skin1/index/js/jquery.min.js"></script>
	<script src="<%=contextPath%>/coframe/auth/skin1/index/js/jquery.mCustomScrollbar.concat.min.js"></script>
	<script src="<%=contextPath%>/coframe/auth/skin1/index/js/menu.js"></script>
	<script src="<%=contextPath%>/coframe/auth/skin1/index/js/menutip.js"></script>
	<link rel="shortcut icon" href="<%=contextPath%>/coframe/tools/icons/favicon.ico" />

</head>

</head>

<body>
	<div class="navbar">
		<div class="navbar-header">
			<div class="navbar-brand" style="position:relative;padding:0;line-height:50px">
			<img name="logoPath" onerror="javascript:this.remove()" style="display:inline-block;vertical-align: middle;width:40px;margin-top:-2px;" src="images/logo.png"/>
			<span style="font-size:18px;">
			<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "leftTitleName")==null?"VANSYS 万序健康":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "leftTitleName"))%>
			</span>
			</div>
			<div class="navbar-brand navbar-brand-compact" style="padding:10px;">
				<img name="logoPath" onerror="javascript:this.remove()" src="images/logo.png" alt="" srcset="" style="width:100%;">
			</div>
		</div>
		<ul class="nav navbar-nav">
			<li><a id="toggle"><span class="fa fa-bars"></span></a></li>
			<!-- <li style="margin-right:10px;">
				<input style="margin-top:12px" class="mini-textbox" onenter="searchMenu(this)" emptyText="菜单搜索" />
			</li> -->
			<li id="small" style="position:relative;text-align:center;font-family:微软雅黑;display:none">
				<p style="font-size:14px;margin-top:5px;font-weight:bold">
				<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameCN")==null?"":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameCN"))%>

				</p>
				<p style="font-size:12px;margin-top:-4px;opacity:0.9">
				<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameEN")==null?"":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameEN"))%>

				
				</p>
			</li>
			<li id="big" style="position:relative;text-align:center;font-family:微软雅黑;line-height:46px;display:none">
				<span style="font-size:16px;font-weight:bold">
				<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameCN")==null?"":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameCN"))%>
				</span>
				<span style="font-size:14px;opacity:0.9;margin-left:5px;">
				<%=(com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameEN")==null?"":com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "titleNameEN"))%>
				</span>
			</li>
			<!-- <li class="icontop"><a href="#"><i class="fa fa-hand-pointer-o"></i><span >系统演示</span></a></li>
										<li class="icontop"><a href="#"><i class="fa fa-puzzle-piece"></i><span >开发文档</span></a></li>
										<li class="icontop"><a href="#"><i class="fa fa-sort-amount-asc"></i><span >人力资源</span></a></li>
										<li class="icontop"><a href="#"><i class="fa  fa-cog"></i><span >系统设置</span></a></li> -->
		</ul>
		<ul class="nav navbar-nav navbar-right navbar-hover">
			<li>
				<a href="#" class='dropdown-toggle' id=>
					<i class="fa fa-user-o" style="margin-right:6px;"></i><%=SessionUtil.getEmpName() %>
					<span id="curdept"></span>
				</a>

			</li>
			<li><a id="switchLogin" href="javascript:switchLogin()"><i class="fa fa-exchange"></i> 切换部门</a></li>
			<li><a href="#" id="updatepassword"><i class="fa fa-pencil-square-o"></i> 修改密码</a></li>
			<li class="dropdown">
				<a style="padding-bottom:5px" class="dropdown-toggle userinfo">
					<img class="user-img" src="index/images/user.jpg" />个人资料<i class="fa fa-angle-down"></i>
				</a>
				<ul class="dropdown-menu pull-right">
					<!-- <li ><a href="#"><i class="fa fa-eye "></i> 用户信息</a></li>-->
					<li><a href="javascript:getUserInfo()"><i class="fa fa-info-circle"></i> 版本信息</a></li>
					<li ><a href="javascript:switchThem()"><i class="fa fa-exchange"></i> 切换颜色</a></li>
					<li><a href="javascript:getUserInfo1()"><i class="fa fa-envelope-open"></i> 更新日志</a></li>
					<li><a href="<%=contextPath%>/coframe/auth/login/logout.jsp" target="_top"><i
								class="fa fa-sign-out"></i> 退出登录</a></li>

				</ul>
			</li>
		</ul>
	</div>

	<div class="container clearfix">
		<div style="position:relative;width:225px;">
			<input id="menuSearchBox" class="mini-textbox" emptyText="菜单搜索" />
			<span id="menuSearchBoxIcon" class="icons icon-search"></span>
		</div>
		
		<div class="sidebar" style="top:76px;padding-top:0px;">
			
			<!-- <div class="sidebar-toggle" style="opacity:0"><i class="fa fa-fw fa-dedent"></i></div> -->
			<div id="mainMenu"></div>
		</div>
		<div id="winSwitchLogin" class="mini-window" title="切换部门" style="width:700px;height:300px;" showShadow="true"
			showToolbar="true" showFooter="true" showModal="false" allowDrag="true">
			<div property="toolbar" style="padding:5px;">
				<span style="font-weight:bold;">当前登录部门：</span>
				<span id="deptName" style="color:blue;"></span>
				<input class="mini-textbox" id="deptsearch" name="deptsearch" emptyText="查找..."
					onvaluechanged="deptSearch" />
			</div>
			<div id="deptGrid" class="mini-datagrid" style="width:650px;height:170px;margin:auto;" allowResize="false"
				url="org.gocom.components.coframe.org.employee.queryEmpOfOrg.biz.ext" dataField="orgs"
				onrowdblclick="SwitchCon" multiSelect="false" showPager="false">
				<div property="columns">
					<div type="checkcolumn" width="30">选择</div>
					<div type="indexcolumn" width="30">序号</div>
					<div field="COMPANYCD" width="60" headerAlign="center" allowSort="true">机构编码</div>
					<div field="COMPANYNAME" width="140" headerAlign="center" allowSort="true">机构名称</div>
					<div field="orgcode" width="60" headerAlign="center" allowSort="true">部门编号</div>
					<div field="orgname" width="140" headerAlign="center" allowSort="true">部门名称</div>
				</div>
			</div>
			<div property="footer" style="text-align:right;padding:10px;padding-right:15px;">
				<a class="mini-button" iconCls="icon-ok" onclick="SwitchCon()">确定</a>
				<a class="mini-button" iconCls="icon-cancel" onclick="SwitchCan()">取消</a>
			</div>
		</div>
		<div id="UserInfo" class="mini-window aboutInfo" allowDrag="false"
			style="width:400px;height:500px;max-height:100%;overflow:auto">
			<img alt="" srcset="" style="position: absolute;width:100%;height:100%;">
			<div class="infoDec">
				<div class="logo" style="text-align:center;">
					<img alt="" style="opacity: 0.2;position: absolute;top: -19px;" srcset="">
				</div>
				<div class="decCenter" style="flex:2;padding-top:30px;">
					<div style="display:none;" id="empty"
						style="text-align: center;font-size: 19px; color: #cae5ff;margin-top: 172px;">
						您的系统未被授权，请提供MAC地址并联系 万序健康 021-55155381获取许可证书。
					</div>

					<div id="onempty">
						<div class='common' id="PROJECTNAME">
							SPD耗材管理系统
						</div>
						<div style="line-height:40px;padding: 0 20px;">
							授权到期时间 <span id="AUTHEXPDATE"></span>
						</div>
						<div style="line-height:40px;padding: 0 20px;">
							MAC地址 <span id="MACADDRS"></span>
						</div>
					</div>
				</div>
				<div style="text-align: center;color: #cae5ff;">
					<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "copyright")%>
				</div>
			</div>
		</div>

		<!-- 更新日志-->
		<div id="UserInfo1" class="mini-window aboutInfo" allowDrag="false"
			style="width:400px;height:500px;max-height:100%;overflow:auto;">
			<!-- <img alt="" srcset="" style="position: absolute;width:100%;height:100%;"> -->
			<div class="infoDec">
				<!-- <div class="logo" style="text-align:center;">
					<img  alt="" style="opacity: 0.2;position: absolute;top: -19px;" srcset="">
				</div> -->
				<div class="decCenter" style="flex:2;padding-top:30px;">
					<div style="display:none;" id="empty"
						style="text-align: center;font-size: 19px; color: #cae5ff;margin-top: 172px;">
						您的系统未被授权，请提供MAC地址并联系 万序健康 021-55155381获取许可证书。
					</div>
					<div id="onempty">
						<div class='common' id="manage">
							<p style="font-weight:900;">更新日志</p>
						</div>
						<div id="ADDDATE"></div>
					</div>
				</div>
				<div style="text-align: center;color: #cae5ff;">
					<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "copyright")%>
				</div>
			</div>
		</div>


		<div class="main">
			<div id="mainTabs" class="mini-tabs main-tabs" activeIndex="0" style="height:100%;" plain="false"
				buttons="#tabsButtons" arrowPosition="side" contextMenu="#tabsMenu">
				<!-- <div name="index" iconCls="fa-android" title="控制台">
														MiniUI导航框架
												</div> -->
			</div>
			<!--<div id="tabsButtons">
												<a class="tabsBtn"><i class="fa fa-home"></i></a>
												<a class="tabsBtn"><i class="fa fa-refresh"></i></a>
												<a class="tabsBtn"><i class="fa fa-remove"></i></a>
												<a class="tabsBtn"><i class="fa fa-arrows-alt"></i></a>
										</div>   -->
		</div>

	</div>
	<ul id="tabsMenu" class="mini-contextmenu" onbeforeopen="onBeforeOpen">
	</ul>
	<div id="TipsWin" class="mini-window" title="提醒" style="width:400px;height:250px;" showShadow="true"
		showToolbar="true" showFooter="true" showModal="false" onbeforebuttonclick="onTipsWinClose" allowDrag="true">
		<div id="tipsGrid" class="mini-datagrid" style="width:390px;height:190px;margin:auto;" allowResize="false"
			showPager="false">
			<div property="columns">
				<div type="indexcolumn" width="22px">序号</div>
				<div field="MSGID" width="50px" headerAlign="center" allowSort="true" visible="false">ID</div>
				<div field="MSGTYPE" width="50px" headerAlign="center" allowSort="true">类型</div>
				<div field="MSGTITLE" width="140" headerAlign="center" allowSort="true">内容</div>
				<div field="MSGDATE" width="110px" headerAlign="center" allowSort="true">时间</div>
			</div>
		</div>
		<!--<div id="ck1" name="product" class="mini-checkbox" readOnly="false" text="不再提醒" onvaluechanged="onValueChanged">
		</div>-->
	</div>
	
	<script>
		nui.parse();
		<%-- 这是一段需要校验的代码块的开始标记 --%>
		<% 
			String  param = (String)request.getSession().getServletContext().getAttribute("ISAUTH");
		%>
		var authorg = {};
		var lastDays = "<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "licRemindDays") %>";
		authorg = '<%=param%>';
		authorg = mini.decode(authorg, false);
		console.info(authorg);
		if (authorg!=null&&authorg.ISAUTH == 'TRUE') {
			if(Number(authorg.COUNTDOWNDATE)!=null&&Number(authorg.COUNTDOWNDATE)>-1&&Number(authorg.COUNTDOWNDATE)<Number(lastDays)){
				mini.alert("距离授权到期时间还有"+authorg.COUNTDOWNDATE+"天，请提醒管理员及时更新授权！");
			}
		} else if (authorg.ISAUTH == 'FALSE') {
			checkSessionjava(authorg);
		} else if(authorg.ISAUTH == 'EXPIRE'){
			checkSessionjava(authorg);
			mini.alert("授权已到期，请提醒管理员及时更新授权！");
		}
		function checkSessionjava(f) {
			mini.open({
				targetWindow: window, //页面对象。默认是顶级页面。
				url: 'Authorization.html', //页面地址
				title: '授权', //标题
				iconCls: '', //标题图标
				width: 400, //宽度
				height: 340, //高度
				allowResize: false, //允许尺寸调节
				allowDrag: false, //允许拖拽位置
				showCloseButton: false, //显示关闭按钮
				showMaxButton: false, //显示最大化按钮
				showModal: false, //显示遮罩
				loadOnRefresh: false, //true每次刷新都激发onload事件
				onload: function () {
					var iframe = this.getIFrameEl();
					var data = f;
					//调用弹出页面方法进行初始化
					iframe.contentWindow.SetData(data);
				},
				ondestroy: function (action) { //弹出页面关闭前
					if (action == "1") {
						window.location.href = "../login/logout.jsp";
					} else {
						setTimeout('checkSessionjava()', 1800000);
					}

				}

			});
		}
		<%-- 这是一段需要校验的代码块的结束标记 --%>
		loadBg();
		var menuData;
        var menuDataCopy;
		$(document).ready(function()         
		{
			$(".sidebar").height(document.body.scrollHeight-76);
			if((screen.width)>1360){
				$("#big").show();
				$("#small").hide();
			}else{
				$("#small").show();
				$("#big").hide();
			}
		})
		var contextPath = "<%=contextPath %>";
		var jmqm = "<%=jmqm %>";
		var contextPath = "<%=request.getContextPath() %>";

		var EmpId = "<%=SessionUtil.getEmpId() %>";
		var CompanyId = "<%=SessionUtil.getCompanyId() %>";
		var CompanyCd = "<%=SessionUtil.getCompanyCd() %>";
		var CompanyName = "<%=SessionUtil.getCompanyName() %>";
		var DeptId = "<%=SessionUtil.getDeptId() %>";
		var DeptCd = "<%=SessionUtil.getDeptCd() %>";
		var DeptName = "<%=SessionUtil.getDeptName() %>";
		var CompanyType = "<%=SessionUtil.getCompanyType() %>";
		var DeptType = "<%=SessionUtil.getDeptType() %>";
		//折叠菜单
		function collapse(e) {
			if (e.paneIndex == 1) {
				$('#logoWx').hide();
				$('#WxTitle').html("万序健康医用耗材SPD管理平台");
			}
		}
		//展开菜单
		function expand(e) {
			if (e.paneIndex == 1) {
				$('#logoWx').show();
				$('#WxTitle').html("医用耗材SPD管理平台");
			}
		}
		//设置主题
		function onSkinChange(skin) {
			mini.Cookie.set('miniuiSkin', skin);
			//mini.Cookie.set('miniuiSkin', skin, 100);//100天过期的话，可以保持下次登录也是切换后的效果
			window.location.reload()
		}
		//设置尺寸大小
		function onModeChange(skin) {
			mini.Cookie.set('miniuiMode', skin);
			//mini.Cookie.set('miniuiMode', skin, 100);//100天过期
			window.location.reload()
		}
		window.onload = function () {
			var skin = mini.Cookie.get("miniuiSkin");
			if (skin) {
				var selectSkin = document.getElementById("selectSkin");
				selectSkin.value = skin;
			}
			var mode = mini.Cookie.get("miniuiMode");
			if (mode) {
				var select = document.getElementById("selectMode");
				select.value = mode;
			}
		}
		//得到登录的userid
		var tabs = mini.get("mainTabs");

		function activeTab(item) {
			var tab = tabs.getTab(item.id);
			if (!tab) {
				tab = {
					name: item.menuPrimeKey,
					title: item.menuName,
					url: item.linkAction,
					iconCls: item.openMode,
					showCloseButton: true
				};
				tab = tabs.addTab(tab);
			}
			tabs.activeTab(tab);
		}
		var menu = new Menu("#mainMenu", {
			itemclick: function (item) {
				if (!item.childrenMenuTreeNodeList) {
					showTab(item)
					// activeTab(item);
				}
			}
		});
		$(".sidebar").mCustomScrollbar({
			autoHideScrollbar: true
		});
		getMenuData()

		function getMenuData() {
			$.ajax({
				url: "org.gocom.components.coframe.auth.LoginManager.getMenuData.biz.ext",
				type: "POST",
				success: function (text) {
					//menu
					menuData = text;
					new MenuTip(menu);
					menu.loadData(text.treeNodes);
					$('.has-children')[0].classList.add('open');
					// menu.init();

				}
			});
		}
		//监听<a>标签点击事件，如果有url，则在主内容区展现url,如果有jspUrl，则跳转到该url界面
		$("a").click(function () {
			if ($(this).attr("url")) {
				var url = $(this).attr("url") ? $(this).attr("url") : "";
				var uid = this.id ? this.id : "";
				var text = this.text ? this.text : "";
				var urll = setIFrame(url);
				showTab2(uid, text, urll);
				setIFrame(url);
				document.getElementById("levmenu").style.display = 'none';
				document.getElementById("lev2menu").style.display = 'none';
				$('.h-main').removeClass('blur');
				$('.black').hide();
				document.getElementById("mainField").style.zIndex = "1";
			}
		});

		function showTab2(uid, text, url) {
			var nurl = url;
			var index = url.indexOf("GET_FR_URL");
			if (index != -1) {
				var urlarray = nurl.split("||");
				var key = urlarray[0];
				var json = urlarray[1];
				var sendData = {
					"paramCentre": [json],
					"paramHand": key,
					"dbName": "default"
				};
				var nurl = "";
				mini.ajax({
					url: "com.wx.spd.SysRoute.callSysRoute.biz.ext",
					data: sendData,
					type: "post",
					cache: false,
					async: false,
					contentType: 'text/json',
					success: function (result) {
						var re = result.result;
						var M_JSON_RETN = re.M_JSON_RETN;
						var M_SQL_RETN = re.M_SQL_RETN;
						var message = result.message;
						if (M_JSON_RETN == null && M_SQL_RETN == null) {} else if (M_SQL_RETN.sqlcd == 1) {
							nurl = M_SQL_RETN.url;
						} else if (M_SQL_RETN.sqlcd == -1) {
							mini.alert(M_JSON_RETN.RETN_MSG);
						}
					},
					error: function (jqXHR, textStatus, errorThrown) {
						mini.alert(jqXHR.responseText);
					}
				});
			}

			var id = "tab$" + uid;
			var tab = tabs.getTab(id);
			if (!tab) {
				tab = {};
				tab._nodeid = uid;
				tab.name = id;
				tab.title = text;
				tab.showCloseButton = true;
				tab.url = nurl;
				tabs.addTab(tab);
			}
			tabs.activeTab(tab);
		}

		function showTab(item) {
			if(item){
				var tag = tag || 'span';
				var re = new RegExp('(<'+ tag +'.+?>|<\/'+ tag +'>)', 'g');
				item.menuName =  (item.menuName).replace(re, '');
			}
			var nurl = item.linkAction.lastIndexOf("?") != -1 ? contextPath + item.linkAction + "&menuSeq=" + item.menuSeq :
				contextPath + item.linkAction + "?menuSeq=" + item.menuSeq;
			var index = nurl.indexOf("GET_FR_URL");
			if (index != -1) {
				var urlarray = nurl.split("||");
				var key = urlarray[0];
				var json = urlarray[1];
				var sendData = {
					"paramCentre": [json],
					"paramHand": key,
					"dbName": "default"
				};
				var nurl = "";
				mini.ajax({
					url: "com.wx.spd.SysRoute.callSysRoute.biz.ext",
					data: sendData,
					type: "post",
					cache: false,
					async: false,
					contentType: 'text/json',
					success: function (result) {
						var re = result.result;
						var M_JSON_RETN = re.M_JSON_RETN;
						var M_SQL_RETN = re.M_SQL_RETN;
						var message = result.message;
						if (M_JSON_RETN == null && M_SQL_RETN == null) {} else if (M_SQL_RETN.sqlcd == 1) {
							nurl = M_SQL_RETN.url;
						} else if (M_SQL_RETN.sqlcd == -1) {
							mini.alert(M_JSON_RETN.RETN_MSG);
						}
					},
					error: function (jqXHR, textStatus, errorThrown) {
						mini.alert(jqXHR.responseText);
					}
				});
			}

			var id = "tab$" + item.menuPrimeKey;
			var tab = tabs.getTab(id);
			if (!tab) {
				tab = {};
				tab._nodeid = item.menuPrimeKey;
				tab.name = id;
				tab.iconCls = item.openMode;
				tab.title = item.menuName;
				tab.showCloseButton = true;
				tab.url = nurl;
				tabs.addTab(tab);
			}
			tabs.activeTab(tab);
		}

		var currentTab = null;
		var setMenu = [{
				text: "固定标签页",
				onclick: "setUsuallyTab('add')"
			},
			{
				text: "关闭标签页",
				onclick: "closeTab"
			},
			{
				text: "关闭其他[首页除外]",
				onclick: "closeAllButFirst"
			},
			{
				text: "刷新标签页",
				onclick: "reloadTab()"
			}
		];
		var desetMenu = [{
				text: "取消固定",
				onclick: "setUsuallyTab('delete')"
			},
			{
				text: "关闭标签页",
				onclick: "closeTab"
			},
			{
				text: "关闭其他[首页除外]",
				onclick: "closeAllButFirst"
			},
			{
				text: "刷新标签页",
				onclick: "reloadTab()"
			}
		];

		function onBeforeOpen(e) {
			console.log("要输出的内容");
			currentTab = tabs.getTabByEvent(e.htmlEvent);
			//var s = currentTab;
			if (!currentTab) {
				e.cancel = true;
				return;
			} else if (currentTab.isusual) {
				e.sender.set({
					"items": mini.clone(desetMenu)
				});
			} else {
				e.sender.set({
					"items": mini.clone(setMenu)
				});
			}
		}
		//toggle
		$("#toggle, .sidebar-toggle").click(function () {
			$('body').toggleClass('compact');
			mini.layout();
		});

		//dropdown
		$(".dropdown-toggle").click(function (event) {
			$(this).parent().addClass("open");
			return false;
		});

		$(document).click(function (event) {
			$(".dropdown").removeClass("open");
		});
		// 点击空白处收起选项框
		// $(document).click(function (event) {
		// 	$(".dropdown").removeClass("open");
		// });

		getDeptName();

		function getDeptName() { //下拉获得框部门
			var sendData = {
				"userid": userid
			};
			mini.ajax({
				url: "org.gocom.components.coframe.org.employee.queryEmpOfOrg.biz.ext",
				data: sendData,
				type: "post",
				cache: false,
				async: false,
				success: function (result) {
					var deptName = result.deptName;
					document.getElementById("curdept").innerHTML = "【" + deptName + "】";
				}
			});
		}
		
		//跳转到集采系统
		<%
	        java.util.Map attributes = SessionUtil.getUserObjectAttributes();
	        String loginUserName = "";
	        if (attributes!=null) {
	        	loginUserName = (String) attributes.get("EXTEND_USER_ID");
	        }
        %>
        var userid = '<%=loginUserName %>'
		
	    //获取用户信息
		function getUserInfo() {
			mini.ajax({
				url: "org.gocom.components.coframe.auth.LoginManager.queryAuthbiz.biz.ext",
				data: {},
				type: "post",
				cache: false,
				async: false,
				success: function (result) {
					var res = result.result;
					if (res.ISAUTH == 'TRUE') {
						$('#PROJECTNAME').html(res.PROJECTNAME)
						$('#AUTHEXPDATE').html(dateFormat(parseInt(res.EXPRIEDATE)))
						$('#MACADDRS').html(res.MACADDRESS)
						$('#empty').hide()
					} else {
						$('#empty').show()
						$('#onempty').hide()
					}
					mini.get("UserInfo").show();
					$('.mini-panel-viewport').height($('.aboutInfo').height());
					$('.mini-panel-body').height($('.aboutInfo').height());
					$('.mini-panel-viewport').css("#63A4C9");
					console.log(result)
				}
			});
		}
		
		/**
	 * 日期格式化
	 * @param Number time 
	 */
	function dateFormat (time) {
	  const t = new Date(time)
	  // 日期格式
	  const format = 'Y-m-d h:i:s'
	  const year = t.getFullYear()
	  // 由于 getMonth 返回值会比正常月份小 1
	  const month = t.getMonth() + 1
	  const day = t.getDate()
	  const hours = t.getHours()
	  const minutes = t.getMinutes()
	  const seconds = t.getSeconds()
	  const hash = {
		'Y': year,
		'm': month,
		'd': day,
		'h': hours,
		'i': minutes,
		's': seconds
	  }
	  return format.replace(/\w/g, o => {
		return hash[o]
	  })
	}

		// 更新日志
		function getUserInfo1() {
			var sendData = {
				adddate: '',
				versionid: "",
				updatemsg: "",
				dotype: "1"
			};
			var sdata = {
				'param': sendData
			};
			mini.ajax({
				url: "org.gocom.components.coframe.updatemsg.updatemsgcomponent.queryUpdateMsgbiz.biz.ext",
				data: JSON.stringify(sdata),
				type: "post",
				cache: false,
				async: false,
				contentType: 'text/json',
				success: function (result) {
					console.log(result.result)
					var res = result.result
					//拼接字符串
					var str = '';
					//对数据做遍历，拼接到页面显示
					for (var i = 0; i < res.length; i++) {
						str += '<p class="lit-top">' +
							'<p style="font-size:14px;color:#fff;font-weight:900">' + '' + res[i].ADDDATE + '(V' +
							res[i].VERSIONID + ')' + '</p>' +
							'<p>' + '' + '</p>' +
							'<ol>' +
							'<li class="space">' + res[i].UPDATEMSG + '</li>' +
							'</ol>' +
							'</p>'
						console.log(res[i].ADDDATE)

					}
					//放入页面的容器显示
					$('#ADDDATE').html(str);
					// $('#manage').html(str1);
					mini.get("UserInfo1").show();
					$('.mini-panel-viewport').height($('.aboutInfo').height());
					$('.mini-panel-body').height($('.aboutInfo').height());
					//      console.log(result.result)
					//   console.log(result[0].ADDDATE)
				}
			});
		}

		function switchLogin() { //下拉获得框部门
			var sendData = {
				"userid": userid
			};
			mini.ajax({
				url: "org.gocom.components.coframe.org.employee.queryEmpOfOrg.biz.ext",
				data: sendData,
				type: "post",
				cache: false,
				async: false,
				success: function (result) {
					var deptName = result.deptName;
					document.getElementById("deptName").innerHTML = deptName;
					var orgs = result.orgs;
					if (!orgs.length || orgs.length < 1) {
						mini.alert("当前登录用户没有可切换部门");
					} else {
						mini.get("winSwitchLogin").show();
						mini.get("deptGrid").load(sendData);
						$('.mini-panel-viewport').height('260px');
						$('.mini-panel-body').height('162px');
					}
				}
			});
		}

		function deptSearch() {
			var sendData = {
				"userid": userid,
				"querycd": mini.get("deptsearch").getValue()
			};
			mini.get("deptGrid").load(sendData);
		}

		function SwitchCon() {
			var select = mini.get("deptGrid").getSelected();
			mini.ajax({
				url: "org.gocom.components.coframe.org.employee.setEmpDept.biz.ext",
				data: {
					"org": select
				},
				type: "post",
				cache: false,
				async: false,
				success: function (result) {
					var flag = result.flag;
					if (flag == "-1") {
						mini.alert(result.message);
					} else if (flag == "1") {
						document.getElementById("curdept").innerHTML = "【" + select.orgname + "】";
						DeptId = select.orgid;
						DeptType = select.orgtype;
						var tab = tabs.getTab(0);
						tabs.removeAll(tab);
						addTab(2, "标签跟踪", "<%=contextPath%>/coframe/auth/skin1/p_tagstkrec.jsp");
						//addTab(3,"订单明细","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/ins/order/p_order_d.jsp");
						// addTab(3, "工作日清", "<%=contextPath%>/bi/rq2.jsp");
						addTab(19931017, "订单跟踪", "<%=contextPath%>/coframe/auth/skin1/order_track.jsp");
						//addTab(1002,"全院年度耗材消耗金额排名","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/demo/p_chart.jsp")
						//addTab(1003,"BI","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/bi/bi.jsp")
						//addTab(1004,"拣货进度","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/bi/pickDetail.jsp")
						// addTab(3,"BI","<%=contextPath%>/coframe/auth/skin1/p_chart.jsp")
						tabs.reloadTab(tab);
						mini.get("winSwitchLogin").hide();
						getMenuData();
						getIsCheckSts();
						// getUsualMenu();
					}
				}
			});
		}

		function SwitchCan() {
			mini.get("winSwitchLogin").hide();
		}

		function Close() {
			mini.get("TipsWin").hide();
		}

		function onValueChanged(e) {
			var checked = this.getChecked();
			if (checked == true) {
				onTipsWinClose();
			}
		}

		var date = new Date();
		var currentDate = date.getFullYear() + "年" + (date.getMonth() + 1) + "月" + date.getDate() + "日";
		$("#currentData").text(currentDate);

		//主内容区
		var iframe = document.getElementById("mainframe");

		//获取主页信息
		function searchForm() {
			var fefe = null;
			var sendData = {
				"fileName": getJspFileName(),
				"LibId": "searchForm",
				"dbName": getDataSource()
			};
			sendData.reqMap = {
				deptid: DeptId
			};
			var resultList = null;
			mini.ajax({
				url: "com.wx.utils.publicMemer.JDBCComponent.dbComboQuery.biz.ext",
				data: sendData,
				type: "post",
				cache: false,
				async: false,
				contentType: 'text/json',
				success: function (result) {
					if (result.sts == "1") {
						fefe = result.resultList;
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					mini.alert(jqXHR.responseText);
				}
			});
			return fefe;
		}
		//获取sql数据
		function commonselect(obj) {
			var fefe = null;
			obj.name = function () {
				var sendData = {
					"fileName": getJspFileName(),
					"LibId": obj.name,
					"dbName": getDataSource(),
				};
				sendData = $.extend(sendData, obj.sendData);
				// sendData.param = JSON.stringify({deptids:DeptId});
				var resultList = null;
				mini.ajax({
					url: "com.wx.utils.publicMemer.JDBCComponent.dbComboQuery.biz.ext",
					data: sendData,
					type: "post",
					cache: false,
					async: false,
					contentType: 'text/json',
					success: function (result) {
						if (result.sts == "1") {
							fefe = result.resultList;
						}
					},
					error: function (jqXHR, textStatus, errorThrown) {
						mini.alert(jqXHR.responseText);
					}
				});
			}();
			return fefe;
		}
		//监听修改密码链接点击事件
		$("#updatepassword").click(function () {
			nui.open({
				url: contextPath + '/coframe/rights/user/update_password.jsp',
				title: "修改密码",
				width: "500px",
				height: "300px"
			});
		});

		function getUsualMenu() {
			mini.ajax({
				url: "org.gocom.components.coframe.auth.LoginManager.getUsualMenu.biz.ext",
				type: "post",
				success: function (result) {
					results = result.result;
					if (results) {
						var btnhtml = "";
						for (var i = 0; i < results.length; i++) {
							var re = results[i];
							addTab(re.menuid, re.title, setIFrame(re.url));
							//btnhtml+="<input id='"+re.btnid+"' type='image' src='"+re.imgsrc+"' width='70px' heigth='70px'  />";
						}
						//$("#btn").html(btnhtml);
					}
				}
			});
		}




		//在iframe中展现相应页面
		function setIFrame(url) {
			var relative = url.substr(0, 1);
			if (relative == "/") {
				url = "<%=contextPath%>" + url;
			}
			return url;
			//iframe.src = url;
		}
		//默认显示首页
		showindexTab();

		function showindexTab() {
			var uid = 1,
				text = '首页',
				url = '<%=contextPath%>/coframe/auth/skin1/home.jsp';
			var id = "tab$" + uid;
			var tab = tabs.getTab(id);
			if (!tab) {
				tab = {};
				tab._nodeid = uid;
				tab.name = id;
				tab.title = text;
				tab.showCloseButton = false;
				tab.url = url;
				tabs.addTab(tab);
			}
			addTab(2, "标签跟踪", "<%=contextPath%>/coframe/auth/skin1/p_tagstkrec.jsp");
			//addTab(3,"订单明细","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/ins/order/p_order_d.jsp");
			// addTab(3, "工作日清", "<%=contextPath%>/bi/rq2.jsp");
			addTab(19931017, "订单跟踪", "<%=contextPath%>/coframe/auth/skin1/order_track.jsp");
			//addTab(1002,"全院年度耗材消耗金额排名","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/demo/p_chart.jsp")
			//addTab(1003,"BI","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/bi/bi.jsp")
			//addTab(1004,"拣货进度","<%=contextPath%>/OnLineDevelopProFiles/vsspd_hc/bi/pickDetail.jsp")
			// addTab(3,"BI","<%=contextPath%>/coframe/auth/skin1/p_chart.jsp")
			tabs.activeTab(tab);
		}

		function addTab(uid, text, url) {
			var id = "tab$" + uid;
			var tab = tabs.getTab(id);
			if (!tab) {
				tab = {};
				tab._nodeid = uid;
				tab.name = id;
				tab.title = text;
				tab.showCloseButton = false;
				tab.url = url;
				tab.isusual = true;
				tabs.addTab(tab);
			}
		}
		function JumpT(uid,text,url){
			var id = "tab$" + uid;
			var tab = tabs.getTab(id);
			if (!tab) {
				tab = {};
				tab._nodeid = uid;
				tab.name = id;
				tab.title = text;
				tab.showCloseButton = true;
				tab.url = url;
				tab.isusual = true;
				tabs.addTab(tab);
				tabs.activeTab(tab);
			}
		}
		function onTabsActiveChanged(e) {
			var tabs = e.sender;
			var tab = tabs.getActiveTab();
		}

		function ontabpagetourl(menuid, title, url) {
			showTab2(menuid, title, setIFrame(url));
			//showTab(menuid, title, setIFrame(url));
		}
		var currentTab = null;
		var setMenu = [{
				text: "固定标签页",
				onclick: "setUsuallyTab('add')"
			},
			{
				text: "关闭标签页",
				onclick: "closeTab"
			},
			{
				text: "关闭其他[首页除外]",
				onclick: "closeAllButFirst"
			},
			{
				text: "刷新标签页",
				onclick: "reloadTab()"
			}
		];
		var desetMenu = [{
				text: "取消固定",
				onclick: "setUsuallyTab('delete')"
			},
			{
				text: "关闭标签页",
				onclick: "closeTab"
			},
			{
				text: "关闭其他[首页除外]",
				onclick: "closeAllButFirst"
			},
			{
				text: "刷新标签页",
				onclick: "reloadTab()"
			}
		];

		function onBeforeOpen(e) {
			currentTab = tabs.getTabByEvent(e.htmlEvent);
			//var s = currentTab;
			if (!currentTab) {
				e.cancel = true;
				return;
			} else if (currentTab.isusual) {
				e.sender.set({
					"items": mini.clone(desetMenu)
				});
			} else {
				e.sender.set({
					"items": mini.clone(setMenu)
				});
			}
		}

		function closeTab() {
			tabs.removeTab(currentTab);
		}

		function closeAllButFirst() {
			var but = [currentTab];
			but.push(tabs.getTab("tab$1"));
			tabs.removeAll(but);
		}

		function reloadTab() {
			tabs.reloadTab(currentTab);
		}

		function setUsuallyTab(type) {
			//var b = currentTab.url.indexOf("_t=");
			var url = currentTab.url.replace("<%=request.getContextPath() %>", "");
			/*	if(b>0){
					url = currentTab.url.substring(nui.context.length,b);
				}else{
					url = currentTab.url.substring(nui.context.length,currentTab.url.length);
				}
				*/
			var node = {
				menuid: currentTab._nodeid,
				name: currentTab.name,
				title: currentTab.title,
				url: url,
				type: type
			};
			var sendData = {
				"node": node
			};
			mini.ajax({
				url: "org.gocom.components.coframe.auth.LoginManager.saveUsualMenu.biz.ext",
				data: sendData,
				type: "post",
				cache: false,
				async: false,
				contentType: 'text/json',
				success: function (result) {
					if (result.status == "1") {
						currentTab.isusual = true;
					} else if (result.status == "2") {
						currentTab.isusual = false;
					}
					mini.alert(result.message);
				},
				error: function (jqXHR, textStatus, errorThrown) {
					mini.alert(jqXHR.responseText);
				}
			});
		}
		
		
		getIsCheckSts();
		//获取验证状态
		function getIsCheckSts(){
			//console.log("bbb")
			var sendData = {
				"deptid":window.parent.DeptId,"configname":"ISFLAG"
			};
			//sendData.reqMap = {"deptid":window.parent.DeptId,"configname":"ISFLAG"};
			var resultList = null;
			mini.ajax({
				url: "org.gocom.components.coframe.auth.newLoginManager.getConfigurationUtil.biz.ext",
				data: sendData,
				type: "post",
				cache:false,
				async:false,
				contentType:'text/json',
				success: function (result) {
					if(result.falg == true){
						showalert();
					}else{
						Close();
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {
					mini.alert(jqXHR.responseText);
				}
			});
		}
		
		//showalert();
		
		function showalert() {
			var TipsWin = mini.get("TipsWin");
			var resultList = getTips();
			if (resultList.length > 0) {
				var tipsGrid = mini.get("tipsGrid");
				TipsWin.hide();
				tipsGrid.clearRows();
				tipsGrid.addRows(resultList);
				TipsWin.showAtPos("right", "bottom");
				TipsWin.blur();
			}else{
				Close();
			}
		}

		function getTips() {
			var TipsList = null;
			//var reqMap = {"deptids":JSON.parse(window.parent.DeptId)};
			var sendate = {"reqMap":{"deptids":window.parent.DeptId}};
			$.ajax({
				url: "com.wx.utils.msgAlert.msgAlertcomponent.getMsgs.biz.ext",
				type: 'POST',
				data: mini.encode(sendate),
				cache: false,
				async: false,
				contentType: 'text/json',
				success: function (result) {
					if (result.exception != null) {
						alert("您的登录已超时或账号在其它地点登录，请重新登录!");
					} else {
						TipsList = result.resultList;
					}
				}
			});
			return TipsList;
		}

		function onTipsWinClose() {
			var tipsGrid = mini.get("tipsGrid");
			var TipsList = tipsGrid.getData();
			var reqMap = {
				"TipsList": TipsList
			};
			mini.ajax({
				url: "com.wx.utils.msgAlert.msgAlertcomponent.updMsgs.biz.ext",
				type: 'POST',
				data: reqMap,
				cache: false,
				async: false,
				contentType: 'text/json',
				success: function (result) {}
			});
			Close();
		}
		getTipsConfig();

		function getTipsConfig() {
			var sendData = {
				"datas": [{
					"DEPTID": 1
				}],
				"json_args": {
					"EXECSQL": "PJSON_FUNCTIONS.F_GET_CONFIGVALUE"
				},
				"json_argsdetail": {
					"DEPTID": "1",
					"CONFIGCD": "SYS001"
				},
				"json_columns": [],
				"tagkey": "获取系统消息提示轮循时长",
				"dbName": "default"
			};
			mini.ajax({
				url: "com.wx.utils.publicMemer.SysRoute.callSysRoute.biz.ext",
				type: 'POST',
				data: sendData,
				cache: false,
				async: false,
				contentType: 'text/json',
				success: function (result) {
					var sysRetn = result.result;
					var configValue = 60000;
					if (sysRetn.CONFIGVALUE) {
						configValue = parseInt(sysRetn.CONFIGVALUE) * 1000;
					}
					window.setInterval(getIsCheckSts, configValue);
				}
			});
		}
		checkPsw();
		function checkPsw(){
			mini.ajax({
				url: "com.wx.utils.publicMemer.db.config.CheckPw.checkpwByuser.biz.ext",
				type: "POST",
				success: function (res) {
				   console.log(res);
				   if(res){
					   if(res.isDefault) {
					   	   alertModPswForce();
					   } else if (res.expireDays < Number(lastDays)) {
					   	   alertModPswExpired(res.expireDays);
					   }else if(!res.isDefault && res.score<30){
						   alertModPsw();
					   }
				   }
				}
			});
		}
		
		function alertModPswExpired(expireDays){
			mini.showMessageBox({
				title:'系统提示',
				message:'检测到您的密码还有'+ (expireDays + 1) + '天过期，为了您的账号安全，请修改密码！',
				buttons:['去修改','取消'],
				showCloseButton: false,
				callback:function(action){
					if(action=='去修改'){
						nui.open({
							url: contextPath + '/coframe/rights/user/update_password.jsp',
							title: "修改密码",
							width: "500px",
							height: "300px"
						  });
					}
				}
				
			});
		}
		
		function alertModPswForce(){
			mini.showMessageBox({
				title:'系统提示',
				message:'检测到您的密码为默认密码，为了您的账号安全，请修改密码！',
				buttons:['去修改'],
				showCloseButton: false,
				callback:function(action){
					if(action=='去修改'){
						nui.open({
							url: contextPath + '/coframe/rights/user/update_password.jsp',
							title: "修改密码",
							width: "500px",
							height: "300px"
						  });
					}
				}
				
			});
		}
		
		function alertModPsw(){
			mini.showMessageBox({
				title:'系统提示',
				message:'检测到您的密码强度太弱，为了您的账号安全，请尽快修改密码！',
				buttons:['去修改','取消'],
				callback:function(action){
					if(action=='去修改'){
						nui.open({
							url: contextPath + '/coframe/rights/user/update_password.jsp',
							title: "修改密码",
							width: "500px",
							height: "300px"
						  });
					}
				}
				
			});
			
		}
		function switchThem(){
			nui.open({
				url: contextPath + '/common/jsp/changeColor.jsp',
				title: "切换颜色",
				width: "350px",
				height: "230px"
			});
		}
		function loadBg() {
			var leftmenucolor = localStorage.getItem("leftmenucolor");
			var bottomcolor = localStorage.getItem("bottomcolor");
			var fontcolor = localStorage.getItem("fontcolor");
			var headercolor = localStorage.getItem("headercolor");
			var titlefontcolor = localStorage.getItem("titlefontcolor");
			var activecolor = localStorage.getItem("activecolor");
			var gridhovercolor = localStorage.getItem("gridhovercolor");
			var gridselectcolor = localStorage.getItem("gridselectcolor");
			var headerlogocolor = localStorage.getItem("headerlogocolor");
			var logoPath = localStorage.getItem("logoPath");
			if(leftmenucolor||fontcolor||bottomcolor||headercolor||titlefontcolor||activecolor){
				var root = document.querySelector(':root');
				root.setAttribute('style', '--leftmenucolor: '+leftmenucolor+';--fontcolor: '+fontcolor+';--titlefontcolor: '+titlefontcolor+';--headercolor: '+headercolor+';--activecolor: '+activecolor+';--gridselectcolor: '+gridselectcolor+';--headerlogocolor: '+headerlogocolor+';--gridhovercolor: '+gridhovercolor+';--bottomcolor: '+bottomcolor+'');
			}
			if(logoPath){
				$("img[name='logoPath']").attr("src",logoPath);
				
			}
		}
		function changeColor(params) {
			var root = document.querySelector(':root');
			localStorage.setItem("leftmenucolor",params.leftmenucolor);
			localStorage.setItem("fontcolor",params.fontcolor);
			localStorage.setItem("headercolor",params.headercolor);
			localStorage.setItem("titlefontcolor",params.titlefontcolor);
			localStorage.setItem("bottomcolor",params.bottomcolor);
			localStorage.setItem("activecolor",params.activecolor);
			localStorage.setItem("gridhovercolor",params.gridhovercolor);
			localStorage.setItem("gridselectcolor",params.gridselectcolor);
			localStorage.setItem("headerlogocolor",params.headerlogocolor);
			localStorage.setItem("logoPath",params.logoPath);
			loadBg();
		}
		$('#menuSearchBox input').bind('input propertychange', function() {
			searchMenu(this)
		});
		function searchMenu(e){
            if(e.value.trim()){
                $.ajax({
                    url: "org.gocom.components.coframe.auth.LoginManager.getMenuData.biz.ext",
                    type: "POST",
                    success: function (text) {
                        menuDataCopy = text;
                        filterMenu(menuDataCopy,e.value,'treeNodes','childrenMenuTreeNodeList');
                        menuDataCopy=  markP(menuDataCopy["treeNodes"]);
                        menu.loadData(menuDataCopy,1,e.value);
                    }
                });

			}else{
                $.ajax({
                    url: "org.gocom.components.coframe.auth.LoginManager.getMenuData.biz.ext",
                    type: "POST",
                    success: function (text) {
                        menu.loadData(text["treeNodes"]);
                        $('.has-children')[0].classList.add('open');
                    }
                });
			}

		}

		// function reBuildData(menuData,baseName,childName,nodepath,pid) {
        //     if(!menuData[baseName].length){
        //         return ;
        //     }
        //     for(var i=0;i<menuData[baseName].length;i++){
        //         menuData[baseName][i]["id"] = menuData[baseName][i]["menuPrimeKey"];
        //         menuData[baseName][i]["pid"] = pid?pid:'';
        //         menuData[baseName][i]["nodepath"] =(nodepath?nodepath:'')+menuData[baseName][i]["menuName"]+'/';
        //         if(menuData[baseName][i][childName]){
        //             reBuildData(menuData[baseName][i],childName,childName,menuData[baseName][i]["nodepath"],menuData[baseName][i]["id"]);
        //         }
		//
		// 	}
        // }

		function filterMenu(menuData,val,baseName,childName){
			if(!menuData[baseName].length){
			    return ;
			}
			for(var i=0;i<menuData[baseName].length;i++){
                if(menuData[baseName][i][childName]){
                    filterMenu(menuData[baseName][i],val,childName,childName);
                }else{
                    if(menuData[baseName][i]["menuName"].indexOf(val)!=-1){
                        menuData[baseName][i]["match"] = 1 ;
                    }
				}
			}
		}

		function markP(arr) {
            var A = function (arr1) {
                var flg;
				for(var i=0;i<arr1.length;i++){
				    if(arr1[i]["match"]==1){
				        flg = true;
				        break;
					}
				    if(arr1[i]["childrenMenuTreeNodeList"]!=null){
                        if(A(arr1[i]["childrenMenuTreeNodeList"])){
                            arr1[i]["match"]  = 1;
                        }
					}else{
				        continue;
					}

				}
				return flg;
            };
            A(arr);
            for(var j=0;j<arr.length;j++){
                if(arr[j]["childrenMenuTreeNodeList"]!=null){
                    var B = function (data) {
                        var flag;
						for(var m=0;m<data.length;m++){
							if(data[m]["match"]==1){
							    flag = true;
							    break;
							}
						}
						return flag;
                    };
                    if(B(arr[j]["childrenMenuTreeNodeList"])){
                        arr[j]["match"] =1;
					}
                }

			}
			//新增四级查询
			for(var k=0;k<arr.length;k++){
                if(arr[k]["childrenMenuTreeNodeList"]!=null){
					for (var s=0;s<arr[k]["childrenMenuTreeNodeList"].length;s++){
						if(arr[k]["childrenMenuTreeNodeList"][s]["childrenMenuTreeNodeList"]!=null){
							var C = function (data) {
							var flag;
							for(var m=0;m<data.length;m++){
								if(data[m]["match"]==1){
									flag = true;
									break;
								}
							}
							return flag;
							};
							if(C(arr[k]["childrenMenuTreeNodeList"][s]["childrenMenuTreeNodeList"])){
								arr[k]["match"] =1;
								arr[k]["childrenMenuTreeNodeList"][s]["match"]=1;
								
							}
						}
					}
                }

			}
            return arr;
        }
		
	</script>
</body>

</html>
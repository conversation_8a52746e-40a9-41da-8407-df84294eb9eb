<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="saveDictType" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" collapsed="false" nodeType="note" type="note" content="保存业务字典类型&#xD;&#xA;&#xD;&#xA;包括：&#xD;&#xA;1.添加字典类型&#xD;&#xA;2.添加子类型&#xD;&#xA;3.修改字典类型" title="陈鹏&#x9;13-11-29 下午3:33">
    <location x="34" y="405"/>
    <size height="136" width="226"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>transactionbegin0</targetNode>
    </sourceConnections>
    <location x="109" y="330"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link11</targetConnections>
    <targetConnections>link12</targetConnections>
    <targetConnections>link20</targetConnections>
    <location x="972" y="182"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.dict.dict.EosDictType" name="data" type="query" valueType="DataObject">data</process:return>
      <process:return id="1" language="String" name="status" type="query" valueType="Primitive">status</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="111" y="366"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="974" y="218"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="新增" displayName="insertEntity" grouped="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>transactioncommit0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link9" name="link9" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <targetConnections>link0</targetConnections>
    <location x="531" y="182"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.insertEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="530" y="218"/>
    <figSize height="17" width="25"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="修改" displayName="updateEntity" grouped="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>transactioncommit0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link8" name="link8" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link21</targetConnections>
    <location x="531" y="330"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.updateEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="530" y="366"/>
    <figSize height="17" width="25"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tTransactionBegin" id="transactionbegin0" name="事务开始" displayName="事务开始" type="transactionbegin">
    <sourceConnections xsi:type="process:tLink" id="link21" name="link21" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactionbegin0</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link22" name="link22" displayName="连接线" type="transition">
      <sourceNode>transactionbegin0</sourceNode>
      <targetNode>invokePojo2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">data/action</process:leftOperand>
          <process:rightOperand type="literal">add</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link15</targetConnections>
    <location x="257" y="330"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionbegin0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionbegin0label" name="label" nodeType="label">
    <location x="247" y="366"/>
    <figSize height="17" width="49"/>
    <node>transactionbegin0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionCommit" id="transactioncommit0" name="事务提交" displayName="事务提交" type="transactioncommit">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactioncommit0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <targetConnections>link6</targetConnections>
    <location x="676" y="182"/>
    <size height="28" width="28"/>
    <nodeLabel>transactioncommit0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactioncommit0label" name="label" nodeType="label">
    <location x="666" y="218"/>
    <figSize height="17" width="49"/>
    <node>transactioncommit0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionRollback" id="transactionrollback0" name="事务回滚" displayName="事务回滚" type="transactionrollback">
    <sourceConnections xsi:type="process:tLink" id="link10" name="link10" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactionrollback0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <targetConnections>link9</targetConnections>
    <location x="675" y="330"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionrollback0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionrollback0label" name="label" nodeType="label">
    <location x="665" y="366"/>
    <figSize height="17" width="49"/>
    <node>transactionrollback0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link12" name="成功" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="821" y="182"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">success</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="823" y="218"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link11" name="失败" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link10</targetConnections>
    <location x="821" y="330"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">fail</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="823" y="366"/>
    <figSize height="17" width="25"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="431" y="182"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">data/rank</process:from>
      <process:to type="query">rank</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">Integer.valueOf(rank) + 1</process:from>
      <process:to type="query">data/rank</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">data/seqno</process:from>
      <process:to type="query">seqno</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">data/dicttypeid</process:from>
      <process:to type="query">dicttypeid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">seqno + dicttypeid + &quot;.&quot;</process:from>
      <process:to type="query">data/seqno</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="433" y="218"/>
    <figSize height="17" width="25"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link13" name="link13" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="528" y="91"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="expression" pattern="reference">1</process:from>
      <process:to type="query">data/rank</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">data/dicttypeid</process:from>
      <process:to type="query">dicttypeid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">&quot;.&quot; + dicttypeid + &quot;.&quot;</process:from>
      <process:to type="query">data/seqno</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="530" y="127"/>
    <figSize height="17" width="25"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="查询字典项" displayName="查询字典项" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link19" name="link19" displayName="link14" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>transactioncommit1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="添加字典类型" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:complexCondition>
          <process:code>exist == 0 &amp;&amp; data.get(&quot;parentid&quot;) == null</process:code>
        </process:complexCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="添加子类型" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:complexCondition>
          <process:code>exist == 0 &amp;&amp; data.get(&quot;parentid&quot;) != null</process:code>
        </process:complexCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link22</targetConnections>
    <location x="259" y="30"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.expandEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">exist</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="240" y="66"/>
    <figSize height="17" width="61"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tTransactionCommit" id="transactioncommit1" name="事务提交" displayName="事务提交" collapsed="false" type="transactioncommit">
    <sourceConnections xsi:type="process:tLink" id="link18" name="link18" displayName="link17" isDefault="true" type="transition">
      <sourceNode>transactioncommit1</sourceNode>
      <targetNode>assign5</targetNode>
    </sourceConnections>
    <targetConnections>link19</targetConnections>
    <location x="677" y="30"/>
    <size height="28" width="28"/>
    <nodeLabel>transactioncommit1label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactioncommit1label" name="label" nodeType="label">
    <location x="667" y="66"/>
    <figSize height="17" width="49"/>
    <node>transactioncommit1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign5" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link20" name="已存在" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign5</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link18</targetConnections>
    <location x="821" y="30"/>
    <size height="28" width="28"/>
    <nodeLabel>assign5label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">exist</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign5label" name="label" nodeType="label">
    <location x="823" y="66"/>
    <figSize height="17" width="25"/>
    <node>assign5</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-11-29 16:50:10" date="2013-11-29Z" description="" name="保存业务字典类型" version="6.3"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" name="rank" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="seqno" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="dicttypeid" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="exist" primitiveType="Int"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="业务字典类型" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="data"/>
  </process:inputs>
  <process:outputs>
    <process:output description="业务字典类型" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictType" name="data"/>
    <process:output description="" isArray="false" name="status" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

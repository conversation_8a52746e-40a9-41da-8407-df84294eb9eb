package com.wx.utils.publicMemer.db;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.eos.system.annotation.Bizlet;

import net.sf.json.JSONObject;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;

/**
 * 数据库存储过程调用工具类 (重构优化版)
 * <p>
 * 主要优化点:
 * 1. 增加对人大金仓(KingbaseES)数据库的支持。
 * 2. 全面使用 try-with-resources 语句自动管理JDBC资源，防止泄漏。
 * 3. 提取重复代码到私有辅助方法中，遵循DRY原则。
 * 4. 使用静态常量替代硬编码的 "魔法值" (如数据库名、SQL语句)。
 * 5. 优化JSON转换逻辑，使用库函数替代手动拼接，增强健壮性。
 * 6. 改进异常处理，向上抛出异常而非直接打印堆栈。
 *
 * <AUTHOR>
 * @date 2015-09-17 15:40:18
 * @refactoredBy Senior Software Engineer
 */
@Bizlet("")
public class SysRouteUtil {

    // --- 常量定义 ---
    private static final String DB_PRODUCT_SQL_SERVER = "Microsoft SQL Server";
    private static final String DB_PRODUCT_ORACLE = "Oracle";
    private static final String DB_PRODUCT_DAMENG = "DM DBMS";
    private static final String DB_PRODUCT_KINGBASE = "KingbaseES";
    private static final String DB_PRODUCT_POSTGRESQL = "PostgreSQL"; // 人大金仓可能识别为PostgreSQL

    private static final String ARRAY_TYPE = "TYPE_NVARCHAR2";
    private static final String PROCEDURE_CALL_PX_ROUTE = "{call px_route.p_route_json(?,?,?,?,?)}";
    private static final String PROCEDURE_CALL_P_ROUTE_JSON = "{call p_route_json(?,?,?)}";
    
    private static final String SQL_INSERT_TBR_SYS_ROUTE = "INSERT INTO tbr_sys_route " +
            "(routekey, routedetailkey, tagkey, execsql, json_args, json_argsdetail, adddate, adduserid, addtermid, addapid) " +
            "VALUES (?,?,?,?,?,?,?,?,?,?)";
    
    private static final String SQL_GET_MAX_ROUTE_KEY_SQL_SERVER = "SELECT CASE WHEN MAX(routekey) > MAX(routedetailkey) " +
            "THEN MAX(routekey) ELSE MAX(routedetailkey) END AS seq FROM tbr_sys_route";


    /**
     * 调用不同数据库的存储过程
     *
     * @param datas             详细数据集
     * @param json_args         主参数
     * @param json_argsdetail   附加到每个详细记录的参数
     * @param json_columns      需要从datas中提取的列
     * @param dbName            数据源名称
     * @return 过程执行结果
     * @throws Exception
     */
    @Bizlet("")
    public HashMap<String, Object> callSysRoute(
            HashMap<String, Object>[] datas, HashMap<String, Object> json_args,
            HashMap<String, Object> json_argsdetail, String[] json_columns,
            String dbName) throws Exception {

        // 准备参数
        if (json_args.get("ADDUSERID") == null || "".equals(json_args.get("ADDUSERID")) || "sysadmin".equals(json_args.get("ADDUSERID"))) {
            json_args.put("ADDUSERID", "1");
        }
        String param1 = prepareArgs(json_args);
        String[] param2 = prepareArgsDetail(datas, json_argsdetail == null ? new HashMap<>() : json_argsdetail, json_columns);

        // 使用 try-with-resources 确保连接被关闭
        try (Connection conn = new ConnectionHelper().getConnectionProvider(dbName).getConnection()) {
            if (conn == null) {
                // 建议使用日志框架记录错误
                System.err.println("数据源配置问题，没有找到数据源：" + dbName);
                throw new SQLException("无法获取数据库连接，数据源: " + dbName);
            }
            
            String databaseProductName = conn.getMetaData().getDatabaseProductName();

            if (DB_PRODUCT_SQL_SERVER.equals(databaseProductName)) {
                return callProceduresOfSQLServer(conn, json_args, param2);
            } else if (DB_PRODUCT_ORACLE.equals(databaseProductName)) {
                return callProcedureWithArrayForOracle(conn, param1, param2);
            } else if (DB_PRODUCT_DAMENG.equals(databaseProductName) ||
                       DB_PRODUCT_KINGBASE.equals(databaseProductName) || 
                       DB_PRODUCT_POSTGRESQL.equals(databaseProductName)) { // 人大金仓和达梦使用标准JDBC Array
                return callProcedureWithArrayForStandardDB(conn, param1, param2);
            } else {
                throw new UnsupportedOperationException("不支持的数据库类型: " + databaseProductName);
            }

        } catch (SQLException e) {
            // 建议使用日志框架记录详细错误信息
            System.err.println("数据库操作失败: " + e.getMessage());
            throw e; // 向上抛出异常，让调用者处理
        }
    }

    /**
     * 针对Oracle调用带数组参数的过程（使用Oracle特定API）
     */
    private HashMap<String, Object> callProcedureWithArrayForOracle(Connection conn, String param1, String[] param2) throws SQLException {
        try (CallableStatement st = conn.prepareCall(PROCEDURE_CALL_PX_ROUTE)) {
            ArrayDescriptor descriptor = ArrayDescriptor.createDescriptor(ARRAY_TYPE, conn);
            ARRAY arr = new ARRAY(descriptor, conn, param2);
            st.setString(1, param1);
            st.setObject(2, arr, Types.ARRAY);
            return executeAndFetchResults(st);
        }
    }

    /**
     * 针对支持标准JDBC Array的数据库（达梦、人大金仓等）调用带数组参数的过程
     */
    private HashMap<String, Object> callProcedureWithArrayForStandardDB(Connection conn, String param1, String[] param2) throws SQLException {
        try (CallableStatement st = conn.prepareCall(PROCEDURE_CALL_PX_ROUTE)) {
            // 标准JDBC建议使用基本类型名，如 "VARCHAR", "TEXT" 等。具体类型名可能取决于数据库驱动。
            // "VARCHAR" 是一个比较通用的选择。
            java.sql.Array array = conn.createArrayOf(ARRAY_TYPE, param2);
            st.setString(1, param1);
            st.setObject(2, array, Types.ARRAY);
            return executeAndFetchResults(st);
        }
    }

    /**
     * 抽取的公共执行和结果获取逻辑
     */
    private HashMap<String, Object> executeAndFetchResults(CallableStatement st) throws SQLException {
        st.registerOutParameter(3, Types.VARCHAR);
        // 假设返回的数组类型名在不同数据库间是兼容的，或者存储过程定义是兼容的
        st.registerOutParameter(4, Types.ARRAY, ARRAY_TYPE); 
        st.registerOutParameter(5, Types.VARCHAR);

        st.execute();

        HashMap<String, Object> repMap = new HashMap<>();
        repMap.put("M_JSON_RETN", regexp_replace("\\n", st.getString(3)));
        // 注意: 获取数组返回值的逻辑可能需要根据具体驱动调整
        // 原代码中 M_JSON_ARGS_RETN 始终为 null，此处保持一致
        repMap.put("M_JSON_ARGS_RETN", null); 
        repMap.put("M_SQL_RETN", regexp_replace("\\n", st.getString(5)));
        return repMap;
    }

    /**
     * 调用SQL Server的存储过程
     */
    public HashMap<String, Object> callProceduresOfSQLServer(Connection conn, HashMap<String, Object> json_args, String[] param2) throws Exception {
        HashMap<String, Object> repMap = new HashMap<>();
        int routekey = saveTbrSysRoute(conn, json_args, param2);
        
        try (CallableStatement st = conn.prepareCall(PROCEDURE_CALL_P_ROUTE_JSON)) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("ROUTEKEY", routekey);
            
            st.setString(1, prepareArgs(map));
            st.registerOutParameter(2, Types.NVARCHAR);
            st.registerOutParameter(3, Types.NVARCHAR);
            st.execute();

            repMap.put("M_JSON_RETN", regexp_replace("\\n", st.getString(2)));
            repMap.put("M_SQL_RETN", regexp_replace("\\n", st.getString(3)));
        }
        return repMap;
    }

    /**
     * 为SQL Server保存路由数据
     */
    public int saveTbrSysRoute(Connection conn, HashMap<String, Object> json_args, String[] param2) throws Exception {
        int routekey = getMaxRouteKeyOfSQLServer(conn) + 1;
        json_args.put("ROUTEKEY", routekey);
        
        // 使用 try-with-resources 确保 PreparedStatement 被关闭
        try (PreparedStatement ps = conn.prepareStatement(SQL_INSERT_TBR_SYS_ROUTE)) {
            int routedetailkey = routekey;
            for (String detailJson : param2) {
                json_args.put("ROUTEDETAILKEY", routedetailkey++);
                ps.setLong(1, routekey);
                ps.setLong(2, routedetailkey);
                ps.setString(3, String.valueOf(json_args.getOrDefault("TAGKEY", "")));
                ps.setString(4, String.valueOf(json_args.getOrDefault("EXECSQL", "")));
                ps.setString(5, prepareArgs(json_args));
                ps.setString(6, detailJson);
                ps.setTimestamp(7, new java.sql.Timestamp(System.currentTimeMillis()));
                ps.setString(8, String.valueOf(json_args.getOrDefault("ADDUSERID", "")));
                ps.setString(9, String.valueOf(json_args.getOrDefault("ADDTERMID", "")));
                ps.setString(10, String.valueOf(json_args.getOrDefault("ADDAPID", "")));
                ps.addBatch();
            }
            ps.executeBatch();
            conn.commit(); // 假设业务逻辑需要手动提交
        }
        return routekey;
    }

    /**
     * 获取SQL Server中的最大路由键
     */
    public int getMaxRouteKeyOfSQLServer(Connection conn) throws SQLException {
        // 使用 try-with-resources 确保 Statement 和 ResultSet 被关闭
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(SQL_GET_MAX_ROUTE_KEY_SQL_SERVER)) {
            if (rs.next()) {
                return rs.getInt("seq");
            }
        }
        return 0; // 如果表中没有数据，返回0，后续逻辑会+1
    }

    /**
     * 将HashMap数组转换为JSON字符串数组（优化版）
     * 使用 net.sf.json 库来安全地构建JSON，避免手动拼接。
     */
    public String[] HashMapToJson(HashMap<String, Object>[] maps) {
        if (maps == null) {
            return new String[0];
        }
        String[] jsonArray = new String[maps.length];
        for (int i = 0; i < maps.length; i++) {
            // 将Key转换为大写，以保持与原逻辑一致
            HashMap<String, Object> upperCaseMap = new HashMap<>();
            if (maps[i] != null) {
                for (Map.Entry<String, Object> entry : maps[i].entrySet()) {
                	String value = entry.getValue() != null ? entry.getValue().toString() : "";
                    upperCaseMap.put(entry.getKey().toUpperCase(), value);
                }
            }
            jsonArray[i] = JSONObject.fromObject(upperCaseMap).toString();
        }
        return jsonArray;
    }
    
    /**
     * 将单个HashMap转换为JSON字符串
     */
    public String prepareArgs(HashMap<String, Object> json_args) {
        if (json_args == null) {
            return "{}";
        }
        return HashMapToJson(new HashMap[]{json_args})[0];
    }

    /**
     * 准备详细参数数组
     */
    public String[] prepareArgsDetail(HashMap<String, Object>[] datas,
                                      HashMap<String, Object> json_argsdetail, String[] json_columns) {
        if (datas == null || datas.length == 0) {
            return new String[0];
        }
        
        HashMap<String, Object>[] params = new HashMap[datas.length];
        for (int i = 0; i < datas.length; i++) {
            HashMap<String, Object> data = datas[i];
            HashMap<String, Object> temp = new HashMap<>();
            if (json_columns == null || json_columns.length == 0) {
                temp.putAll(data);
            } else {
                for (String column : json_columns) {
                    temp.put(column, data.get(column));
                }
            }
            temp.putAll(json_argsdetail);
            params[i] = temp;
        }
        return HashMapToJson(params);
    }
    
    /**
     * 将JSON字符串转换为HashMap
     */
    public HashMap<String, String> jsontomap(String jsonStr) {
        HashMap<String, String> result = new HashMap<>();
        if (jsonStr == null || jsonStr.trim().isEmpty() || "null".equalsIgnoreCase(jsonStr.trim())) {
            return result;
        }
        try {
            JSONObject jsonObject = JSONObject.fromObject(jsonStr);
            for (Object key : jsonObject.keySet()) {
                String keyStr = String.valueOf(key);
                result.put(keyStr, jsonObject.getString(keyStr));
            }
        } catch (Exception e) {
            // 如果JSON格式不正确，返回空Map，而不是抛出异常，保持原逻辑的宽容性
            System.err.println("JSON to map conversion failed for string: " + jsonStr);
        }
        return result;
    }

    /**
     * 解析存储过程返回的结果
     */
    @Bizlet("")
    public HashMap<String, Object> resolve(HashMap<String, String> req) {
        HashMap<String, Object> rep = new HashMap<>();
        
        // 使用 getOrDefault 提供默认值，使代码更简洁
        HashMap<String, String> M_JSON_RETN = jsontomap(req.getOrDefault("M_JSON_RETN", "{'RETN_CODE':null,'RETN_MSG':null}"));
        HashMap<String, String> M_SQL_RETN = jsontomap(req.getOrDefault("M_SQL_RETN", "{'SQLCD':null,'SQLMSG':null}"));

        rep.putAll(M_SQL_RETN);
        rep.putAll(M_JSON_RETN);
        
        String rtncd = M_JSON_RETN.get("RETN_CODE");
        rep.put("message", M_JSON_RETN.get("RETN_MSG"));
        rep.put("flag", "1".equals(rtncd));
        
        return rep;
    }

    /**
     * 使用正则表达式替换字符串
     */
    public static String regexp_replace(String regEx, String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        Pattern p = Pattern.compile(regEx);
        return p.matcher(str).replaceAll("").trim();
    }

    /**
     * 使用正则表达式匹配字符串
     */
    public static String regexp_matcher(String regEx, String str) {
        if (str == null) {
            return null;
        }
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return m.group();
        }
        return str; // 如果未找到匹配，返回原字符串，保持原逻辑
    }
}

<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<%@ page import="java.util.Optional" %>
<%@ page import="com.eos.foundation.eoscommon.ConfigurationUtil" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!--
  - Author(s): ouwen
  - Date: 2022-05-17 17:58:13
  - Description: 帆软11.0 Ajax跨域单点登录集成
-->
<head>
<title>ReportServer11</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <script src="<%= request.getContextPath()%>/coframe/auth/skin1/index/js/jquery.min.js"></script>
</head>
<body>
<%
	// 优化ip、port的取值
	String reportServerHost = Optional
			.ofNullable(ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "reportServerHost"))
			.orElse("127.0.0.1");
	String reportServer11Port = Optional
			.ofNullable(ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "reportServer11Port"))
			.orElse("38899");
%>
	<script type="text/javascript">
       var params = window.location.search;
       var ip = "<%=reportServerHost%>";
       var port = "<%=reportServer11Port%>";
       var prefix = params.includes(".fvs") ? "/view/duchamp" : "/view/report";

       // 帆软11.0单点登录用户名和密码
       var frUsername = "viewer"; // 帆软系统用户名
       var frPassword = "viewer"; // 帆软系统密码

       // 构建帆软服务器地址
       var frServerUrl = window.location.protocol + "//" + ip + ":" + port;

       // 目标报表地址
       var targetUrl = frServerUrl + "/webroot/decision" + prefix + params;

       // 使用Ajax跨域单点登录（帆软11.0）
       function performSSO() {
           var loginUrl = frServerUrl + "/webroot/decision/login/cross/domain";

           jQuery.ajax({
               url: loginUrl,
               timeout: 10000, // 10秒超时
               dataType: "jsonp", // 跨域采用jsonp方式
               jsonp: "callback",
               data: {
                   "fine_username": frUsername,
                   "fine_password": frPassword,
                   "validity": -1
               },
               success: function(res) {
                   console.log(res);
                   if (res.errorCode) {
                       showError("帆软单点登录失败：" + res.errorMsg);
                   } else {
                       // 登录成功，跳转到目标报表页面
                       window.location.href = targetUrl;
                   }
               },
               error: function(xhr, status, error) {
                   if (status === 'timeout') {
                       showError("帆软单点登录超时，请检查网络连接");
                   } else {
                       showError("帆软单点登录失败：" + error);
                   }
               }
           });
       }

       // 显示错误信息
       function showError(message) {
           alert(message);
           console.error(message);
       }

       // 页面加载完成后执行单点登录
       if (document.readyState === 'loading') {
           document.addEventListener('DOMContentLoaded', performSSO);
       } else {
           performSSO();
       }
    </script>
</body>
</html>
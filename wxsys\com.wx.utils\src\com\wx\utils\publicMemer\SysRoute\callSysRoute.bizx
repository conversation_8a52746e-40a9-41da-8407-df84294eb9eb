<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="callSysRoute" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" description="" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="102" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link2</targetConnections>
    <targetConnections>link5</targetConnections>
    <location x="510" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="java.util.HashMap" name="result" type="query" valueType="Java">result</process:return>
      <process:return id="1" language="String" name="message" type="query" valueType="Primitive">message</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="104" y="186"/>
    <figSize height="16" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="512" y="186"/>
    <figSize height="16" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="callSysRoute" displayName="callSysRoute" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>assign2</targetNode>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="294" y="150"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.wx.utils.publicMemer.db.SysRouteUtil.callSysRoute</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="datas" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.Object>[]" valueType="Java" pattern="reference">datas</process:inputVariable>
      <process:inputVariable id="1" name="json_args" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.Object>" valueType="Java" pattern="reference">json_args</process:inputVariable>
      <process:inputVariable id="2" name="json_argsdetail" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.Object>" valueType="Java" pattern="reference">json_argsdetail</process:inputVariable>
      <process:inputVariable id="3" name="json_columns" type="query" value="java.lang.String[]" valueType="Java" pattern="reference">json_columns</process:inputVariable>
      <process:inputVariable id="4" name="dbName" type="query" value="java.lang.String" valueType="Java" pattern="reference">dbName</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.Object>" valueType="Java">repMap</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="269" y="186"/>
    <figSize height="16" width="73"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="resolve" displayName="resolve" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>assign2</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="395" y="150"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.wx.utils.publicMemer.db.SysRouteUtil.resolve</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="req" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.String>" valueType="Java" pattern="reference">repMap</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.Object>" valueType="Java">result</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="385" y="186"/>
    <figSize height="16" width="42"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值2" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <targetConnections>link3</targetConnections>
    <location x="291" y="255"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="expression" pattern="reference">context.get(&quot;__exception&quot;)</process:from>
      <process:to type="query">message</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="289" y="291"/>
    <figSize height="16" width="32"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="194" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">tagkey</process:from>
      <process:to type="query">json_args/TAGKEY</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/empid</process:from>
      <process:to type="query">json_args/ADDUSERID</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/userRemoteIP</process:from>
      <process:to type="query">json_args/ADDTERMID</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/userAddApid</process:from>
      <process:to type="query">json_args/ADDAPID</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/userLoginBrower</process:from>
      <process:to type="query">json_args/LOGINBROWSER</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/userId</process:from>
      <process:to type="query">json_args/ADDUSERCD</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/userName</process:from>
      <process:to type="query">json_args/ADDUSERNAME</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="196" y="186"/>
    <figSize height="16" width="25"/>
    <node>assign0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="gkm" createTime="2015-09-17 15:45:34" date="2015-09-17Z" description="" name="callSysRoute" version="*******"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="dbName" primitiveType="String"/>
    <process:input anyType="java.util.HashMap" description="" isArray="true" name="datas"/>
    <process:input anyType="java.util.HashMap" description="" isArray="false" name="json_args"/>
    <process:input anyType="java.util.HashMap" description="" isArray="false" name="json_argsdetail"/>
    <process:input description="" isArray="true" name="json_columns" primitiveType="String"/>
    <process:input description="" isArray="false" name="tagkey" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="java.util.HashMap" description="" isArray="false" name="result"/>
    <process:output description="" isArray="false" name="message" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

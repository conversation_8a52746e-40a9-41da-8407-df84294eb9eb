﻿<%@page pageEncoding="UTF-8"%>
  <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
  <html xmlns="http://www.w3.org/1999/xhtml">
  <%@include file="/coframe/tools/skins/common.jsp" %>
    <HTML>

    <HEAD>
      <META http-equiv=Content-Type content="text/html; charset=gb2312">
      <LINK href="<%=contextPath %>/coframe/auth/skin1/css/admin.css" type="text/css" rel="stylesheet">
      <link href="<%=contextPath %>/coframe/auth/skin1/css/font-awesome/css/font-awesome.css" type="text/css" rel="stylesheet" />
        <style>
          html,body{
            width: 100%;
            height: 100%;
          }
          .sanji{
            padding-left:10px;
          }
          .menums{
            transition: all 0.4s;
            padding-bottom: 30px;
          }
          /* .menums .menumsli{
            background:url(images/gaoguang.png) no-repeat center;background-size:100% 100%;
          } */
          .menums li.menumsli:hover{
            cursor: pointer;
            background-color:#2c3542;
            color:#fff !important;
          }
          .tables .hovermenu:hover{
            cursor: pointer;
            background-color:#2c3542;
          }
          a{
            display: block;
            color:#dde8ef;
          }
          a:link {color:#bcc5cc !important;}		/* 未被访问的链接 */
          a:visited {color:#d0d0d0 !important ;}	/* 已被访问的链接 */
          a:hover {color:#fff !important;}	/* 鼠标指针移动到链接上 */
          a:active {color:#d0d0d0 !important;}	/* 正在被点击的链接 */
          A.menuParent:link {
              COLOR: #d5eeff;
          }
          A.menuChild:link {
              COLOR: #d5eeff;
          }
          .lihovercur A.menuParent:link{
            color:#fff;
          }
          /*
          点击选中后颜色样式
          */
          .lihovercur{
            height: 40px;
            background-color:#2c3542;
          }
          .lihovercur a:link{
            color:#fff !important;
          }
          .lihovercur a{
            color:#fff !important;
          }
          .licur{
            background-color: #4384d4;
            color:#fff !important;
          }
          .tables .hovermenu.tablesTrcur{
            background-color:#233c52;
          }
        </style>
      <SCRIPT language=javascript>

        function expand(el,tableHtml) {
          console.log($('#'+el))
          $(".lihovercur").removeClass("lihovercur");

          if ( typeof($('#'+el).css('display'))=="undefined" || $('#'+el).css('display')=='none') { //判断子菜单是否隐藏
            $('.menuSecound').hide(); 
            try{
            	 $('.fa-angle-down').addClass("fa-angle-right"); //所有二级箭头合并
            	$('.fa-angle-down').removeClass("fa-angle-down"); //所有二级箭头合并

            }catch(e){
            	console.log(e);
            }
           
            if(typeof(tableHtml) !="undefined" && tableHtml.localName=="li") //一级菜单点击
            {
              $('.menuFirst').hide();//一级菜单 隐藏

             // $('.icon-minus').addClass("icon-adds"); //所有一级箭头合并
             // $('.icon-minus').removeClass("icon-minus"); //移出

              //tableHtml.getElementsByTagName('i')[0].className = "icon iconfont icon-minus";
            }
            else if(typeof(tableHtml) !="undefined") //二级菜单点击
            {
              tableHtml.className="hovermenu lihovercur";
              try{
              	tableHtml.getElementsByTagName('i')[1].className = "fa fa-angle-down";
              }catch(e){
              	console.log(e);
              }
              
            }
            $('#'+el).show();//显示下级菜单
            
          }
          else {
            if(typeof(tableHtml) !="undefined" && tableHtml.localName=="li") //当前点击一级菜单 合并
            {
             // $('.icon-minus').addClass("icon-adds"); //所有一级箭头合并
             // $('.icon-minus').removeClass("icon-minus"); //移出
            }
            $('.fa-angle-down').addClass("fa-angle-right"); //所有二级箭头合并
            $('.fa-angle-down').removeClass("fa-angle-down"); //
            $('#'+el).hide();//隐藏下级菜单
          }
          return;
        }

      </SCRIPT>
    </HEAD>

    <BODY id="meauscontent" style="background-color:#364150">
      
    </BODY>

    <script>
      getMenuData()
      function getMenuData() {
        $.ajax({
          url: "org.gocom.components.coframe.auth.LoginManager.getMenuData.biz.ext",
          type: "POST",
          success: function (text) {
            var menuHtml ="<ul class=\"menums\">" ;
            for(var i=0;i < text.treeNodes.length; i++){

              var firstMenu = "<li class=\"menumsli\" style=\"width:100%;height:46px;line-height:46px;color:#d5eeff; text-align: left; font-size: 14px; \"  onclick=expand(\"menu"+text.treeNodes[i].menuPrimeKey+"\",this)>"
              //  firstMenu+= "<div style=\"background:url(images/underline.png) no-repeat center;background-size:100% 100%;position: relative;bottom:2px;width: 100%;height:4px;\"></div>"
                  firstMenu+="<i style=\"color:#d5eeff; padding-right: 4px;margin-left: 14px;font-size:16px;\" class=\""+text.treeNodes[i].openMode
+"\"></i>"+ text.treeNodes[i].menuName
                firstMenu+= " </li>";
              firstMenu+="<li class=\"hovermenu menuFirst\" style=\"display:block;padding-top: 4px;\" id =menu"+text.treeNodes[i].menuPrimeKey+">"
              var treeNodes = text.treeNodes[i].childrenMenuTreeNodeList;

              var levmenu = setMenuData(treeNodes);
              firstMenu += levmenu;
              firstMenu +="</li>";
              menuHtml += firstMenu;
            }
            menuHtml += "</ul>";
            $("#meauscontent").html(menuHtml);
            
          }
        });
      }
      function setMenuData(data) {
        // console.log(JSON.stringify(data))
        if (data) {
          var levmenu = "";
          
          for (var i = 0; i < data.length; i++) { //循环1

            var menuName = data[i].menuName;
            var linkAction = data[i].linkAction ? data[i].linkAction : "";
            var menuPrimeKey = data[i].menuPrimeKey;
            var menuSeq = data[i].menuSeq;
            
            var cellSpac = "<TABLE class=\"tables\" cellSpacing=0 cellPadding=0  border=0 style=\"width:100% ;\" > <TR class=\"hovermenu\" onclick=expand(\"child"+menuPrimeKey+"\",this) height=40>   <TD style=\"PADDING-LEFT: 24px\">";
            if (linkAction == "") { 
                  cellSpac += "<A  id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + " class=menuParent  > <i style=\"font-size: 12px; padding-right: 6px;\" class=\""+data[i].openMode
+"\"></i>" +  menuName +"<i style=\"font-size: 14px;float:right;margin-right:20px;margin-top: 2px;\" class=\"fa fa-angle-right\"></i></A>"
            } else { 
              cellSpac += "<A  id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + " class=menuParent  style=\"padding-left:14px;\" href='javascript:topwindowurl(\"" + menuPrimeKey + "\",\"" + menuName + "\",\"" + linkAction + "\")'  id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</A>";
            }
            cellSpac +="</TD> </TR> <TR height=4> <TD></TD> </TR> </TABLE>"
            levmenu += cellSpac;

            if (data[i].childrenMenuTreeNodeList) {
              var childTable = "<TABLE id=child"+menuPrimeKey+" class=\"menuSecound\" style=\"DISPLAY: none; width:100%\" cellSpacing=0 cellPadding=0 border=0>"
              var Lev2childrens = data[i].childrenMenuTreeNodeList;
              for (var j = 0; j < Lev2childrens.length; j++) {   
                var menuName = Lev2childrens[j].menuName;
                var linkAction = Lev2childrens[j].linkAction ? Lev2childrens[j].linkAction : "";
                var menuPrimeKey = Lev2childrens[j].menuPrimeKey;
                var menuSeq = Lev2childrens[j].menuSeq;
                var otherChildrens = getThirdOrMoreChild(Lev2childrens[j]);

                var secondChilds ="<TR height=28 >";
                  
       					if (otherChildrens && (otherChildrens != "")) {  //三级<a href='#' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a>
                   	secondChilds += "<TD style=\"text-align: left;padding-left:50px;\"> <A class=menuChild href='#'   id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</A> </TD> </TR>";
                	childTable +=secondChilds ;  
       					}else {
                  	if(linkAction.lastIndexOf("?")!=-1){
											linkAction += "&menuSeq="+menuSeq;
										}else{
											linkAction += "?menuSeq="+menuSeq;
										}
									secondChilds += "<TD style=\"text-align: left;padding-left:50px;\"> <A class=menuChild href='javascript:topwindowurl(\"" + menuPrimeKey + "\",\"" + menuName + "\",\"" + linkAction + "\")'   id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</A> </TD> </TR>";
                  childTable +=secondChilds ;
               	}
              }
              
              childTable +="<TR height=4> <TD colSpan=2></TD></TR> </TABLE>" 
              levmenu+=childTable;
            }
            
          }
          levmenu = "<TABLE height=\"100%\"  cellSpacing=0 cellPadding=0  border=0 style=\"width:100%;\" class=\"accordion \"><TR><TD vAlign=top align=middle>" + levmenu +"</TD></TR></TABLE>" ;
          return levmenu;
        }
        return "";
      };
      function getThirdOrMoreChild(lev2Childrens){
        var results = "";
        if(lev2Childrens.childrenMenuTreeNodeList){
          var childrens = lev2Childrens.childrenMenuTreeNodeList;
          for(var i = 0; i < childrens.length; i++){
            var menuName = childrens[i].menuName;
            var linkAction = childrens[i].linkAction ? childrens[i].linkAction : "";
            var menuPrimeKey = childrens[i].menuPrimeKey;
            var menuSeq = childrens[i].menuSeq;
            if(linkAction == ""){
              results +="<TABLE  id='chodes" + menuPrimeKey + "'  style=\"DISPLAY: none\" cellSpacing=0 cellPadding=0 width=150 border=0><TR height=20><TD align=middle width=30></TD><TD><span class=\"sanji\"  id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</span></TD></TR></TABLE>"
              // results += "<a class='lev3menu list-group-item faded' style='text-aling:left;' href='#' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a>";
            }else{
              results +="<TABLE  id='chodes" + menuPrimeKey + "'  style=\"DISPLAY: none\" cellSpacing=0 cellPadding=0 width=150 border=0><TR height=20><TD align=middle width=30></TD><TD><a class=\"sanji\"  href='javascript:topwindowurl(\""+menuPrimeKey+"\",\""+ menuName +"\",\""+linkAction+"\")' url='" + "<%=contextPath %>" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + "   id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></TD></TR></TABLE>"
              // results += "<a class='lev3menu list-group-item faded' style='text-aling:left;' href='javascript:topwindowurl(\""+menuPrimeKey+"\",\""+ menuName +"\",\""+linkAction+"\")' url='" + "<%=contextPath %>" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a>";
            }
            var ret = getThirdOrMoreChild(childrens[i]);
            if(ret){
              results += ret;
            }
          }
          if(results){
            return results;
          }
        }
      };

	function topwindowurl(id,name,url){
    	window.parent.ontabpagetourl(id,name,url);
	}
    </script>

    </HTML>
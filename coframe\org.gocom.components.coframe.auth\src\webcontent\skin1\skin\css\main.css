/*
* @Author: <PERSON>
* @Date:   2016-03-29 15:03:21
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-04-05 13:15:35
* @main.scss
*/
@import url(./bootstrap/css/bootstrap.min.css);
@import url(./FontAwesome/css/font-awesome.min.css);
/*
* @Author: <PERSON>
* @Date:   2016-03-29 15:15:25
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-03-29 15:22:59
* @hhq.scss
*/
/*
* @Author: <PERSON>
* @Date:   2016-03-29 15:19:34
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-03-30 18:07:32
* @global.scss
*/
.faed {
  transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -webkit-transition: all 0.4s; }

.blur {
  -webkit-filter: blur(5px);
  -moz-filter: blur(5px);
  -ms-filter: blur(5px);
  filter: blur(5px); }

ul, ol, li {
  list-style-type: none; }

p, a, h1, h2, h3, h4, h5, h6, span, em, i, li, div, span {
  font-family: "Microsoft YaHei"; }

h1, h2, h3, h4, h5, h6 {
  font-weight: 100;
  margin: 0; }

.h-menu-wrap {
  background-image: none; }

.navTop {
  background-color: #09c;
  min-height: 41px;
  margin: 0;
  position: relative;
  z-index: 5;
  padding-left: 0px;
  border: 0px; }

.h-main {
  transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -webkit-transition: all 0.4s;
  padding-top: 8px; }

.menuStart {
  height: 41px;
  border: 0px;
  border-radius: 0px;
  line-height: 41px;
  margin-left: 0px;
  padding: 0 15px; }
  .menuStart span {
    margin-right: 5px; }

.user {
  height: 100%; }
  .user li {
    line-height: 41px;
    height: 100%;
    border-right: 1px solid #31b0d5;
    text-align: center; }
    .user li a {
      display: block;
      float: left;
      height: 100%;
      width: 100px;
      margin: 0 5px;
      padding: 0 5px;
      border-right: 1px solid #31b0d5;
      padding: 0 10px;
      transition: all 0.4s;
      -o-transition: all 0.4s;
      -moz-transition: all 0.4s;
      -webkit-transition: all 0.4s; }
      .user li a:hover, .user li a:focus {
        text-decoration: none;
        background-color: #008fbf; }

#switchLogin, #updatepassword, .user .hendle .login-out, .user .name {
  color: #fff;
  background-image: none;
  margin: 0;
  padding: 0 10px; }

.nav-icon {
  margin-right: 6px;
  position: relative;
  top: 2px;
  color: #fff; }

#hmenu {
  background-color: #2e6da4;
  color: #fff;
  cursor: pointer; }
  #hmenu:hover {
    background-color: #286090; }

#levmenu, #lev2menu {
  border-top: 4px solid #3DA2E1;
  position: absolute;
  padding-bottom: 15px;
  display: none;
  background-color: #fff;
  box-shadow: 2px 2px 3px #999;
  border: 1px solid #eee;
  min-height: 400px;
  top: 41px;
  text-align: left;
  transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -webkit-transition: all 0.4s; }
  #levmenu a, #lev2menu a {
    color: #09c;
    font-weight: normal;
    white-space: pre-line; }
    #levmenu a:hover, #lev2menu a:hover {
      color: #FF7F24;
      text-decoration: none; }
  #levmenu dt, #lev2menu dt {
    cursor: pointer;
    transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -webkit-transition: all 0.4s; }
    #levmenu dt span, #lev2menu dt span {
      opacity: 0;
      float: right;
      padding-right: 10px;
      margin-left: 0px;
      padding-top: 8px;
      color: #C9C9C9;
      transition: all 0.4s;
      -o-transition: all 0.4s;
      -moz-transition: all 0.4s;
      -webkit-transition: all 0.4s; }
    #levmenu dt:hover, #lev2menu dt:hover {
      background-color: #eee; }
      #levmenu dt:hover span, #lev2menu dt:hover span {
        opacity: 1; }
      #levmenu dt:hover .lev2, #lev2menu dt:hover .lev2 {
        border-top: 1px solid #FF7F24; }
        #levmenu dt:hover .lev2 a, #lev2menu dt:hover .lev2 a {
          color: #FF7F24; }

#levmenu {
  left: 0px;
  min-width: 160px; }
  #levmenu dt {
    height: 32px;
    line-height: 32px;
    padding-left: 10px; }
  #levmenu a {
    display: block; }

#lev2menu {
  min-width: 250px;
  max-width: 500px;
  left: 160px; }
  #lev2menu dt {
    min-height: 32px;
    padding-bottom: 10px;
    line-height: 32px; }
    #lev2menu dt .lev2 {
      padding-left: 15px; }
    #lev2menu dt ul .lev2 {
      color: #FF7F24; }
    #lev2menu dt .lev3 a {
      padding: 0 5px;
      margin: 3px 0;
      display: inline-block;
      width: 140px;
      height: 18px;
      line-height: 18px;
      border-left: 2px solid #09c; }
      #lev2menu dt .lev3 a:hover {
        border-color: #FF7F24; }

.lev2 a, .lev2 {
  display: block;
  width: 100%; }

.lev2 {
  transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -webkit-transition: all 0.4s;
  border-top: 1px solid #eee; }
  .lev2:hover {
    border-top: 1px solid #FF7F24; }

.black {
  display: none;
  width: 100%;
  height: 100%;
  background-color: #fff;
  opacity: 0.5;
  position: absolute;
  top: 0;
  left: 0; }

.h-menu {
  margin-bottom: 0px;
  height: 41px; }

.nav_subcats_div {
  background-color: #EDEDED;
  border-left: 1px solid #DEDEDE;
  border-right: 2px solid #F7F7F7;
  height: 100%;
  left: 0;
  padding: 0 2px 0 0;
  position: absolute;
  width: 0; }

.dlCls {
  border-top: 1px solid #D4D4D4;
  position: absolute;
  bottom: 0px;
  text-align: center;
  padding: 0 20px;
  width: 100%; }
  .dlCls a {
    margin-top: 20px; }
    .dlCls a span {
      margin-right: 5px; }

.lev3 {
  padding-left: 10px; }

.aaa {
  float: left;
  height: 30px;
  line-height: 16px;
  font-size: 12px;
  font-weight: normal; }

.mini-tab {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background-color: #FFF;
  background-image: none;
  border-color: #ddd;
  transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  -webkit-transition: all 0.4s; }
  .mini-tab:hover {
    background-color: #eee; }

.mini-tabs-scrollCt {
  border-left: 0px;
  border-right: 0px;
  border-top: 0px;
  background-color: #fff;
  padding-top: 10px;
  color: #09c; }

.mini-tabs-headers {
  border: 0px;
  border-color: #fff;
  background-color: #FFF; }

.mini-tab-active {
  background-color: #eee;
  border-bottom: solid 1px #eee;
  color: #555; }

.mini-tabs-bodys {
  background-color: #eee;
  padding: 0; }

.mini-tabs-space, .mini-tabs-space2, .mini-tabs-bodys {
  border-color: #ddd; }

.mini-menu-horizontal .mini-menu-inner {
  background-color: #eee; }

.mini-menu-border {
  border: none; }

table, .mini-textbox-border, .mini-buttonedit-border, .mini-layout-region, .mini-grid-headerCell {
  border-color: #ddd !important; }

/*# sourceMappingURL=main.css.map */

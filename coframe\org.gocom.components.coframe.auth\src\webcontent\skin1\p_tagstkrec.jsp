﻿<%@page pageEncoding="UTF-8"%>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
	<%@include file="/coframe/tools/skins/common.jsp" %>

	<title>
		<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%>
	</title>
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/css/bootstrap.min.css" />
	<link href="<%=contextPath%>/coframe/auth/skin1/css/font-awesome/css/font-awesome.css" type="text/css" rel="stylesheet" />
	<link href="<%=contextPath%>/coframe/auth/skin1/css/font-awesome/css/components-rounded.min.css" type="text/css" rel="stylesheet"
	/>
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/css/style.min.css" />
	<!--<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/css/calendar.css" />-->
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/Notice/css/main.css" />
	<link rel="stylesheet" type="text/css" href="<%=contextPath%>/coframe/auth/skin1/date/css/date.css" />

	<style>
		#wrapper {
			background-color: #fff;
		}

		.img-thumbnail,
		body {
			background-color: #eee;
			font-size: 12px;
		}
        #lefttips{
			font-size: 20px;
		}
        #lefttips p{
			font-size: 16px;
		}
		.tableform td {
			border: 1px solid #000;
			color: #000;
			padding: 5px 8px;
			text-align: center;
		}

		.tableform thead td {
			font-weight: 700;
			background-color: #defdfd!important;
		}

		.tableform>thead>tr>td {
			padding: 10px 8px;
		}

		.table-responsive {
			padding-right: 0px;
		}

		.tableform {
			border: 2px solid #888;
		}

		.tooltiradius {
			display: inline-block;
			width: 10px;
			height: 10px;
			border-radius: 50%;
			margin-right: 5px;
			background: #87eba2;
			vertical-align: center;
		}
		#lefttips{
            color:#fff;
			padding: 30px 0 0 30px;
		}
	</style>
	</head>

	<body style="background:#fff;">
		<div class="wrapper wrapper-content">
			<div class="row" id="form" style="margin-bottom:20px;">
				<div class="col-sm-3">
					<div class="input-group">
						<input type="text" class="form-control" placeholder="请输入标签码" style="border-color:#888;" id="searchCode">
						<span class="input-group-btn" style="left:25px;">
							<button class="btn btn-success" type="button" onclick="search()">搜索</button>
						</span>
					</div>
				</div>
			</div>
			<div class="row" style=" background:url(images/timg.jpg) no-repeat 0 0;background-size:cover; margin: 0 0 10px 0;">
				<div class="col-sm-4">
					 <div id="lefttips"></div>
				</div>
				<div class="col-sm-8" style="position:  relative;padding-bottom:50px;">
					<div id="main" style="width:100%;"></div>
				</div>
				</div>
				<div class="row">
				<div class="col-sm-12">
						<!-- <table id="table"  data-mobile-responsive="true"></table> -->
						<table id="table" class="table tableform table-bordered">
							<thead>
								<td>标签码</td>
								<td>商品编码</td>
								<td>商品名称</td>
								<td>规格</td>
								<td>业务ID</td>
								<td>业务状态</td>
								<td>提交状态</td>
								<td>操作人</td>
								<td>部门名称</td>
								<td>生成时间</td>
							</thead>
							<tbody id="insoutstr">
							</tbody>
						</table>
					</div>
			</div>
		</div>
	</body>
	<script src="<%=contextPath%>/coframe/auth/skin1/js/jquery.min.js"></script>
	<script src="<%=contextPath%>/coframe/auth/skin1/js/bootstrap.min.js"></script>
	<script src="<%=contextPath%>/coframe/auth/skin1/Notice/js/main.js"></script>
	<script src="<%=contextPath%>/coframe/auth/skin1/echarts/echarts.js"></script>
	<script src="<%=contextPath%>/coframe/auth/skin1/echarts/echarts-demo.min.js"></script>
	<script>   
		var BIData = [];
		var iframeHeight = document.body.offsetHeight;
		// $('#table').height(iframeHeight - 124);
		$('#main').height(iframeHeight - 284);
		// initTable(BIData);
		BIData = window.parent.commonselect({
			name: 'searchTagrec',
			sendData: { "param": JSON.stringify({ TagCode: '011266344' }) }
		});
		newsoutlist(BIData, "#insoutstr", "main");
		function search() {
			var searchCode = $('#searchCode').val();
			BIData = window.parent.commonselect({
				name: 'searchTagrec',
				sendData: { "param": JSON.stringify({ TagCode: searchCode }) }
			});
			newsoutlist(BIData, "#insoutstr", "main");
		}
		function initTable(tableData) {
			var opt = { silent: true }
			$("#table").bootstrapTable('refresh');
			$('#table').bootstrapTable({
				striped: true, //是否显示行间隔色
				cache: false, //是否使用缓存，默认为true，所以一般情况下需要设置一下这个属性（*）
				pagination: true, //是否显示分页（*）
				sortable: false, //是否启用排序
				sortOrder: "asc", //排序方式
				// queryParams: oTableInit.queryParams,//传递参数（*）
				sidePagination: "client", //分页方式：client客户端分页，server服务端分页（*）
				pageNumber: 1, //初始化加载第一页，默认第一页
				pageSize: 10, //每页的记录行数（*）
				pageList: [10, 20, 50, 100], //可供选择的每页的行数（*）
				columns: [{
					field: 'tagcode',
					title: '标签码'
				}, {
					field: 'articd',
					title: '商品编码'
				}, {
					field: 'flname',
					title: '商品名称'
				}, {
					field: 'busseventid',
					title: '业务ID'
				}, {
					field: 'busseventiddesc',
					title: '业务状态'
				}, {
					field: 'bussstatus',
					title: '提交状态'
				}, {
					field: 'empname',
					title: '操作人'
				}, {
					field: 'deptname',
					title: '部门名称'
				}],
				data: tableData
			});
		}
		function slicestr(str) {
			var num = str.indexOf('.');
			if (!num) {
				return str;
			} else {
				return str.slice(0, num + 3);
			}
		}
		function newChart(data, id) {
			var myChart = echarts.init(document.getElementById(id));
			option = {
				title: {
					text: '标签码履历图',
					textStyle: {
						color: '#ffffff',
						fontSize: 20,
						lineHeight: 22,
					},
					left: 'center',
					top: 30
				},
				grid: {
					bottom: 90,
					right: '5%',
					left: '20%'
				},
				tooltip: {
					trigger: 'axis',
					formatter: function (params) {
						if (params) {
							let res = '';
							let obj = {};
							for (let i = 0; i < data.insoutobj.length; i++) {
								if (params[0].axisValue == data.insoutobj[i].adddate) {
									obj = data.insoutobj[i];
									res = '<span><span class="tooltiradius"></span>业务ID' + ':' + obj.busseventid + '</span></br>'
										+ '<span><span class="tooltiradius"></span>业务状态' + ':' + obj.busseventiddesc + '</span></br>'
										+ '<span><span class="tooltiradius"></span>提交状态' + ':' + obj.bussstatus + '</span></br>'
										+ '<span><span class="tooltiradius"></span>操作人' + ':' + obj.empname + '</span></br>'
										+ '<span><span class="tooltiradius"></span>部门' + ':' + obj.deptname + '</span></br>'
										+ '<span><span class="tooltiradius"></span>时间' + ':' + obj.adddate + '</span>'
								}
							}
							return res;
						}
					},
				},
				xAxis: {
					type: 'category',
					data: data.insoutX,
					boundaryGap: false,
					axisLabel: {
						interval: 0,
						rotate: 40,
						color: '#ffffff'
					},
					axisLine: {
						lineStyle: {
							color: '#ffffff'
						}
					}
				},
				yAxis: {
					type: 'category',
					axisLabel: {
						color: '#ffffff',
						fontSize:14,
					},
					axisLine: {
						lineStyle: {
							color: '#ffffff'
						}
					}
				},
				series: [{
					data: data.insoutY,
					type: 'line',
					lineStyle: {
						color: 'orange',
						width:3
					},
					symbolSize: 8,
					itemStyle: {
						color: '#87eba2',
						borderWidth: '3'
					},
					label: {
						normal: {
							formatter: function (params) {
								if (params) {
									return data.insoutobj[params.dataIndex].busseventiddesc;
								}
							},
							show: true,
							position: 'top',
							color: '#f6ff0f',
							fontSize:16,
							fontWeight:600
						}
					},
				}]
			};
			// 使用刚指定的配置项和数据显示图表。
			myChart.setOption(option, { notMerge: true });
		}
		function newsoutlist(data, id, id1) {
			var insoutstr = '';
			var tipstr='';
			var insoutdata = {
				insoutX: [],
				insoutY: [],
				insoutobj: []
			};
			for (var i = 0; i < data.length; i++) {
				if (data[i].bussstatus == 10) {
					data[i].bussstatus = "已提交";
				} else {
					data[i].bussstatus = "未提交";
				}
				insoutdata.insoutobj.push(data[i]);
				insoutdata.insoutY.push(data[i].deptname);
				insoutdata.insoutX.push(data[i].adddate);
				insoutstr += "<tr>" +
					"<td>" + data[i].tagcode + "</td>" +
					"<td>" + data[i].articd + "</td>" +
					"<td>" + data[i].flname + "</td>" +
					"<td>" + data[i].spec + "</td>" +
					"<td>" + data[i].busseventid + "</td>" +
					"<td>" + data[i].busseventiddesc + "</td>" +
					"<td>" + data[i].bussstatus + "</td>" +
					"<td>" + data[i].empname + "</td>" +
					"<td>" + data[i].deptname + "</td>" +
					"<td>" + data[i].adddate + "</td>" +
					"</tr>"
			}
			tipstr='<h3>商品信息</h3>'
			      +'<p><span>标签码 :</span>'+insoutdata.insoutobj[0].tagcode+'</p>'
				  +'<p><span>HIS计费编码 :</span>'+insoutdata.insoutobj[0].feeitemcode+'</p>'
			      +'<p><span>商品名称 :</span>'+insoutdata.insoutobj[0].flname+'</p>'
			      +'<p><span>规格 :</span>'+insoutdata.insoutobj[0].spec+'</p>'
			      +'<p><span>生产厂商 :</span>'+insoutdata.insoutobj[0].mfname+'</p>';
			$("#lefttips").html(tipstr);
			$(id).html(insoutstr);
			newChart(insoutdata, id1);
		}
	</script>

	</html>
<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="TKTJSSOLogin" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******" state="stateless">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="泰康同济（武汉）医院-老版本" title="ouwen&#x9;22-5-9 下午12:03">
    <location x="30" y="300"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link6" name="link6" lineType="reference" isDefault="true" type="action" actionName="action0" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
      <process:validateRules onError="default"/>
      <process:inputParameters>
        <process:parameter description="" historyStateLocation="client" isArray="false" name="token" primitiveType="String"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="126" y="160"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="125" y="196"/>
    <figSize height="20" width="31"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="验证用户" displayName="TKTJOldLoginByUserId" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link1" name="link1" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">result</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="268" y="160"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginManager.TKTJOldLoginByUserId</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="token" type="query" value="String" valueType="Primitive" pattern="reference">token</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="retCode" type="query" value="Int" valueType="Primitive">result</process:outputVariable>
        <process:outputVariable id="1" name="msg" type="query" value="String" valueType="Primitive"/>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="251" y="195"/>
    <figSize height="20" width="61"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end1" name="登录界面" displayName="结束" collapsed="false" type="end" contextPath="" method="forward" uri="/coframe/auth/login/SSORedirect.jsp" variableUri="false">
    <targetConnections>link0</targetConnections>
    <location x="375" y="80"/>
    <size height="28" width="28"/>
    <nodeLabel>end1label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end1label" name="label" nodeType="label">
    <location x="359" y="116"/>
    <figSize height="20" width="61"/>
    <node>end1</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end2" name="主页" displayName="结束" collapsed="false" type="end" contextPath="" method="forward" uri="/coframe/auth/login/redirect.jsp">
    <targetConnections>link1</targetConnections>
    <location x="402" y="228"/>
    <size height="28" width="28"/>
    <nodeLabel>end2label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end2label" name="label" nodeType="label">
    <location x="401" y="264"/>
    <figSize height="20" width="31"/>
    <node>end2</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="ouwen" createTime="2021-09-11 16:06:57" date="2021-09-11Z" description="" name="ssoLogin" version="*******"/>
  <process:variables/>
  <process:inputParameters/>
</process:tPageFlow>

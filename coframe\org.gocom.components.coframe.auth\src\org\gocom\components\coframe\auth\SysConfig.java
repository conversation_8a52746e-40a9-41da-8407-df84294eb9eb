package org.gocom.components.coframe.auth;

import java.util.HashMap;

import com.eos.foundation.database.DatabaseExt;
import com.eos.system.annotation.Bizlet;

@Bizlet("")
public class SysConfig {
	public static boolean queryConfig(String configname) {
		boolean flag = false;
		HashMap<String, String> param = new HashMap();
		param.put("configname", configname);
		HashMap<String, String> map = null;

		try {
			Object[] datas = DatabaseExt.queryByNamedSql("default",
					"org.gocom.components.coframe.auth.queryConfig.queryConfig", param);

			for (int i = 0; i < datas.length; ++i) {
				map = (HashMap) datas[i];
			}

			String configValue = ((String) map.get("CONFIGVALUE")).toString();
			if ("是".equals(configValue)) {
				return true;
			}
		} catch (Exception var6) {
			var6.printStackTrace();
			flag = false;
		}

		return flag;
	}

	@Bizlet
	public static boolean queryDeptConfig(String configname, String deptid) {
		boolean flag = false;
		HashMap<String, String> param = new HashMap();
		param.put("configname", configname);
		param.put("deptid", deptid);
		HashMap<String, String> map = null;

		try {
			Object[] datas = DatabaseExt.queryByNamedSql("default",
					"org.gocom.components.coframe.auth.queryConfig.queryDeptConfig", param);

			for (int i = 0; i < datas.length; ++i) {
				map = (HashMap) datas[i];
			}

			if (map == null) {
				flag = false;
				return flag;
			}

			if ("0".equals(map.get("DEPTCONFIGTYPE") == null ? "" : ((String) map.get("DEPTCONFIGTYPE")).toString())) {
				if ("是".equals(map.get("CONFIGVALUE") == null ? "" : ((String) map.get("CONFIGVALUE")).toString())) {
					return true;
				}
			} else {
				Object[] datasdetail = DatabaseExt.queryByNamedSql("default",
						"org.gocom.components.coframe.auth.queryConfig.queryDeptConfigdetial", param);

				for (int i = 0; i < datasdetail.length; ++i) {
					map = (HashMap) datasdetail[i];
				}

				if ("是".equals(map.get("CONFIGVALUE") == null ? "" : ((String) map.get("CONFIGVALUE")).toString())) {
					return true;
				}
			}
		} catch (Exception var8) {
			var8.printStackTrace();
			flag = false;
		}

		return flag;
	}

	@Bizlet
	public static String queryConfigValue(String configname) {
		String configValue = null;
		HashMap<String, String> param = new HashMap<String, String>();
		param.put("configname", configname);
		HashMap<String, String> map = null;
		try {
			Object[] datas = DatabaseExt.queryByNamedSql("default",
					"org.gocom.components.coframe.auth.queryConfig.queryConfig", param);
			for (int i = 0; i < datas.length; i++) {
				map = (HashMap) datas[i];
			}
			configValue = map.get("CONFIGVALUE").toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return configValue;
	}
}

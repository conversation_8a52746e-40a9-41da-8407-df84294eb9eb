*,
*:before,
*:after{    
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

html, body
{
    margin:0;
    height:100%;
    padding:0;
    font-family:微软雅黑;
    font-size:13px; 
	overflow:hidden;
	/* ;
	position: relative;
    width: 100%;*/
}

a {
  text-decoration: none;
}

/* layout */

.navbar
{
    height:50px;
    background:#3c8dbc;
    border-bottom:0;
	z-index:1;
}

.sidebar
{
    position:absolute;
    width:225px;
    top:0;
    left:0;
    bottom:0;
    background:#42485b;
	padding-top:22px;
}

.main
{
    position:absolute;
    top:50px;
    left:225px;
    right:0;
    bottom:0;
    background:#ffffff;
}


/* header */

.navbar-brand
{
    width:225px;
    background:#367ea8;
    cursor:default;
    font-size: 23px;
    font-weight: bold;    
}
.navbar-brand, .navbar-brand:hover, .navbar-brand:focus
{
    color:#fff;
}

.navbar,
.navbar-nav > li > a
{
    color:#fff;    
}
.navbar-nav > li > a
{
    padding-top:16px;
}
.navbar-nav > li > a:hover,
.navbar-nav > li > a:focus,
.navbar-nav > li.active > a,
.navbar-nav li.open a.dropdown-toggle
{
    background:#367fa9;
}

.navbar-nav > li > a.userinfo
{
    padding-top:10px;
}
.userinfo > img {
    height: 35px;
    width: 35px;
    vertical-align: middle;
    border-radius: 50%;
    margin-right:4px;
}

.dropdown-menu
{    
    font-size:13px;
}
.dropdown-menu i
{
    min-width:18px;
    margin-right:2px;
}


/* compact */
.compact .sidebar,
.compact .navbar-brand
{
    width:55px;
}
.compact .main
{
    left:55px;
}

.navbar-brand-compact,
.compact .navbar-brand
{
    display:none;
}
.compact .navbar-brand-compact
{
    display:block;
}

.compact .menu-text,
.compact .menu-arrow
{
    display:none;
}

.compact .menu-title
{
    padding-left:0px !important;
    text-align:center;
    display: flex;
    line-height: 36px;
    height: 36px;
    align-items: center;
    justify-content: center;
}

/*.compact .has-children > a > .menu-icon:before
{
    font-size:16px;
    content: "\f0d7";
}
.compact .open > a > .menu-icon:before
{
    content: "\f0d8";
}*/

/* icontop */

.navbar-nav > li.icontop > a{
    padding:0 15px;
}
.icontop i
{
    padding-top: 4px;
    text-align: center;
    display: block;
    font-size: 25px;
    line-height: 25px;
    width: 100%;
    margin: 0;
}
.icontop span {
    text-align: center;
    display: block;
    line-height: 21px;
    width: 100%;
    margin: 0;
}

/* sidebar-toggle */ 
.sidebar-toggle 
{
    height:30px;
    line-height: 30px;
    color: #aeb9c2;
    background-color: #4a5064;
    font-size: 14px;
    text-align: center;
    cursor:pointer;
}
.sidebar-toggle .fa {
    margin-top: 10px;
}
.sidebar-toggle:hover .fa {
    color: #fff;
}


.menutip
{
    z-index:100;
}
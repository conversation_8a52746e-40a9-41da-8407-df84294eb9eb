/*
 * Copyright 2013 Primeton.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gocom.components.coframe.entityauth.pojo;

/**
 * 实体权限常量池
 * <AUTHOR> (mailto:<EMAIL>)
 */
public class ConstantPool {
	// 实体资源类型
	public static final String RES_TYPE = "entity_rule";
	// 实体规则类型
	public static final String RULE_TYPE = "cap_entity";
}

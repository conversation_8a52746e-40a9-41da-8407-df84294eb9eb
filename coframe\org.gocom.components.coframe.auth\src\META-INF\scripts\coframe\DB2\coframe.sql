drop table cap_partyauth;
drop table CAP_RESAUTH;
drop table CAP_ROLE;
drop table COMP_IP_ACCESS_RULES;
drop table COMP_WIN7_AUTO_START;
drop table COMP_WIN7_CONFIG;
drop table COMP_WIN7_CUSTOM_PICTURES;
drop table COMP_WIN7_ICONS;
drop table app_funcresource;
drop table app_function;
drop table app_funcgroup;
drop table app_application;
drop table app_menu;
drop table cap_ssouser;
drop table cap_user;
drop table org_emporg;
drop table org_empposition;
drop table org_empgroup;
drop table org_employee;
drop table org_group;
drop table org_groupposi;
drop table org_position;
drop table org_duty;
drop table org_organization;
drop table org_recent_visit;
create table CAP_RESAUTH (PARTY_ID varchar(64) not null, PARTY_TYPE varchar(64) not null, RES_ID varchar(255) not null, RES_TYPE varchar(64) not null, TENANT_ID varchar(64), RES_STATE varchar(512) not null, PARTY_SCOPE varchar(1) default '0', CREATEUSER varchar(64), CREATETIME timestamp, primary key (PARTY_ID, PARTY_TYPE, RES_ID, RES_TYPE));
create table CAP_ROLE (ROLE_ID varchar(64) not null, TENANT_ID varchar(64) not null, ROLE_CODE varchar(64) not null, ROLE_NAME varchar(64), ROLE_DESC varchar(255), CREATEUSER varchar(64), CREATETIME timestamp, primary key (ROLE_ID));
create table COMP_IP_ACCESS_RULES (RULES_ID varchar(255) not null, START_IP varchar(255), END_IP varchar(255), RULES_TYPE varchar(255), REMARK varchar(255), MAKERS_ID varchar(255), ADD_DATE varchar(255), ENABLED varchar(255), primary key (RULES_ID));
create table COMP_WIN7_AUTO_START (START_ID varchar(255) not null, MENU_ID varchar(255), START_DESC varchar(255), USER_ID varchar(255), primary key (START_ID));
create table COMP_WIN7_CONFIG (CONFIG_ID varchar(255) not null, BG_PICTURE_PATH varchar(255), USER_ID varchar(255), CONFIG_DATA clob(102400), OPEN_TYPE varchar(255), DEFAULT_MAX smallint, DEFAULT_WIDTH integer, DEFAULT_HEIGHT integer, DESK_STYLE varchar(255), EXT1 varchar(255), EXT2 varchar(255), EXT3 varchar(255), primary key (CONFIG_ID));
create table COMP_WIN7_CUSTOM_PICTURES (CUSTOM_ID varchar(255) not null, FILE_NAME varchar(255), USER_ID varchar(255), UPLOAD_TIME varchar(255), primary key (CUSTOM_ID));
create table COMP_WIN7_ICONS (ICON_ID varchar(255) not null, ICON_NAME varchar(255), ICON_TEXT varchar(255), ICON_PATH varchar(255), ICON_TITLE varchar(255), MENU_ID varchar(255), ICON_INDEX varchar(255), ICON_DESC varchar(255), USER_ID varchar(255), primary key (ICON_ID));
create table app_application (APPID numeric(10,0) not null, APPCODE varchar(32), APPNAME varchar(50), APPTYPE varchar(255), ISOPEN varchar(1), OPENDATE date, URL varchar(256), APPDESC varchar(512), MAINTENANCE numeric(10,0), MANAROLE varchar(64), DEMO varchar(512), INIWP varchar(1), INTASKCENTER varchar(1), IPADDR varchar(50), IPPORT varchar(10), APP_ID varchar(64), TENANT_ID varchar(64) not null, protocol_type varchar(64), primary key (APPID));
create table app_funcgroup (FUNCGROUPID numeric(10,0) not null, FUNCGROUPNAME varchar(40), GROUPLEVEL integer, FUNCGROUPSEQ varchar(256), ISLEAF varchar(1), SUBCOUNT numeric(10,0), APP_ID varchar(64), TENANT_ID varchar(64) not null, PARENTGROUP numeric(10,0), APPID numeric(10,0) not null, primary key (FUNCGROUPID));
create table app_funcresource (RESID numeric(10,0) not null, RESTYPE varchar(255), RESPATH varchar(256), COMPACKNAME varchar(40), RESNAME varchar(40), APP_ID varchar(64), TENANT_ID varchar(64) not null, FUNCCODE varchar(255), primary key (RESID));
create table app_function (FUNCCODE varchar(255) not null, FUNCNAME varchar(128) not null, FUNCDESC varchar(512), FUNCACTION varchar(256), PARAINFO varchar(256), ISCHECK varchar(1), FUNCTYPE varchar(255) default '1', ISMENU varchar(1), APP_ID varchar(64), TENANT_ID varchar(64) not null, FUNCGROUPID numeric(10,0), primary key (FUNCCODE));
create table app_menu (MENUID varchar(40) not null, MENUNAME varchar(40) not null, MENULABEL varchar(40) not null, MENUCODE varchar(40), ISLEAF varchar(1), PARAMETER varchar(256), UIENTRY varchar(256), MENULEVEL smallint, ROOTID varchar(40), DISPLAYORDER smallint, IMAGEPATH varchar(100), EXPANDPATH varchar(100), MENUSEQ varchar(256), OPENMODE varchar(255), SUBCOUNT numeric(10,0), APPID numeric(10,0), FUNCCODE varchar(255), APP_ID varchar(64), TENANT_ID varchar(64) not null, PARENTSID varchar(40), primary key (MENUID));
create table cap_partyauth (ROLE_TYPE varchar(64) not null, PARTY_ID varchar(64) not null, PARTY_TYPE varchar(64) not null, ROLE_ID varchar(64) not null, TENANT_ID varchar(64) not null, CREATEUSER varchar(64), CREATETIME timestamp not null, primary key (ROLE_TYPE, PARTY_ID, PARTY_TYPE, ROLE_ID));
create table cap_ssouser (OPERATOR_ID varchar(64) not null, TENANT_ID varchar(64), USER_ID varchar(64) not null, PASSWORD varchar(100), USER_NAME varchar(64), EMAIL varchar(128), STATUS varchar(16), UNLOCKTIME timestamp, LASTLOGIN timestamp not null, ERRCOUNT numeric(10,0), MACCODE varchar(255), IPADDRESS varchar(255), CREATEUSER varchar(64), CREATETIME timestamp not null, primary key (OPERATOR_ID));
create table cap_user (OPERATOR_ID numeric(18,0) not null, TENANT_ID varchar(64) not null, USER_ID varchar(64) not null, PASSWORD varchar(100), INVALDATE date, USER_NAME varchar(64), AUTHMODE varchar(255), STATUS varchar(16), UNLOCKTIME timestamp not null, MENUTYPE varchar(255), LASTLOGIN timestamp not null, ERRCOUNT numeric(10,0), STARTDATE date, ENDDATE date, VALIDTIME varchar(255), MACCODE varchar(128), IPADDRESS varchar(128), EMAIL varchar(255), CREATEUSER varchar(64), CREATETIME timestamp not null, primary key (OPERATOR_ID));
create table org_duty (DUTYID numeric(10,0) not null, DUTYCODE varchar(20), DUTYNAME varchar(30), PARENTDUTY numeric(10,0), DUTYLEVEL integer, DUTYSEQ varchar(256), DUTYTYPE varchar(255), ISLEAF varchar(10), SUBCOUNT numeric(10,0), REMARK varchar(256), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (DUTYID));
create table org_empgroup (GROUPID numeric(10,0) not null, EMPID numeric(10,0) not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (GROUPID, EMPID));
create table org_employee (EMPID numeric(10,0) not null, EMPCODE varchar(30), OPERATORID numeric(18,0), USERID varchar(30), EMPNAME varchar(50), REALNAME varchar(50), GENDER varchar(255), BIRTHDATE date, POSITION numeric(10,0), EMPSTATUS varchar(255), CARDTYPE varchar(255), CARDNO varchar(20), INDATE date, OUTDATE date, OTEL varchar(12), OADDRESS varchar(255), OZIPCODE varchar(10), OEMAIL varchar(128), FAXNO varchar(14), MOBILENO varchar(14), QQ varchar(16), HTEL varchar(12), HADDRESS varchar(128), HZIPCODE varchar(10), PEMAIL varchar(128), PARTY varchar(255), DEGREE varchar(255), MAJOR numeric(10,0), SPECIALTY varchar(1024), WORKEXP varchar(512), REGDATE date, CREATETIME date not null, LASTMODYTIME date not null, ORGIDLIST varchar(128), ORGID numeric(10,0), REMARK varchar(512), TENANT_ID varchar(64) not null, APP_ID varchar(64), WEIBO varchar(255), primary key (EMPID));
create table org_emporg (ORGID numeric(10,0) not null, EMPID numeric(10,0) not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (ORGID, EMPID));
create table org_empposition (POSITIONID numeric(10,0) not null, EMPID numeric(10,0) not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (POSITIONID, EMPID));
create table org_group (GROUPID numeric(10,0) not null, ORGID numeric(10,0), GROUPLEVEL integer, GROUPNAME varchar(50), GROUPDESC varchar(512), GROUPTYPE varchar(255), GROUPSEQ varchar(256), STARTDATE date, ENDDATE date, GROUPSTATUS varchar(255), MANAGER varchar(30), CREATETIME timestamp not null, LASTUPDATE date, UPDATOR numeric(10,0), ISLEAF varchar(1), SUBCOUNT numeric(10,0), TENANT_ID varchar(64) not null, APP_ID varchar(64), PARENTGROUPID numeric(10,0), primary key (GROUPID));
create table org_groupposi (GROUPID numeric(10,0) not null, POSITIONID numeric(10,0) not null, ISMAIN varchar(1), TENANT_ID varchar(64) not null, APP_ID varchar(64), primary key (GROUPID, POSITIONID));
create table org_organization (ORGID numeric(10,0) not null, ORGCODE varchar(32) not null, ORGNAME varchar(64), ORGLEVEL numeric(2,0) default 1, ORGDEGREE varchar(255), ORGSEQ varchar(512), ORGTYPE varchar(12), ORGADDR varchar(256), ZIPCODE varchar(10), MANAPOSITION numeric(10,0), MANAGERID numeric(10,0), ORGMANAGER varchar(128), LINKMAN varchar(30), LINKTEL varchar(20), EMAIL varchar(128), WEBURL varchar(512), STARTDATE date, ENDDATE date, STATUS varchar(255), AREA varchar(30), CREATETIME timestamp not null, LASTUPDATE timestamp not null, UPDATOR numeric(10,0), SORTNO integer, ISLEAF varchar(1), SUBCOUNT numeric(10,0), REMARK varchar(512), TENANT_ID varchar(64) not null, APP_ID varchar(64), PARENTORGID numeric(10,0), primary key (ORGID));
create table org_position (POSITIONID numeric(10,0) not null, POSICODE varchar(20), POSINAME varchar(128) not null, POSILEVEL numeric(2,0), POSITIONSEQ varchar(512) not null, POSITYPE varchar(255), CREATETIME date not null, LASTUPDATE date not null, UPDATOR numeric(10,0), STARTDATE date, ENDDATE date, STATUS varchar(255), ISLEAF varchar(1), SUBCOUNT numeric(10,0), TENANT_ID varchar(64) not null, APP_ID varchar(64), DUTYID numeric(10,0), MANAPOSI numeric(10,0), ORGID numeric(10,0), primary key (POSITIONID));
create table org_recent_visit (id varchar(32) not null, target_id varchar(32) not null, UserID varchar(32) not null, Frequency integer default 1 not null, LastTime timestamp not null, target_type varchar(32) not null, primary key (id));

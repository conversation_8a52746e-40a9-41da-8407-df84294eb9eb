<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="deleteApplications" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="tab页的列表多选删除" title="fangwl&#x9;13-3-9 下午3:55">
    <location x="42" y="225"/>
    <size height="100" width="190"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>loopstart0</targetNode>
    </sourceConnections>
    <location x="61" y="91"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link5</targetConnections>
    <location x="660" y="91"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="63" y="127"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="662" y="127"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tLoopStart" description="" id="loopstart0" name="循环" displayName="循环" grouped="true" type="loopstart" matchedName="loopend0" loopType="iterate">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopstart0</sourceNode>
      <targetNode>subprocess1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="207" y="93"/>
    <size height="24" width="24"/>
    <nodeLabel>loopstart0label</nodeLabel>
    <process:condition/>
    <process:iterate iterable="nodes" iterableElement="node"/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopstart0label" name="label" nodeType="label">
    <location x="207" y="125"/>
    <figSize height="12" width="25"/>
    <node>loopstart0</node>
  </nodes>
  <nodes xsi:type="process:tLoopEnd" id="loopend0" name="循环结束" displayName="循环结束" grouped="true" type="loopend" matchedName="loopstart0">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopend0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="493" y="93"/>
    <size height="24" width="24"/>
    <nodeLabel>loopend0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopend0label" name="label" nodeType="label">
    <location x="481" y="125"/>
    <figSize height="12" width="49"/>
    <node>loopend0</node>
  </nodes>
  <nodes xsi:type="model:GroupNode" id="group0" name="group" grouped="true" gstart="loopstart0" gend="loopend0">
    <location x="207" y="71"/>
    <size height="84" width="311"/>
    <chidren>loopend0</chidren>
    <chidren>loopstart0</chidren>
    <chidren>subprocess1</chidren>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="deleteApplication" displayName="deleteApplication" grouped="true" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>loopend0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="340" y="91"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="2" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.deleteApplication</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="nodeId" type="query" value="String" valueType="Primitive" pattern="reference">node/realId</process:inputVariable>
        <process:inputVariable id="1" name="nodeType" type="query" value="String" valueType="Primitive" pattern="reference">node/type</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="302" y="126"/>
    <figSize height="12" width="103"/>
    <node>subprocess1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-01 15:19:08" date="2013-03-01Z" description="" name="deleteApplication" version="6.3"/>
  <process:variables>
    <process:variable anyType="commonj.sdo.DataObject" description="" historyStateLocation="client" isArray="false" name="node"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input anyType="commonj.sdo.DataObject" description="" isArray="true" name="nodes"/>
  </process:inputs>
  <process:outputs/>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

/*
 * Copyright 2013 Primeton Technologies Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gocom.components.coframe.framework;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.NumberUtils;
import org.gocom.components.coframe.framework.application.AppApplication;
import org.gocom.components.coframe.framework.application.AppFunction;
import org.gocom.components.coframe.framework.constants.IAppConstants;
import org.gocom.components.coframe.tools.IconCls;

import com.eos.das.entity.IDASCriteria;
import com.eos.das.entity.criteria.CriteriaType;
import com.eos.das.entity.criteria.ExprType;
import com.eos.das.entity.criteria.impl.ExprTypeImpl;
import com.eos.data.datacontext.DataContextManager;
import com.eos.data.datacontext.IUserObject;
import com.eos.foundation.PageCond;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.spring.BeanFactory;
import com.eos.spring.DASDaoSupport;
import com.eos.system.logging.Logger;
import com.primeton.cap.TenantManager;
import commonj.sdo.DataObject;

/**
 * TODO 此处填写 class 信息
 *
 * <AUTHOR> (mailto:<EMAIL>)
 */
public class AppApplicationService extends DASDaoSupport implements IAppApplicationService{
	private Logger log = TraceLoggerFactory.getLogger(AppApplicationService.class);
	
	public void addAppApplication(AppApplication appApplication){
		getDASTemplate().getPrimaryKey(appApplication);
		appApplication.setTenant_id(TenantManager.getCurrentTenantID());
		try{
			getDASTemplate().insertEntity(appApplication);
		} catch (Throwable t) {
			log.error("Insert application [appid=" + appApplication.getAppid() + "] failure, please do the operation again or contact the sysadmin.", t);
		}
	}

	public void deleteAppApplication(AppApplication[] appApplications ){
		for(DataObject appApplication:appApplications){
			try{
				getDASTemplate().deleteEntityCascade(appApplication);
			} catch (Throwable t) {
				log.error("Delete application [appid=" + appApplication.get("appid") + "] failure, please do the operation again or contact the sysadmin.", t);
			}
		}
	}


	public void getAppApplication(AppApplication appApplication){
		getDASTemplate().expandEntity(appApplication);
	}


	public AppApplication[]  queryAppApplications(CriteriaType criteria, PageCond pageCond){
		criteria.set_entity(AppApplication.QNAME);
		
		//zly add start 20151209
		ExprType exprType1 = new ExprTypeImpl();  
		exprType1.set_op("=");  
	    exprType1.set_property("APPCODE");  
	    exprType1.set_value("wxsys"); 
	    List<ExprType> list = new ArrayList<ExprType>(); 
	    list.add(exprType1);
		
		criteria.set_expr(list);
		//zly add start 20151209
		
		IDASCriteria dasCriteria = getDASTemplate().criteriaTypeToDASCriteria(criteria);
		AppApplication[] results = getDASTemplate().queryEntitiesByCriteriaEntityWithPage(AppApplication.class, dasCriteria, pageCond);
		return results;
	}


    public void updateAppApplication(AppApplication appApplication){
	    //更新资源库
	    BeanFactory beanFactory = BeanFactory.newInstance();
	    IAppFunctionService functionService =  beanFactory.getBean("AppFunctionBean");
	    AppFunction[] functions = functionService.getFunctionsByAppId(appApplication.getAppid().toString());
	    try{
	    	getDASTemplate().updateEntity(appApplication);
		    functionService.updateResoucesBatch(functions);
	    } catch (Throwable t) {
			log.error("Update application [appid=" + appApplication.getAppid() + "] failure, please do the operation again or contact the sysadmin.", t);
		}
	    
    }

	public int countAppApplication(CriteriaType criteria) {
		criteria.set_entity(AppApplication.QNAME);
		IDASCriteria dasCriteria = getDASTemplate().criteriaTypeToDASCriteria(criteria);
		return getDASTemplate().count(dasCriteria);
	}

	public AppApplication[] queryAllAppApplications(CriteriaType criteria) {
		criteria.set_entity(AppApplication.QNAME);
		//zly add start 20151223
		IUserObject userObject2 = DataContextManager.current().getMUODataContext().getUserObject();
		Map<String, Object> attr = userObject2.getAttributes();
		DataObject empobject = (DataObject) attr.get("EmpObject");
	//	String appcode = (String) empobject.get("orgtype");
	//	criteria.set("_expr[1]/appcode", appcode);
	//	criteria.set("_expr[1]/_op", "=");
		//zly add start 20151223
		IDASCriteria dasCriteria = getDASTemplate().criteriaTypeToDASCriteria(criteria);
		AppApplication[] results = getDASTemplate().queryEntitiesByCriteriaEntity(AppApplication.class, dasCriteria);
		return results;
	}

	@SuppressWarnings("unchecked")
	public List<Map> getApplicationTree(DataObject[] application, DataObject[] functionGroup, DataObject[] function) {
		List<Map> treeList = new ArrayList<Map>();
		
		Map<String, Object> functionTreeMap = new HashMap<String, Object>();
		for (DataObject dataObject : application) {
			functionTreeMap.put(dataObject.get("appid").toString(), IAppConstants.TYPE_APPLICATION + "_" + dataObject.get("appid"));
		}
		for (DataObject dataObject : functionGroup) {
			functionTreeMap.put(dataObject.get("funcgroupid").toString(), IAppConstants.TYPE_FUNCGROUP + "_" + dataObject.get("funcgroupid"));
		}
		
		//构造applicationTree
		for(DataObject dataObject : application){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_APPLICATION + "_" + dataObject.get("appid"));
			map.put("realId", dataObject.get("appid"));
			map.put("text", dataObject.get("appname"));
			map.put("type", IAppConstants.TYPE_APPLICATION);
			map.put("iconCls", IconCls.APPLICATION);
			treeList.add(map);
		}
		/*//构造functiongroupTree
		for(DataObject dataObject : functionGroup){
				Map map = new HashMap();
				map.put("id", IAppConstants.TYPE_FUNCGROUP + "_" + dataObject.get("funcgroupid"));
				map.put("realId", dataObject.get("funcgroupid"));
				map.put("text", dataObject.get("funcgroupname"));                                                                                                                                                                                              
				map.put("type", IAppConstants.TYPE_FUNCGROUP);
				map.put("pid", IAppConstants.TYPE_APPLICATION + "_" + dataObject.getDataObject("appApplication").get("appid"));
				map.put("iconCls", IconCls.FUNCTION_GROUP_CLOSE);
				map.put("isLeaf", true);
				map.put("expanded", false);
				treeList.add(map);
		}*/
		
		for(DataObject dataObject : functionGroup){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_FUNCGROUP + "_" + dataObject.get("funcgroupid"));
			map.put("realId", dataObject.get("funcgroupid"));
			map.put("text", dataObject.get("funcgroupname"));
			map.put("type", IAppConstants.TYPE_FUNCGROUP);
			String functionSeq = (String)dataObject.get("funcgroupseq");
			String[] sequences = functionSeq.split("\\.");
			if(sequences.length > 2){
			    String parentId = sequences[sequences.length - 2];
				if (parentId != null && parentId.trim().length() > 0 && functionTreeMap.containsKey(parentId)) {
				     map.put("pid", functionTreeMap.get(parentId));
				} else {
				     map.put("pid", IAppConstants.TYPE_APPLICATION + "_" + dataObject.getDataObject("appApplication").get("appid"));
				}
			}else{
			    map.put("pid", IAppConstants.TYPE_APPLICATION + "_" + dataObject.getDataObject("appApplication").get("appid"));
			 }
			map.put("iconCls", IconCls.FUNCTION_GROUP_CLOSE);
			treeList.add(map);
		}
		//构造functionTree
		for(DataObject dataObject : function){
			Map map = new HashMap();
			map.put("id",IAppConstants.TYPE_FUNCTION + "_" + dataObject.get("funccode"));
			map.put("realId", dataObject.get("funccode"));
			map.put("text", dataObject.get("funcname"));
			map.put("type", IAppConstants.TYPE_FUNCTION);
			map.put("pid", IAppConstants.TYPE_FUNCGROUP + "_" + dataObject.getDataObject("appFuncgroup").get("funcgroupid"));
			map.put("iconCls", IconCls.MENU_ROOT);
			map.put(IAppConstants.FUNCTION_IS_CHECK, String.valueOf(dataObject.get("ischeck")));
			map.put("isLeaf", true);
			map.put("expanded", false);
			treeList.add(map);
		}
		return treeList;
	}

	@SuppressWarnings("unchecked")
	public List<Map> getApplicationRoot(DataObject[] application) {
		List<Map> nodeList = new ArrayList<Map>();
		Map rootMap = new HashMap();
		rootMap.put("id","root_root");
		rootMap.put("realId", "root");
		rootMap.put("text", IAppConstants.APP_TREE_ROOT);
		rootMap.put("type", "root");
		rootMap.put("iconCls", IconCls.APPLICATION_HOME);
		rootMap.put("isLeaf", false);
		rootMap.put("expanded", true);
		nodeList.add(rootMap);
		//构造应用List
		for(DataObject dataObject : application){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_APPLICATION + "_" + dataObject.get("appid"));
			map.put("realId", dataObject.get("appid"));
			map.put("text", dataObject.get("appname"));
			map.put("type", IAppConstants.TYPE_APPLICATION);
			map.put("pid", "root_root");
			map.put("iconCls", IconCls.APPLICATION);
			map.put("isLeaf", false);
			map.put("expanded", false);
			nodeList.add(map);
		}
		return nodeList;
	}
	
	@SuppressWarnings("unchecked")
	public List<Map> getApplicationNode(DataObject[] application) {
		List<Map> nodeList = new ArrayList<Map>();
		//构造应用List
		for(DataObject dataObject : application){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_APPLICATION + "_" + dataObject.get("appid"));
			map.put("realId", dataObject.get("appid"));
			map.put("text", dataObject.get("appname"));
			map.put("type", IAppConstants.TYPE_APPLICATION);
			map.put("pid", "root_root");
			map.put("iconCls", IconCls.APPLICATION);
			map.put("isLeaf", false);
			map.put("expanded", false);
			nodeList.add(map);
		}
		return nodeList;
	}
    
	@SuppressWarnings("unchecked")
	public List<Map> getFunctionGroupNode(DataObject[] functionGroup) {
		List<Map> nodeList = new ArrayList<Map>();
		//构造功能组List
		for(DataObject dataObject : functionGroup){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_FUNCGROUP + "_" + dataObject.get("funcgroupid"));
			map.put("realId",dataObject.get("funcgroupid"));
			map.put("text", dataObject.get("funcgroupname"));
			map.put("type", IAppConstants.TYPE_FUNCGROUP);
			map.put("pid", IAppConstants.TYPE_APPLICATION + "_"+dataObject.getDataObject("appApplication").get("appid"));
			map.put("iconCls", IconCls.FUNCTION_GROUP_CLOSE);
			map.put("isLeaf", false);
			map.put("expanded", false);
			nodeList.add(map);
		}
		return nodeList;
	}
	
	@SuppressWarnings("unchecked")
	public List<Map> getSubFunctionGroupAndFunctionNode(DataObject[] functionGroup, DataObject[] function) {
		List<Map> nodeList = new ArrayList<Map>();
		//构造子功能组List
		for(DataObject dataObject : functionGroup){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_SUBFUNCGROUP + "_" + dataObject.get("funcgroupid"));
			map.put("realId",dataObject.get("funcgroupid"));
			map.put("text", dataObject.get("funcgroupname"));
			map.put("type", IAppConstants.TYPE_SUBFUNCGROUP);
			map.put("pid", IAppConstants.TYPE_APPLICATION + "_" + dataObject.getDataObject("appFuncgroup").get("funcgroupid"));
			map.put("iconCls", IconCls.FUNCTION_GROUP_CLOSE);
			map.put("isLeaf", false);
			map.put("expanded", false);
			nodeList.add(map);
		}
		
		//构造功能List
		for(DataObject dataObject : function){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_FUNCTION + "_" + dataObject.get("funccode"));
			map.put("realId",dataObject.get("funccode"));
			map.put("text", dataObject.get("funcname"));
			map.put("type", IAppConstants.TYPE_FUNCTION);
			map.put("pid", IAppConstants.TYPE_FUNCGROUP + "_" + dataObject.getDataObject("appFuncgroup").get("funcgroupid"));
			map.put("iconCls", IconCls.MENU_ROOT);
			map.put("isLeaf", true);
			map.put("expanded", false);
			nodeList.add(map);
		}
		return nodeList;
	}

	@SuppressWarnings("unchecked")
	public List<Map> getFunctionNode(DataObject[] function) {
		List<Map> nodeList = new ArrayList<Map>();
		//构造功能List
		for(DataObject dataObject : function){
			Map map = new HashMap();
			map.put("id", IAppConstants.TYPE_FUNCTION + "_" + dataObject.get("funccode"));
			map.put("realId",dataObject.get("funccode"));
			map.put("text", dataObject.get("funcname"));
			map.put("type", IAppConstants.TYPE_FUNCTION);
			map.put("pid", IAppConstants.TYPE_FUNCGROUP + "_" + dataObject.getDataObject("appFuncgroup").get("funcgroupid"));
			map.put("iconCls", IconCls.MENU_ROOT);
			map.put("isLeaf", true);
			map.put("expanded", false);
			nodeList.add(map);
		}
		return nodeList;
	}

	@SuppressWarnings("deprecation")
	public void deleteApplicationById(String id) {
		AppApplication appApplication = AppApplication.FACTORY.create();
		appApplication.setAppid(NumberUtils.createBigDecimal(id));
		try{
			getDASTemplate().deleteEntityCascade(appApplication);
		} catch (Throwable t) {
			log.error("Delete application [appid=" + appApplication.getAppid() + "] failure, please do the operation again or contact the sysadmin.", t);
		}
		
	}
}

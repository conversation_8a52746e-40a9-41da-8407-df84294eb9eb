<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="saveDict.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" collapsed="false" nodeType="note" type="note" content="保存业务字典项&#xD;&#xA;&#xD;&#xA;包括：&#xD;&#xA;1.添加字典项&#xD;&#xA;2.添加子项&#xD;&#xA;3.修改字典项" title="陈鹏&#x9;13-11-29 下午3:33">
    <location x="95" y="412"/>
    <size height="136" width="226"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" collapsed="false" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <location x="123" y="313"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <location x="125" y="349"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" collapsed="false" nodeType="common" type="end">
    <targetConnections>link19</targetConnections>
    <targetConnections>link18</targetConnections>
    <targetConnections>link27</targetConnections>
    <location x="1054" y="165"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.dict.dict.EosDictEntry" name="data" type="query" valueType="DataObject">data</process:return>
      <process:return id="1" language="String" name="status" type="query" valueType="Primitive">status</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <location x="1056" y="201"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="新增" displayName="新增" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link20" name="link20" displayName="link5" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>transactioncommit0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link21" name="link21" displayName="link9" lineType="note" exception="java.lang.Exception" type="exception">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link22</targetConnections>
    <targetConnections>link16</targetConnections>
    <location x="624" y="165"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.insertEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="623" y="201"/>
    <figSize height="17" width="25"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="修改" displayName="修改" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="link6" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>transactioncommit0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link12" name="link12" displayName="link8" lineType="note" type="exception">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="624" y="313"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.updateEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="623" y="349"/>
    <figSize height="17" width="25"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tTransactionBegin" id="transactionbegin0" name="事务开始" displayName="事务开始" collapsed="false" type="transactionbegin">
    <sourceConnections xsi:type="process:tLink" description="" id="link14" name="修改字典项" displayName="修改字典类型" isDefault="true" type="transition">
      <sourceNode>transactionbegin0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link28" name="link28" displayName="连接线" type="transition">
      <sourceNode>transactionbegin0</sourceNode>
      <targetNode>invokePojo2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">data/action</process:leftOperand>
          <process:rightOperand type="literal">add</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link23</targetConnections>
    <location x="349" y="313"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionbegin0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionbegin0label" name="label" nodeType="label">
    <location x="339" y="349"/>
    <figSize height="17" width="49"/>
    <node>transactionbegin0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionCommit" id="transactioncommit0" name="事务提交" displayName="事务提交" collapsed="false" type="transactioncommit">
    <sourceConnections xsi:type="process:tLink" id="link10" name="link10" displayName="link7" isDefault="true" type="transition">
      <sourceNode>transactioncommit0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link11</targetConnections>
    <targetConnections>link20</targetConnections>
    <location x="769" y="165"/>
    <size height="28" width="28"/>
    <nodeLabel>transactioncommit0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactioncommit0label" name="label" nodeType="label">
    <location x="759" y="201"/>
    <figSize height="17" width="49"/>
    <node>transactioncommit0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionRollback" id="transactionrollback0" name="事务回滚" displayName="事务回滚" collapsed="false" type="transactionrollback">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="link10" isDefault="true" type="transition">
      <sourceNode>transactionrollback0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <targetConnections>link12</targetConnections>
    <targetConnections>link21</targetConnections>
    <location x="768" y="313"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionrollback0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionrollback0label" name="label" nodeType="label">
    <location x="758" y="349"/>
    <figSize height="17" width="49"/>
    <node>transactionrollback0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link19" name="成功" displayName="link12" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link10</targetConnections>
    <location x="914" y="165"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">success</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="916" y="201"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link18" name="失败" displayName="link11" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="914" y="313"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">fail</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="916" y="349"/>
    <figSize height="17" width="25"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link22" name="link22" displayName="link0" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="525" y="165"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">data/rank</process:from>
      <process:to type="query">rank</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">Integer.valueOf(rank) + 1</process:from>
      <process:to type="query">data/rank</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">data/seqno</process:from>
      <process:to type="query">seqno</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">data/dictid</process:from>
      <process:to type="query">dictid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">seqno + dictid + &quot;.&quot;</process:from>
      <process:to type="query">data/seqno</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="527" y="201"/>
    <figSize height="17" width="25"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="link13" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <targetConnections>link15</targetConnections>
    <location x="621" y="75"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="expression" pattern="reference">1</process:from>
      <process:to type="query">data/rank</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">data/dictid</process:from>
      <process:to type="query">dictid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">&quot;.&quot; + dictid + &quot;.&quot;</process:from>
      <process:to type="query">data/seqno</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="623" y="111"/>
    <figSize height="17" width="25"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link23" name="link23" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>transactionbegin0</targetNode>
    </sourceConnections>
    <targetConnections>link17</targetConnections>
    <location x="229" y="313"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">1</process:from>
      <process:to type="query">data/status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="231" y="349"/>
    <figSize height="17" width="25"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign5" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link27" name="已存在" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign5</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link26</targetConnections>
    <location x="914" y="30"/>
    <size height="28" width="28"/>
    <nodeLabel>assign5label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">exist</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign5label" name="label" nodeType="label">
    <location x="916" y="66"/>
    <figSize height="17" width="25"/>
    <node>assign5</node>
  </nodes>
  <nodes xsi:type="process:tTransactionCommit" id="transactioncommit1" name="事务提交" displayName="事务提交" collapsed="false" type="transactioncommit">
    <sourceConnections xsi:type="process:tLink" id="link26" name="link26" displayName="link24" isDefault="true" type="transition">
      <sourceNode>transactioncommit1</sourceNode>
      <targetNode>assign5</targetNode>
    </sourceConnections>
    <targetConnections>link25</targetConnections>
    <location x="761" y="30"/>
    <size height="28" width="28"/>
    <nodeLabel>transactioncommit1label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactioncommit1label" name="label" nodeType="label">
    <location x="751" y="66"/>
    <figSize height="17" width="49"/>
    <node>transactioncommit1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="查询字典项" displayName="查询字典项" collapsed="false" nodeType="common" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link25" name="link25" displayName="link7" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>transactioncommit1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link15" name="添加字典项" displayName="添加字典类型" isDefault="false" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:complexCondition>
          <process:code>exist == 0 &amp;&amp; data.get(&quot;parentid&quot;) == null</process:code>
        </process:complexCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link13" name="添加子项" displayName="添加子类型" isDefault="false" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:complexCondition>
          <process:code>exist == 0 &amp;&amp; data.get(&quot;parentid&quot;) != null</process:code>
        </process:complexCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link28</targetConnections>
    <location x="351" y="30"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.expandEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">data</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">exist</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="332" y="66"/>
    <figSize height="17" width="61"/>
    <node>invokePojo2</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-02 10:17:28" date="2013-12-02Z" description="" name="保存业务字典项" version="6.3"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" name="rank" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="seqno" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="dictid" primitiveType="String"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="exist" primitiveType="Int"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="业务字典项" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="data"/>
  </process:inputs>
  <process:outputs>
    <process:output description="业务字典项" isArray="false" modelType="org.gocom.components.coframe.dict.dict.EosDictEntry" name="data"/>
    <process:output description="" isArray="false" name="status" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

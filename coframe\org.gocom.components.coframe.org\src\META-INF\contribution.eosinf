<?xml version="1.0" encoding="UTF-8"?>
<contribution xmlns="http://www.primeton.com/xmlns/eos/1.0">
	<!-- MBean config -->
	<module name="Mbean">
		<!-- DataSourceMBean config -->
		<group name="DatasourceMBean">
			<configValue key="Type">config</configValue>
			<configValue key="Class">com.eos.system.management.config.mbean.Config</configValue>
			<configValue key="Handler">com.eos.common.connection.mbean.ContributionDataSourceConfigHandler</configValue>
			<configValue key="ConfigFileType">config</configValue>
		</group>
		<group name="ContributionLoggerMBean">
			<configValue key="Type">config</configValue>
			<configValue key="Class">com.eos.system.management.config.mbean.Config</configValue>
			<configValue key="Handler">com.eos.common.logging.mbean.LogConfigHandler</configValue>
			<configValue key="ConfigFileType">log</configValue>
		</group>
	</module>

	<!-- datasource config -->	
	<module name="DataSource">
		<group name="Reference">
			<!--
				the configuration below describes 
				the corresponding relationship between contribution datasource and application datasource, 
				multiple datasources can be defined. 
				the value 'default' of attibute 'key' denotes a contribution datasource 
				and the field value 'default' of 'configValue' node stands for an application datasource. 
			-->
			<configValue key="default">default</configValue>
		</group>
	</module>
	
	<!-- tag page config -->	
	<module name="AuthTab">
		<group name="org">
			<configValue key="title">机构</configValue>
			<configValue key="url">/coframe/org/organization/org_auth.jsp</configValue>
			<configValue key="order">40</configValue>
		</group>
		<group name="group">
			<configValue key="title">工作组</configValue>
			<configValue key="url">/coframe/org/group/group_auth.jsp</configValue>
			<configValue key="order">50</configValue>
		</group>
		<group name="position">
			<configValue key="title">岗位</configValue>
			<configValue key="url">/coframe/org/position/position_auth.jsp</configValue>
			<configValue key="order">30</configValue>
		</group>
		<group name="emp">
			<configValue key="title">员工</configValue>
			<configValue key="url">/coframe/org/employee/employee_auth.jsp</configValue>
			<configValue key="order">60</configValue>
		</group>
	</module>
	
</contribution>

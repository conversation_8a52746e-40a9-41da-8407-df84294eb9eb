package org.gocom.components.coframe.auth;

import java.util.HashMap;

import com.eos.foundation.database.DatabaseExt;

public class LoginMiss {
   private String userid; //用户id
   private int    missNum;//错误次数
   private String missTime;//限制时间
   private String missFlag;//是否限制登陆
   public String getMissFlag() {
	return missFlag;
}
public void setMissFlag(String missFlag) {
	this.missFlag = missFlag;
}

private HashMap<String,String> missinfo;
public String getUserid() {
	return userid;
}
public void setUserid(String userid) {
	this.userid = userid;
}
public int getMissNum() {
	return missNum;
}
public void setMissNum(int missNum) {
	this.missNum = missNum;
}
public String getMissTime() {
	return missTime;
}
public void setMissTime(String missTime) {
	this.missTime = missTime;
}

 public HashMap<String,String> getMissInfo(){
	 if(missinfo==null){
		 missinfo = new HashMap<String,String>();
	 }
	 missinfo.put("userid",this.getUserid() );
	 missinfo.put("missNum", String.valueOf(this.getMissNum()));
	 missinfo.put("missTime", this.getMissTime());
	 missinfo.put("missFlag", this.getMissFlag());
	 return this.missinfo;
 }
 
 public HashMap querymiss(String userid){
	 HashMap map = null;
	 HashMap<String,String> param = new HashMap<String,String>();
	 param.put("userid", userid);
	 try {
		 Object[] datas = DatabaseExt.queryByNamedSql("default", "org.gocom.components.coframe.auth.loginLog.queryMissInfoByuserid", param);
		 if(datas==null||datas.length<1){
             	this.saveMiss(userid);
             	return this.querymiss(userid);
		 }
		 for (int i = 0; i < datas.length; i++) {
			  map = (HashMap) datas[i];
		 }
	 } catch (Exception e) {
		 e.printStackTrace();
	 }
	 return map;
 }
 
 public HashMap queryPasswordInvaldate(String userid){
	 HashMap map = null;
	 HashMap<String,String> param = new HashMap<String,String>();
	 param.put("userid", userid);
	 try {
		 Object[] datas = DatabaseExt.queryByNamedSql("default", "org.gocom.components.coframe.auth.loginLog.queryPasswordInvaldate", param);
		 if(datas==null||datas.length<1){
             	this.saveMiss(userid);
             	return this.querymiss(userid);
		 }
		 for (int i = 0; i < datas.length; i++) {
			  map = (HashMap) datas[i];
		 }
	 } catch (Exception e) {
		 e.printStackTrace();
	 }
	 return map;
 }
 
 public boolean saveMiss(String userid){
	 HashMap<String,String> param = new HashMap<String,String>();
	 param.put("userid", userid);
	 boolean flag = false;
	 try {
		DatabaseExt.executeNamedSql("default", "org.gocom.components.coframe.auth.loginLog.addLoginMiss", param);
	    flag = true;
	 } catch (Exception e) {
		 
	}
	 return flag;
	 
 }
 
 public boolean updateMiss(){
	 boolean flag = false;
	 HashMap map =this.getMissInfo();
	 try {
		DatabaseExt.executeNamedSql("default", "org.gocom.components.coframe.auth.loginLog.updateLoginMiss", this.getMissInfo());
	    flag = true;
	 } catch (Exception e) {
		 
	}
	 return flag;
	 
 }
	
}

<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getXinYiUserInfo" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link3</targetConnections>
    <location x="350" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="String" name="res" type="query" valueType="Primitive">res</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="352" y="96"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="调用医院接口获取用户信息" displayName="getUserInfo" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="208" y="60"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="static" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.SSOLoginService.getUserInfo</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="ticket" type="query" value="java.lang.String" valueType="Java" pattern="reference">ticket</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">res</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="147" y="96"/>
    <figSize height="17" width="145"/>
    <node>invokePojo1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="ouwen" createTime="2021-07-09 13:45:31" date="2021-07-09Z" description="" name="getUserInfo" version="*******"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="ticket" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="res" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

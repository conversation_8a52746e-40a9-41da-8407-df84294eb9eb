<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="queryEmpInGroup" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="查询工作组下所有的员工" title="gouyl&#x9;13-10-13 下午6:50">
    <location x="15" y="255"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
    </sourceConnections>
    <location x="15" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link1</targetConnections>
    <location x="400" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.org.groupdataset.OrgEmpgroup[]" name="emps" type="query" valueType="Java">emps</process:return>
      <process:return id="1" language="Int" name="total" type="query" valueType="Primitive">total</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="17" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="402" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="查询岗位下所有的员工" displayName="queryEmpsInGroup" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="255" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.org.OrgEmpgroupService.queryEmpsInGroup</process:partner>
      <process:instance instanceName="OrgEmpgroupService"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userid</process:inputVariable>
      <process:inputVariable id="1" name="empname" type="query" value="java.lang.String" valueType="Java" pattern="reference">empname</process:inputVariable>
      <process:inputVariable id="2" name="groupid" type="query" value="java.lang.String" valueType="Java" pattern="reference">groupid</process:inputVariable>
      <process:inputVariable id="3" name="page" type="query" value="com.eos.foundation.PageCond" valueType="Java" pattern="reference">page</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.org.groupdataset.OrgEmpgroup[]" valueType="Java">emps</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="209" y="186"/>
    <figSize height="17" width="121"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="计算总数" displayName="countEmpsInGroup" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="133" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.org.OrgEmpgroupService.countEmpsInGroup</process:partner>
      <process:instance instanceName="OrgEmpgroupService"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userid</process:inputVariable>
      <process:inputVariable id="1" name="empname" type="query" value="java.lang.String" valueType="Java" pattern="reference">empname</process:inputVariable>
      <process:inputVariable id="2" name="groupid" type="query" value="java.lang.String" valueType="Java" pattern="reference">groupid</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.Integer" valueType="Java">total</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="123" y="186"/>
    <figSize height="17" width="49"/>
    <node>invokeSpring1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="yangyong" createTime="2013-03-12 09:31:39" date="2013-03-12Z" description="" name="queryEmpByPosition" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="userid" primitiveType="String"/>
    <process:input description="" isArray="false" name="empname" primitiveType="String"/>
    <process:input description="" isArray="false" name="groupid" primitiveType="String"/>
    <process:input description="" isArray="false" modelType="com.eos.foundation.PageCond" name="page"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="org.gocom.components.coframe.org.groupdataset.OrgEmpgroup" description="" isArray="true" name="emps"/>
    <process:output description="" isArray="false" name="total" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

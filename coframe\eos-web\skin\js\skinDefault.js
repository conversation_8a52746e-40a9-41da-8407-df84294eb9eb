/*
* @Author: <PERSON>
* @Date:   2016-03-30 14:50:21
* @Last Modified by:   <PERSON>
* @Last Modified time: 2016-03-30 14:50:36
*/

	nui.parse();
	//得到登录的userid
	var userid="sysadmin";

	function switchLogin(){ //下拉获得框部门
		var sendData={
			"userid": userid
		};
		mini.ajax({
			url:"org.gocom.components.coframe.org.employee.queryEmpOfOrg.biz.ext",
			data:sendData,
			type:"post",
			cache:false,
			async:false,
			success: function (result) {
				var deptName = result.deptName;
				document.getElementById("deptName").innerHTML = deptName;
        	 	var orgs = result.orgs;
		     	if(!orgs.length || orgs.length<1){
		     		mini.alert("当前登录用户没有可切换部门");
		     	}else{
			     	mini.get("winSwitchLogin").show();
			     	mini.get("deptGrid").load(sendData);
		     	}
	        }
        });
	}

	function SwitchCon(){
		var select = mini.get("deptGrid").getSelected();
    	mini.ajax({
			url:"org.gocom.components.coframe.org.employee.setEmpDept.biz.ext",
			data:{"org":select},
			type:"post",
			cache:false,
			async:false,
			success: function (result) {
				var flag = result.flag;
				if(flag=="-1"){
					mini.alert(result.message);
				}else if(flag=="1"){
					var tabs = mini.get("mainTabs");
	 		    	var tab = tabs.getTab(0);
	 		    	tabs.removeAll (tab);
	 		    	tabs.reloadTab(tab);
	 		    	mini.get("winSwitchLogin").hide();
				}
	        }
		});
	}

	function SwitchCan(){
		mini.get("winSwitchLogin").hide();
	}

	var date = new Date();
	var currentDate = date.getFullYear() + "年" + (date.getMonth()+ 1) + "月" + date.getDate() + "日";
	$("#currentData").text(currentDate);

	//主内容区
	var iframe = document.getElementById("mainframe");

	//获取菜单数据
	getMenuData();
	getBtnData();
	//监听修改密码链接点击事件
	$("#updatepassword").click(function(){
    	var jspUrl = $(this).attr("openJsp");
    	if(jspUrl){
    		nui.open({
    			url: jspUrl,
    			title:"修改密码",
    			width: "370px",
    			height: "200px"
    		});
    	}
    });

	//监听鼠标点击事件（用来隐藏菜单）
	$(document).click(function(e){
		LeaveLev1Menu(e);
	});

	//监听菜单按钮鼠标移上移下事件
	$("#hmenu").hover(function(){
		if($("#levmenu").css("display") == "none"){
			$(this).addClass("menuStartHover");
			showLev1Menu();
		}
	},function(e){
		leaveHMenu(e);
	}
	);

	//监听一级菜单区域鼠标移上移下事件
	$("#levmenu").hover(function(){
	},function(e){
		LeaveLev1Menu(e);
	}
	);

	//监听二级菜单区域鼠标移上移下事件
	$("#lev2menu").hover(function(){
	},function(e){
		LeaveLev2Menu(e);
	}
	);

	//展现二级以及更多级菜单
	function showLev2Menu(position){
		document.getElementById("lev2menu").style.display = 'block';
		document.getElementById("mainField").style.zIndex = "-1";
		if(lev2menu){
			var lev2menuHtml = lev2menu[position];
			document.getElementById("lev2menu").innerHTML = "<div class='nav_subcats_div'></div>" + lev2menuHtml;
		}
		var lev1menuHeight = $("#levmenu").css("height");
		var lev2menuHeight = $("#lev2menu").css("height");
		if(lev2menuHeight > lev1menuHeight){
			$("#levmenu").css("min-height", lev2menuHeight);
		}else{
			$("#lev2menu").css("min-height", lev1menuHeight);
		}
		//监听<a>标签点击事件，如果有url，则在主内容区展现url,如果有jspUrl，则跳转到该url界面
		$("a").click(function(){
	    	if($(this).attr("url")){
		    	var url = $(this).attr("url") ? $(this).attr("url") : "";
		    	var uid = this.id ? this.id : "";
		    	var text= this.text ? this.text : "";
		    	var urll = setIFrame(url);
		    	showTab(uid,text,urll);
		    	setIFrame(url);
		    	document.getElementById("levmenu").style.display = 'none';
	        	document.getElementById("lev2menu").style.display = 'none';
	        	document.getElementById("mainField").style.zIndex = "1";
	    	}
	    });

	    //二级菜单监听鼠标事件，选中时，字体颜色，粗细有变化
	    $("#lev2menu dt").hover(function(){
	    	$(this).find("a:first").addClass("dtSelected");
	    },function(){
	    	$(this).find("a:first").removeClass("dtSelected");
	    });
	    //三级以及更多级菜单第一个左侧不展现boder
	    if($(".lev3").children()){
			$(".lev3").each(function(){
				$(this).find("a").first().removeClass("Nav");
			});
		}
	}

	// //清除一级菜单的背景选中箭头
	// function removeArrow(){
	// 	$("#levmenu").children().each(function(){
	// 		if($(this).hasClass("menuArrow")){
	// 			$(this).removeClass("menuArrow");
	// 		}
	// 	});
	// }

	//展现一级菜单，并且监听鼠标在一级菜单区域上的出入事件
	function showLev1Menu(){
		document.getElementById("levmenu").style.display = 'block';
		document.getElementById("mainField").style.zIndex = "-1";
		// removeArrow();
		// $("#levmenu dt").hover(function(){
		// 		removeArrow();
		// 		$(this).addClass("menuArrow");
		// 		$(this).children().addClass("dtSelected");
		// 	},function (e) {
		// 		LeaveLev1Menu(e);
		// 		$(this).children().removeClass("dtSelected");
		// 	}
		// );
		var lev1menuNum = $("#levmenu").children().length - 1;
		if((lev1menuNum * 32 + 40) > 100){
			$("#levmenu").css("height", lev1menuNum * 32 + 100);
		}
	}

	//鼠标离开菜单开始按钮时，判断位置，并作相应处理（如隐藏一级、二级菜单等）
	function leaveHMenu(e){
		var x = e.clientX;
		var y = e.clientY;
		var pmenuDiv = document.getElementById("levmenu");
		if(pmenuDiv){
			var divx1 = pmenuDiv.offsetLeft;
	        var divy1 = pmenuDiv.offsetTop;
	        var divx2 = pmenuDiv.offsetLeft + pmenuDiv.offsetWidth;
	        var divy2 = pmenuDiv.offsetTop + pmenuDiv.offsetHeight;
	        if( x < divx1 || x > divx2 || y < divy1 || y > divy2){
	        	$("levmenu").style.display = 'none';
	        	if(document.getElementById("lev2menu")){
	        		document.getElementById("lev2menu").style.display = 'none';
	        	}
	        	document.getElementById("mainField").style.zIndex = "1";
	        	$("#hmenu").removeClass("menuStartHover");
	        }
		}
	}

	//鼠标离开一级菜单区域时，判断位置，并作相应处理（如隐藏一级、二级菜单等）
	function LeaveLev1Menu(e){
		var x = e.clientX;
		var y = e.clientY;
		var pmenuDiv = document.getElementById("levmenu");
		if(pmenuDiv){
			var divx1 = pmenuDiv.offsetLeft;
	        var divy1 = pmenuDiv.offsetTop;
	        var divx2 = pmenuDiv.offsetLeft + pmenuDiv.offsetWidth;
	        var divy2 = pmenuDiv.offsetTop + pmenuDiv.offsetHeight;
	        if( x < divx1 || x > divx2 || y < divy1 || y > divy2){
	        	pmenuDiv = document.getElementById("hmenu");
				if(pmenuDiv){
					var divx1 = pmenuDiv.offsetLeft;
			        var divy1 = pmenuDiv.offsetTop;
			        var divx2 = pmenuDiv.offsetLeft + pmenuDiv.offsetWidth;
			        var divy2 = pmenuDiv.offsetTop + pmenuDiv.offsetHeight;
			        if( x < divx1 || x > divx2 || y < divy1 || y > divy2){
			        	pmenuDiv = document.getElementById("lev2menu");
						if(pmenuDiv){
							var divx1 = pmenuDiv.offsetLeft;
					        var divy1 = pmenuDiv.offsetTop;
					        var divx2 = pmenuDiv.offsetLeft + pmenuDiv.offsetWidth;
					        var divy2 = pmenuDiv.offsetTop + pmenuDiv.offsetHeight;
					        if( x < divx1 || x > divx2 || y < divy1 || y > divy2){
					        	document.getElementById("levmenu").style.display = 'none';
					        	document.getElementById("lev2menu").style.display = 'none';
					        	document.getElementById("mainField").style.zIndex = "1";
					        	$("#hmenu").removeClass("menuStartHover");
					        }
						}
			        }
				}
	        }
		}
	}

	//鼠标离开二级菜单区域时，判断位置，并作相应处理（如隐藏一级、二级菜单等）
	function LeaveLev2Menu(e){
		var x = e.clientX;
		var y = e.clientY;
		var pmenuDiv = document.getElementById("lev2menu");
		if(pmenuDiv){
			var divx1 = pmenuDiv.offsetLeft;
	        var divy1 = pmenuDiv.offsetTop;
	        var divx2 = pmenuDiv.offsetLeft + pmenuDiv.offsetWidth;
	        var divy2 = pmenuDiv.offsetTop + pmenuDiv.offsetHeight;
	        if( x < divx1 || x > divx2 || y < divy1 || y > divy2){
	        	pmenuDiv = document.getElementById("levmenu");
				if(pmenuDiv){
					var divx1 = pmenuDiv.offsetLeft;
			        var divy1 = pmenuDiv.offsetTop;
			        var divx2 = pmenuDiv.offsetLeft + pmenuDiv.offsetWidth;
			        var divy2 = pmenuDiv.offsetTop + pmenuDiv.offsetHeight;
			        if( x < divx1 || x > divx2 || y < divy1 || y > divy2){
			        	document.getElementById("levmenu").style.display = 'none';
			        	document.getElementById("lev2menu").style.display = 'none';
			        	document.getElementById("mainField").style.zIndex = "1";
			        	$("#hmenu").removeClass("menuStartHover");
			        }
				}
	        }
		}
	}

	//获取菜单数据
	function getMenuData(){
		$.ajax({
			url: "org.gocom.components.coframe.auth.LoginManager.getMenuData.biz.ext",
			type: "POST",
			success: function(text){
				var treeNodes = text.treeNodes;
				setMenuData(treeNodes);
			}
		});
	}
	function getBtnData(){
		mini.ajax({
	    	url: "org.gocom.components.coframe.auth.LoginManager.getBtnData.biz.ext",
	        type: "post",
	        success: function (result) {
	        	results = result.result;
	        	if(results){
	        		var btnhtml="";
	        		for(var i=0; i<results.length; i++){
	        			var re=results[i];
	        			addTab(re.menuid,re.title,setIFrame(re.funccode));
	        			//btnhtml+="<input id='"+re.btnid+"' type='image' src='"+re.imgsrc+"' width='70px' heigth='70px'  />";
	        		}
	        		//$("#btn").html(btnhtml);
	        	}
	        }
	    });
	}

	//二级菜单数组，和一级菜单相关联，鼠标移到一级菜单时，数组里取出相应二级菜单展现
	var lev2menu = [ ];
	//根据取出的菜单数据拼出html
	function setMenuData(data){
		if(data){
			var levmenu = "";
			for(var i = 0; i < data.length; i++){
				var menuName = data[i].menuName;
				var linkAction = data[i].linkAction ? data[i].linkAction: "";
				var menuPrimeKey = data[i].menuPrimeKey;
				var menuSeq = data[i].menuSeq;
				if(linkAction == ""){
					levmenu += "<dt onmouseover='showLev2Menu(" + i + ")'><a url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a><span class='glyphicon glyphicon-chevron-right'></dt>";
				}else{
					levmenu += "<dt onmouseover='showLev2Menu(" + i + ")'><a href='#' url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a><span class='glyphicon glyphicon-chevron-right'></span></dt>";
				}
				var secondChilds = "";
				if(data[i].childrenMenuTreeNodeList){
					var Lev2childrens = data[i].childrenMenuTreeNodeList;
					for(var j = 0; j < Lev2childrens.length; j++){
						var menuName = Lev2childrens[j].menuName;
						var linkAction = Lev2childrens[j].linkAction ? Lev2childrens[j].linkAction : "";
						var menuPrimeKey = Lev2childrens[j].menuPrimeKey;
						var menuSeq = Lev2childrens[j].menuSeq;
						var otherChildrens = getThirdOrMoreChild(Lev2childrens[j]);
						if(otherChildrens && (otherChildrens != "")){
							otherChildrens = "<ul class='lev3'>" + otherChildrens + "</ul>";
							secondChilds += "<dt><ul><a class='lev2' url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></ul>" + otherChildrens + "</dt>";
						}else{
							secondChilds += "<dt><ul class='lev2'><a href='#' url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a></ul></dt>";
						}
					}
				}
				lev2menu[i] = secondChilds;
			}
			insertMenuToHtml(levmenu);
		}
	}

	//根据二级菜单获取其所有子孙数据并且拼成相应html
	function getThirdOrMoreChild(lev2Childrens){
		var results = "";
		if(lev2Childrens.childrenMenuTreeNodeList){
			var childrens = lev2Childrens.childrenMenuTreeNodeList;
			for(var i = 0; i < childrens.length; i++){
				var menuName = childrens[i].menuName;
				var linkAction = childrens[i].linkAction ? childrens[i].linkAction : "";
				var menuPrimeKey = childrens[i].menuPrimeKey;
				var menuSeq = childrens[i].menuSeq;
				results += "<a class='Nav' href='#' url='" + linkAction +"' id='" + menuPrimeKey + "' menuSeq='" + menuSeq + "'" + ">" + menuName + "</a>";
				var ret = getThirdOrMoreChild(childrens[i]);
				if(ret){
					results += ret;
				}
			}
			if(results){
				return results;
			}
		}
	}

	//将拼成的一级菜单html插入到页面中
	function insertMenuToHtml(levmenu){
		levmenu += "<dl class='dlCls'><a href='#' url='/coframe/auth/skin2/allmenu.jsp'>全部菜单</a></dl>"
		$("#levmenu").html(levmenu);
	}

	//在iframe中展现相应页面
	function setIFrame(url){
		var relative = url.substr(0,1);
		if(relative == "/"){
			url = "/default" + url;
		}
		return url;
		//iframe.src = url;
	}
	showindexTab();
	function showindexTab() {
 		var uid=1,text='首页',url='/default/coframe/auth/skin2/allmenu.jsp';
		var tabs = mini.get("mainTabs");
		var id = "tab$" +uid;
		var tab = tabs.getTab(id);
		if (!tab) {
			tab = {};
			tab._nodeid =uid;
			tab.name = id;
			tab.title = text;
			tab.showCloseButton = false;
			tab.url = url;
			tabs.addTab(tab);
		}
	    tabs.activeTab(tab);
	}
	function showTab(uid,text,url) {
		var nurl = url;
		var index = url.indexOf("GET_FR_URL");
	  	if(index!=-1){
	   		var urlarray =nurl.split("||");
	    	var key = urlarray[0];
	    	var json = urlarray[1];
	    	var sendData={"paramCentre":[json],"paramHand":key,"dbName":"default"};
	    	var nurl = "";
			mini.ajax({
			    	url: "com.wx.spd.SysRoute.callSysRoute.biz.ext",
			        data: sendData,
			        type: "post",
			        cache:false,
				    async:false,
				    contentType:'text/json',
			        success: function (result) {
			        		var re=result.result;
				    		var M_JSON_RETN = re.M_JSON_RETN;
				    		var M_SQL_RETN = re.M_SQL_RETN;
				    		var message	= result.message;
				    		if(M_JSON_RETN==null && M_SQL_RETN==null){
				    		}else if(M_SQL_RETN.sqlcd==1){
				    			nurl = M_SQL_RETN.url;
				    		}else if(M_SQL_RETN.sqlcd==-1){
				    			mini.alert(M_JSON_RETN.RETN_MSG);
				    		}
			        },
			        error: function (jqXHR, textStatus, errorThrown) {
			        	mini.alert(jqXHR.responseText);
			        }
			    });
		}

		var tabs = mini.get("mainTabs");
		var id = "tab$" +uid;
		var tab = tabs.getTab(id);
		if (!tab) {
			tab = {};
			tab._nodeid =uid;
			tab.name = id;
			tab.title = text;
			tab.showCloseButton = true;
			tab.url = nurl;
			tabs.addTab(tab);
		}
	    tabs.activeTab(tab);
	}
	function addTab(uid,text,url) {
		var tabs = mini.get("mainTabs");
		var id = "tab$" +uid;
		var tab = tabs.getTab(id);
		if (!tab) {
			tab = {};
			tab._nodeid =uid;
			tab.name = id;
			tab.title = text;
			tab.showCloseButton = false;
			tab.url = url;
			tabs.addTab(tab);
		}
	}
	function onTabsActiveChanged(e) {
        var tabs = e.sender;
		var tab = tabs.getActiveTab();
	}
	function ontabpagetourl(menuid,title,url){
		showTab(menuid,title,setIFrame(url));
	}
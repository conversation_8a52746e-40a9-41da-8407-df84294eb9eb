<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="querySubFuncGroup.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link3</targetConnections>
    <location x="652" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.framework.application.AppFuncgroup[]" name="data" type="query" valueType="Java">data</process:return>
      <process:return id="1" language="Int" name="total" type="query" valueType="Primitive">total</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="654" y="96"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="link1" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="208" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">parentgroupid</process:from>
      <process:to type="query">criteria/_expr[1]/appFuncgroup.funcgroupid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="210" y="96"/>
    <figSize height="12" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="查询子功能组" displayName="queryAppFuncgroups" collapsed="false" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="504" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.queryAppFuncgroups</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
      <process:inputVariable id="1" name="pageCond" type="query" value="com.eos.foundation.PageCond" valueType="Java" pattern="reference">page</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppFuncgroup[]" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="482" y="96"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="查询子功能组数" displayName="countAppFuncgroup" collapsed="false" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="link2" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="356" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.countAppFuncgroup</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out" type="query" value="int" valueType="Java">total</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="328" y="96"/>
    <figSize height="12" width="85"/>
    <node>invokeSpring0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-04 16:22:44" date="2013-03-04Z" description="" name="querySubFuncGroup" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" modelType="com.eos.foundation.PageCond" name="page"/>
    <process:input description="" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
    <process:input description="" isArray="false" name="parentgroupid" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="org.gocom.components.coframe.framework.application.AppFuncgroup" description="" isArray="true" name="data"/>
    <process:output description="" isArray="false" name="total" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

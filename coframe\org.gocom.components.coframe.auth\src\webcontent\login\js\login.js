/*
 * Copyright (c) 2001-2014,FineReport Inc, All Rights Reserved.
 */

/**
 * Coder: Sean
 * Date: 14-7-10
 * Time: 下午1:08
 */
$(function() {
    var imgOffsetX, imgOffsetY, loginImgWidth, loginImgHeight, scale;
    //报错蒙板
    var $mask = $('<div class="fs-login-errmask"/>');
    //用户名
    var $username = $('input.fs-login-username').attr("placeholder", "用户名").attr('title',"用户名");
    //密码
    var $password = $('input.fs-login-password').attr("placeholder", "密码").attr('title',"密码");
    $('input').focus(function(){
        $(this).parent().addClass('fs-login-input-focus');
        $mask.hide();
    }).blur(function(){
            $(this).parent().removeClass('fs-login-input-focus');
        });
    //是否保持登录状态
    var $keep = $('span.fs-login-remember').click(
        function(){
            $(this).toggleClass('fs-login-remember-selected');
        }
    );
    //登录按钮
    $('#fs-login-btn').click(
        function(){
            signIN();
        }
    );
    //绑定回车
    $(document).keydown(function(e){
        if(e.keyCode===13){
            signIN();
        }
    });
    /**
     * 初始化FS的登录背景图片
     */
    var initBackgroundImage = function () {
        var self = this;
        var url = "http://192.168.88.110:18090/spdtest01/coframe/auth/login/images/signin.jpg";

        var ran = new Date().getTime() + "" + (Math.random() * 1000);
        if ($('body').length > 0) {
                var loginImg = $('img.fs-login-img');
                loginImg.attr("src", url);
                loginImg.css({
                    "margin-left": "-" + imgOffsetX + "px",
                    "margin-top": "-" + imgOffsetY + "px",
                    width: loginImgWidth * scale + "px",
                    height: loginImgHeight * scale + "px"
                });

                var offset = $('#fs-login-scalebg').offset();
                var loginScaleBgImg = $('img.fs-login-scalebg-img');
                loginScaleBgImg.attr("src", url);
                loginScaleBgImg.css({
                    "margin-left": "-" + (imgOffsetX + offset.left) + "px",
                    "margin-top": "-" + (imgOffsetY + offset.top) + "px",
                    width: loginImgWidth * scale + "px",
                    height: loginImgHeight * scale + "px"
                });
            }
    };

    var calcBackgroundScale = function () {
        var windowWidth = document.body.clientWidth;
        var windowHeight = document.body.clientHeight;

        if (windowWidth / windowHeight >= loginImgWidth / loginImgHeight) {
            scale = windowWidth / loginImgWidth;
            imgOffsetX = 0;
            imgOffsetY = (loginImgHeight * scale - windowHeight) / 2;
        } else {
            scale = windowHeight / loginImgHeight;
            imgOffsetX = (loginImgWidth * scale - windowWidth) / 2;
            imgOffsetY = 0;
        }
    };

    var showErrorMsg = function($pos, msg){
        $mask.hide().insertAfter($pos).text(msg);
        $mask.click(function(){
            $(this).fadeOut();
            $pos.select();
        }).fadeIn();
    };

    var signIN = function(){
        $mask.hide();
        var user = $username.val();
        var pw = $password.val();
        //用户名为空
        if(user == ''){
            showErrorMsg($username,"用户名不能为空");
            return;
        }
        //密码为空
        if(pw == ''){
            showErrorMsg($password,"密码不能为空");
            return;
        }
        var json = {"userId":user,"password":pw};
      	nui.ajax({
         url:"org.gocom.components.coframe.auth.LoginManager.login.biz.ext",
         type:'post',
         data:json,
         success:function(text){
            var o = nui.decode(text);
            if(o.exception==null){
	           var ret = o.retCode;
	           if(ret==1){
	           	 var a="http://192.168.99.40:8080/login.aspx?username="+json.userId+"&password="+json.password;
	             location.href="<%=request.getContextPath() %>/coframe/auth/login/redirect.jsp?original_url=<%=original_url %>";
	           }else if(ret==0){
	           	showErrorMsg($password,"输入密码错误");
	           }else if(ret==-2){
                nui.alert("用户无权限登录，请联系系统管理员");
	           }else if(ret==-3){
                nui.alert(o.msg);
	           }else{
	           	showErrorMsg($username,"用户名不存在");
	           }
            }else{
                nui.alert("登录系统出错");
            }
         }
       });

    };
    initBackgroundImage();
    $username.focus();
    $(window).resize(function(){
        calcBackgroundScale();
        $('img.fs-login-img').css({
            "margin-left": "-" + imgOffsetX +"px",
            "margin-top": "-" + imgOffsetY +"px",
            width: loginImgWidth * scale + "px",
            height: loginImgHeight * scale + "px"
        });
        var offset = $('#fs-login-scalebg').offset();
        $('img.fs-login-scalebg-img').css({
            "margin-left": "-" + (imgOffsetX + offset.left) + "px",
            "margin-top": "-" + (imgOffsetY + offset.top) + "px",
            width: loginImgWidth * scale + "px",
            height: loginImgHeight * scale + "px"
        });
    });
});
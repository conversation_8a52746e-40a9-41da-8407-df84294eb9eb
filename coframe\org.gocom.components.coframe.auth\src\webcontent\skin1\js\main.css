/*@font-face {*/
    /*font-family: 'iconfont';  !* project id 1031128 *!*/
    /*src: url('//at.alicdn.com/t/font_1031128_7rtez1z4tv.eot');*/
    /*src: url('//at.alicdn.com/t/font_1031128_7rtez1z4tv.eot?#iefix') format('embedded-opentype'),*/
    /*url('//at.alicdn.com/t/font_1031128_7rtez1z4tv.woff2') format('woff2'),*/
    /*url('//at.alicdn.com/t/font_1031128_7rtez1z4tv.woff') format('woff'),*/
    /*url('//at.alicdn.com/t/font_1031128_7rtez1z4tv.ttf') format('truetype'),*/
    /*url('//at.alicdn.com/t/font_1031128_7rtez1z4tv.svg#iconfont') format('svg');*/
/*}*/
@font-face {font-family: 'iconfont';
    src: url('iconfont.eot');
    src: url('iconfont.eot?#iefix') format('embedded-opentype'),
    url('iconfont.woff') format('woff'),
    url('iconfont.ttf') format('truetype'),
    url('iconfont.svg#iconfont') format('svg');
}
.iconfont{
    font-family:"iconfont" !important;
    font-size:16px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;}
* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.text-blue{
    color:#00b7ee;
}
.link-text{
    color:#00b7ee;
    cursor: pointer;
}
.nav-header {
    display: block;
    padding: 3px 15px;
    font-size: 11px;
    font-weight: bold;
    line-height: 20px;
    color: #999;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    text-transform: uppercase;
}
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 99999999999999;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    background-color: #ffffff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, 0.2);
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    text-align:left;
}
.dropdown-menu.pull-right {
    right: 0;
    left: auto;
}
.dropdown-menu .divider {
    *width: 100%;
    height: 1px;
    margin: 9px 1px;
    *margin: -5px 0 5px;
    overflow: hidden;
    background-color: #e5e5e5;
    border-bottom: 1px solid #ffffff;
}
.dropdown-menu a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 20px;
    color: #333333;
    white-space: nowrap;
    text-decoration: none;
}
.dropdown-menu li > a:hover, .dropdown-menu li > a:focus, .dropdown-submenu:hover > a {
    color: #ffffff;
    text-decoration: none;
    background-color: #0088cc;
    background-color: #0081c2;
    background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
    background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
    background-image: -o-linear-gradient(top, #0088cc, #0077b3);
    background-image: linear-gradient(to bottom, #0088cc, #0077b3);
    background-repeat: repeat-x;
    filter: progid: dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}
.dropdown-menu .active > a, .dropdown-menu .active > a:hover {
    color: #ffffff;
    text-decoration: none;
    background-color: #0088cc;
    background-color: #0081c2;
    background-image: linear-gradient(to bottom, #0088cc, #0077b3);
    background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
    background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
    background-image: -o-linear-gradient(top, #0088cc, #0077b3);
    background-repeat: repeat-x;
    outline: 0;
    filter: progid
    : dximagetransform.microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0);
}
.dropdown-menu .disabled > a, .dropdown-menu .disabled > a:hover {
    color: #999999;
}
.dropdown-menu .disabled > a:hover {
    text-decoration: none;
    cursor: default;
    background-color: transparent;
}
.open {
    *z-index: 1000;
}
.open > .dropdown-menu {
    display: block;
}
.pull-right > .dropdown-menu {
    right: 0;
    left: auto;
}
.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
    border-top: 0;
    border-bottom: 4px solid #000000;
    content: "\2191";
}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 1px;
}
.dropdown-submenu {
    position: relative;
}
.dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: -1px;
    -webkit-border-radius: 0 6px 6px 6px;
    -moz-border-radius: 0 6px 6px 6px;
    border-radius: 0 6px 6px 6px;
}
.dropdown-submenu > .dropdown-menu.drop-left{
    left:-100%;
}
.dropdown-submenu:hover .dropdown-menu {
    display: block;
}
.dropdown-submenu > a:after {
    display: block;
    float: right;
    width: 0;
    height: 0;
    margin-top: 5px;
    margin-right: -10px;
    border-color: transparent;
    border-left-color: #cccccc;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    content: " ";
}
.dropdown-submenu:hover > a:after {
    border-left-color: #ffffff;
}
.dropdown .dropdown-menu .nav-header {
    padding-right: 20px;
    padding-left: 20px;
}
/**
 * 	Context Styles
 */

.dropdown-context .nav-header {
    cursor: default;
}
.dropdown-context:before, .dropdown-context-up:before {
    position: absolute;
    top: -7px;
    left: 9px;
    display: inline-block;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #ccc;
    border-left: 7px solid transparent;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    content: '';
}
.dropdown-context:after, .dropdown-context-up:after {
    position: absolute;
    top: -6px;
    left: 10px;
    display: inline-block;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #ffffff;
    border-left: 6px solid transparent;
    content: '';
}
.dropdown-context-up:before, .dropdown-context-up:after {
    top: auto;
    bottom: -7px;
    z-index: 9999;
}
.dropdown-context-up:before {
    border-right: 7px solid transparent;
    border-top: 7px solid #ccc;
    border-bottom: none;
    border-left: 7px solid transparent;
}
.dropdown-context-up:after {
    border-right: 6px solid transparent;
    border-top: 6px solid #ffffff;
    border-left: 6px solid transparent;
    border-bottom: none;
}
.dropdown-context-sub:before, .dropdown-context-sub:after {
    display: none;
}
.dropdown-context .dropdown-submenu:hover .dropdown-menu {
    display: none;
}
.dropdown-context .dropdown-submenu:hover > .dropdown-menu {
    display: block;
}

.compressed-context a{
    padding-left: 14px;
    padding-top: 0;
    padding-bottom: 0;
    font-size: 13px;
}
.compressed-context .divider{
    margin: 5px 1px;
}
.compressed-context .nav-header{
    padding:1px 13px;
}
.layui-table thead tr{
    background-color: #dfdede;
}
.font-12{
    font-size: 12px;!important;
}
.bg-white{
    background-color: #fff !important;
}
.layui-bg-maroon {
    background-color: #d81b60 !important;
}
.layui-bg-alicebule{
    background-color: #F0F8FF !important;
}
.layui-bg-aqua{
    background-color: #00FFFF !important;
}
.layui-bg-blueviolet{
    background-color: #8A2BE2 !important;
}
.layui-bg-Chocolate{
    background-color: #D2691E !important;
}
.layui-bg-Crimson{
    background-color: #DC143C !important;
}
.layui-bg-yellow{
    background-color: #f0ad4e !important;
}
.layui-bg-HotPink{
    background-color: #FF69B4 !important;
}
.layui-bg-63c6aa{
    background-color: #63c6aa !important;
}
.layui-bg-blue{
    background-color:#0073b7 !important;
}
.layui-bg-eblue{
    background-color: #90c4d4 !important;
}
.layui-bg-egreen{
    background-color: #8bc6a5 !important
}
.layui-bg-eyellow{
    background-color: #eddc8e !important;
}
.link-text:hover{
    text-decoration:underline;
    color: #fff;
}
.layui-row p{
    text-overflow: ellipsis;
    white-space:nowrap;
    overflow:hidden;
}
.form-control{
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d2d6de;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    -webkit-transition: border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    border-radius: 0;
    box-shadow: none;
}
img{
    vertical-align: middle;
    border: 0;
}
.layui-row img:not(.uploadImg){
    max-width: 88px;
    max-height: 88px;
}
.text-blue {
    color: #0073b7 !important;
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit;
}
.h4, .h5, .h6, h4, h5, h6 {
    margin-top: 10px;
    margin-bottom: 10px;
}
.h4, h4 {
    font-size: 18px;
}
.modal-body {
    position: relative;
    padding: 15px;
}
.mb5{
    margin-bottom: 5px;
}
.layui-layer-btn .layui-layer-btn2{
    float: left;
    background-color: #dd4b39;
    border-color:#d73925;
    color: #ffffff;
}
.text-red{
    color:#dd4b39 !important;
}
.text-green{
    color:#00a65a !important;
}
.text-default{
    color: #009688 !important;
}
.text-yellow{
    color:#f0ad4e !important;
}
input:disabled{
    background-color: #eee;
    opacity: 1;
    cursor: not-allowed;
}
.form-select{
    height: 30px;
    line-height: 30px;
    margin: 5px 5px 0;
    padding: 0 15px;
    border: 1px solid #dedede;
    background-color: #fff;
    color: #333;
    border-radius: 2px;
    font-weight: 400;
    cursor: pointer;
    text-decoration: none;
}
.form-input{
    height: 27px;
    line-height: 27px;
    margin: 5px 5px 0;
    padding: 0 15px;
    border: 1px solid #dedede;
    background-color: #fff;
    color: #333;
    border-radius: 2px;
    font-weight: 400;
    cursor: pointer;
    text-decoration: none;
}
.font-white{
    color: #ffffff;
}
.img-box {
    margin: 0;
}
.img-box li {
    width: 100px;
    height: 100px;
    margin: 5px;
    background-size: auto 100%;
    border-radius: 4px;
    border: 1px solid #ddd;
    display: inline-block;
    overflow: hidden;
    position: relative;
    transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
}
.img-box li img {
    width: 100%;
}
.img-box li:hover {
    border-color: #3c8dbc;
}
.img-box li:hover:last-child {
    border-color: #ddd;
}
.img-box li .btn-remove {
    position: absolute;
    top: 0px;
    right: 0px;
    padding: 2px 6px;
    cursor: pointer;
    color: #555;
    border-bottom-left-radius: 4px;
    transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -webkit-transition: all 0.2s ease-in-out;
}
.img-box li .btn-remove:hover {
    background-color: rgba(51, 51, 51, 0.45);
    color: #fff;
}
.img-box li.hide {
    display: none;
}
.img-box .btn-add {
    width: 100%;
    height: 100%;
    padding: 16px;
    font-size: 48px;
    color: #888;
}
.img-box .proof_radio label {
    margin-right: 15px;
}
.img-box .content {
    display: none;
}
.img-box .content.active {
    display: block;
}
.layui-btn-default{
    background-color: #f4f4f4;
    color: #444;
    border-color: #ddd;
}
.layui-btn-default:hover{
    color: #616161;
}
.icon-uploadimg{
    position: absolute;font-size: 60px;top: 35px;left: 22px;
}
.fr{
    float: right;
}
.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}
.hidden {
    display: none!important;
}
.list-group-item {
    position: relative;
    display: block;
    padding: 5px 10px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #009688;
}
a.list-group-item, button.list-group-item {
    color: #555;
}
a.list-group-item:focus, a.list-group-item:hover, button.list-group-item:focus, button.list-group-item:hover {
    color: #555;
    text-decoration: none;
    background-color: #f5f5f5;
}
.img-upload-btn{
    position: absolute;
    right:2px;
    z-index: 99;
}
.img-upload-btn a:hover{
    border: 1px solid #009688 !important;
}
.layui-bg-009688{
    background-color: #009688 !important;
}
button:disabled,button:disabled:hover{
    border: 1px solid #e6e6e6;
    background-color: #FBFBFB;
    color: #C9C9C9;
    cursor: not-allowed;
    opacity: 1;
}
.check-icon li:after {
    content: '';
    display: block;
    width: 100%;
    border-top: 2px dashed #ddd;
    position: absolute;
    top: 10px;
    z-index: 0;
}
:after, :before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.check-icon li i{
    z-index: 100;
    position: relative;
    font-size: 24px;
    background-color: #ffffff;
}
.text-muted {
    color: #777;
}
.text-gray{
    color: #c8ccd4;
}
.small-box {
    border-radius: 2px;
    position: relative;
    display: block;
    height: 160px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}
.small-box:nth-child(odd) {
    background: #d9edf7;
}
.small-box:nth-child(even) {
    background: #f9f9c5;
}
.small-box>.inner {
    padding: 10px;
}
.small-box>.small-box-footer {
    position: relative;
    text-align: center;
    padding: 3px 0;
    color: #fff;
    color: rgba(255,255,255,0.8);
    display: block;
    z-index: 10;
    background: rgba(0,0,0,0.1);
    text-decoration: none;
}
.inner h4 {
    height: 55px;
    overflow: hidden;
}

.inner h4 {
    text-align: center;
}
.pull-right{
    float: right;
}
.text-center{
    text-align: center;
}
.address_list li.active{
    color: #009688 !important;
}
.small-box>.inner {
    padding: 10px;
}
.small-box h3 {
    font-size: 38px;
    font-weight: bold;
    margin: 0 0 10px 0;
    white-space: nowrap;

    padding: 0;
}
.small-box h3, .small-box p {
    z-index: 5;
    color: #ffffff;
}

.box {
    position: relative;
    border-radius: 3px;
    background: #ffffff;
    border-top: 3px solid #d2d6de;
    margin-bottom: 20px;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}
.box.box-info {
    border-top-color: #00c0ef;
}
.box-header {
    color: #444;
    display: block;
    padding: 10px;
    position: relative;
}
.box-header.with-border {
    border-bottom: 1px solid #f4f4f4;
}
.box-header>.fa, .box-header>.glyphicon, .box-header>.ion, .box-header .box-title {
    display: inline-block;
    font-size: 18px;
    margin: 0;
    line-height: 1;
}
.box-body {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    padding: 10px;
}
.box.box-danger {
    border-top-color: #dd4b39;
}
.box.box-warning {
    border-top-color: #f39c12;
}
.box-footer {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top: 1px solid #f4f4f4;
    padding: 10px;
    background-color: #fff;
}
#proofForm .layui-form-item{
    margin-bottom: 0;
}
#proofForm tr th{
    width: 15%;
}
#proofForm tr td{
    width: 35%;
}
#proofForm p{
    white-space: normal;
}
#proofForms tr th{
    width: 15%;
}
#proofForms tr td{
    width: 35%;
}
#proofForms p{
    white-space: normal;
}

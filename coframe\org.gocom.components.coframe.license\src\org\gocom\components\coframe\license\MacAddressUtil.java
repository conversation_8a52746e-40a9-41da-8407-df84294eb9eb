package org.gocom.components.coframe.license;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * MAC地址获取工具类 (JDK 8+ 版本) - 优化版
 * 旨在获取一个稳定且唯一的物理MAC地址。
 *
 * 优化点：
 * 1. 增加常量定义，避免魔法值。
 * 2. 简化 `bytesToMacString` 方法，使用 StringBuilder 提升性能和可读性。
 * 3. 抽离复杂的过滤逻辑到独立的私有方法中，使Stream流操作更清晰。
 * 4. 移除冗余的 try-catch 块，统一异常处理。
 * 5. 使用常量集合来管理虚拟网卡关键字，便于维护。
 * 6. 增加私有构造函数，防止工具类被实例化。
 */
public final class MacAddressUtil {

    private static final int MAC_ADDRESS_LENGTH = 6;
    private static final String MAC_ADDRESS_SEPARATOR = "-";

    // 常见的虚拟网卡名称关键字 (小写)
    private static final Set<String> VIRTUAL_IF_KEYWORDS = new HashSet<>();
    static {
        VIRTUAL_IF_KEYWORDS.add("virtual");
        VIRTUAL_IF_KEYWORDS.add("vmware");
        VIRTUAL_IF_KEYWORDS.add("bridge");
        VIRTUAL_IF_KEYWORDS.add("docker");
        VIRTUAL_IF_KEYWORDS.add("tun");
        VIRTUAL_IF_KEYWORDS.add("tap");
        VIRTUAL_IF_KEYWORDS.add("wsl");
        VIRTUAL_IF_KEYWORDS.add("veth");
        VIRTUAL_IF_KEYWORDS.add("br-");
    }

    /**
     * 工具类不应被实例化。
     */
    private MacAddressUtil() {
    }

    /**
     * 将byte数组的MAC地址转换为标准的十六进制字符串表示。
     * 优化：使用 StringBuilder 替代 Stream，对于固定长度的小数组，通常更高效且代码更直观。
     *
     * @param macBytes MAC地址的byte数组
     * @return MAC地址的十六进制字符串表示，如果macBytes为null或长度不正确则返回null
     */
    public static String bytesToMacString(byte[] macBytes) {
        if (macBytes == null || macBytes.length != MAC_ADDRESS_LENGTH) {
            return null;
        }
        StringBuilder sb = new StringBuilder(MAC_ADDRESS_LENGTH * 3 - 1);
        for (int i = 0; i < macBytes.length; i++) {
            if (i > 0) {
                sb.append(MAC_ADDRESS_SEPARATOR);
            }
            sb.append(String.format("%02X", macBytes[i]));
        }
        return sb.toString();
    }

    /**
     * 获取所有网络接口的Stream流。
     *
     * @return NetworkInterface的Stream流，如果发生异常则返回空流。
     */
    private static Stream<NetworkInterface> getNetworkInterfacesStream() {
        try {
            // Collections.list() 是将 Enumeration 转换为 List 的标准方法
            return Collections.list(NetworkInterface.getNetworkInterfaces()).stream();
        } catch (SocketException e) {
            logError("获取网络接口列表失败", e);
            return Stream.empty();
        }
    }

    /**
     * 检查是否为有效的物理网卡。
     * 过滤条件：非回环、已启用、有合法的硬件地址、非虚拟网卡。
     */
    private static boolean isValidInterface(NetworkInterface ni) {
        try {
            if (ni.isLoopback() || !ni.isUp()) {
                return false;
            }
            byte[] hardwareAddress = ni.getHardwareAddress();
            if (hardwareAddress == null || hardwareAddress.length != MAC_ADDRESS_LENGTH) {
                return false;
            }
            return !isVirtualInterface(ni);
        } catch (SocketException e) {
            logError("检查网络接口状态失败: " + ni.getDisplayName(), e);
            return false;
        }
    }

    /**
     * 根据名称判断是否为虚拟网卡。
     */
    private static boolean isVirtualInterface(NetworkInterface ni) {
        String displayName = ni.getDisplayName().toLowerCase();
        String name = ni.getName().toLowerCase();
        return VIRTUAL_IF_KEYWORDS.stream().anyMatch(keyword ->
                displayName.contains(keyword) || name.contains(keyword) || name.startsWith(keyword));
    }

    /**
     * 获取第一个找到的、有效的MAC地址。
     * 顺序不确定，取决于操作系统返回的网卡顺序。
     *
     * @return 找到的第一个MAC地址字符串，如果未找到则返回null。
     */
    public static String getFirstActiveMacAddress() {
        return getNetworkInterfacesStream()
                .filter(MacAddressUtil::isValidInterface)
                .findFirst()
                .map(ni -> {
                    try {
                        return bytesToMacString(ni.getHardwareAddress());
                    } catch (SocketException e) {
                        logError("在 getFirstActiveMacAddress 中获取MAC地址失败", e);
                        return null;
                    }
                })
                .orElse(null);
    }

    /**
     * 获取一个最可能是“主物理网卡”的MAC地址。
     * 策略：
     * 1. 过滤掉无效和虚拟网卡。
     * 2. 对剩余网卡进行排序，优先选择绑定了公网IP的，其次是私网IP，最后按名称排序。
     * 3. 返回排序后的第一个网卡的MAC地址。
     *
     * @return 最优的物理MAC地址字符串，如果未找到则返回null。
     */
    public static String getPreferredMacAddress() {
        // 定义排序比较器，逻辑更清晰
        Comparator<NetworkInterface> physicalInterfaceComparator = Comparator
                // 优先选择有非回环IPv4地址的网卡 (0分)，没有的得1分，分数低的排前面
                .comparingInt((NetworkInterface ni) ->
                        hasNonLoopbackIp(ni) ? 0 : 1)
                // 分数相同，则按名称排序，保证结果稳定
                .thenComparing(NetworkInterface::getDisplayName);

        return getNetworkInterfacesStream()
                .filter(MacAddressUtil::isValidInterface)
                .sorted(physicalInterfaceComparator)
                .findFirst()
                .map(ni -> {
                    try {
                        return bytesToMacString(ni.getHardwareAddress());
                    } catch (SocketException e) {
                        logError("在 getPreferredMacAddress 中获取MAC地址失败", e);
                        return null;
                    }
                })
                .orElseGet(() -> {
                    // 如果以上策略失败，回退到简单策略
                    logError("首选策略未找到MAC地址，回退到简单查找...", null);
                    return getFirstActiveMacAddress();
                });
    }

    /**
     * 检查网卡是否绑定了非回环IP地址。
     */
    private static boolean hasNonLoopbackIp(NetworkInterface ni) {
        return Collections.list(ni.getInetAddresses()).stream()
                .anyMatch(addr -> !addr.isLoopbackAddress());
    }

    /**
     * 尝试通过 `InetAddress.getLocalHost()` 绑定的网卡来获取MAC地址。
     * 这种方法在网络配置简单的环境下很有效，但在复杂网络下（如多网卡、VPN）可能不准确。
     *
     * @return 找到的MAC地址字符串，如果未找到则返回null。
     */
    public static String getMacAddressByLocalHost() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            // getLocalHost 可能返回回环地址，需要处理这种情况
            if (localHost.isLoopbackAddress()) {
                // 尝试寻找一个非回环的地址
                Optional<InetAddress> nonLoopbackIp = findFirstNonLoopbackIp();
                if (nonLoopbackIp.isPresent()) {
                    localHost = nonLoopbackIp.get();
                } else {
                    logError("无法找到任何非回环IP地址", null);
                    return null;
                }
            }

            NetworkInterface ni = NetworkInterface.getByInetAddress(localHost);
            if (ni != null && isValidInterface(ni)) {
                return bytesToMacString(ni.getHardwareAddress());
            }
        } catch (UnknownHostException e) {
            logError("无法解析本地主机名", e);
        } catch (SocketException e) {
            logError("通过IP获取MAC地址时发生Socket异常", e);
        }
        return null;
    }

    /**
     * 查找第一个非回环的IP地址。
     */
    private static Optional<InetAddress> findFirstNonLoopbackIp() {
        return getNetworkInterfacesStream()
                .filter(ni -> {
                    try {
                        return !ni.isLoopback() && ni.isUp();
                    } catch (SocketException e) {
                        return false;
                    }
                })
                .flatMap(ni -> Collections.list(ni.getInetAddresses()).stream())
                .filter(addr -> !addr.isLoopbackAddress() && addr instanceof java.net.Inet4Address)
                .findFirst();
    }
    
    /**
     * 统一的错误日志记录。
     */
    private static void logError(String message, Throwable t) {
        System.err.println("MAC地址工具类错误: " + message);
        if (t != null) {
            // 在实际项目中，应使用日志框架如 SLF4J, Log4j2 等
            t.printStackTrace(System.err);
        }
    }


    public static void main(String[] args) {
        System.out.println("--- 尝试获取MAC地址 (优化版) ---");

        String mac1 = getFirstActiveMacAddress();
        System.out.println("getFirstActiveMacAddress(): " + (mac1 != null ? mac1 : "未找到"));

        String mac2 = getPreferredMacAddress();
        System.out.println("getPreferredMacAddress():   " + (mac2 != null ? mac2 : "未找到"));

        String mac3 = getMacAddressByLocalHost();
        System.out.println("getMacAddressByLocalHost(): " + (mac3 != null ? mac3 : "未找到"));

        System.out.println("\n--- 列出所有检测到的有效网络接口 ---");
        getNetworkInterfacesStream()
                .filter(MacAddressUtil::isValidInterface)
                .sorted(Comparator.comparing(NetworkInterface::getName))
                .forEach(ni -> {
                    try {
                        System.out.println("  接口: " + ni.getDisplayName() + " | MAC: " + bytesToMacString(ni.getHardwareAddress()));
                        Collections.list(ni.getInetAddresses()).forEach(addr ->
                                System.out.println("    IP: " + addr.getHostAddress()));
                    } catch (SocketException e) {
                        logError("处理接口 " + ni.getDisplayName() + " 时出错", e);
                    }
                });
    }
}

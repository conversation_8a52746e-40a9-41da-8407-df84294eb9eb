package org.gocom.components.coframe.auth.login;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.codec.EncoderException;
import org.apache.commons.codec.binary.Base64;

public class MD5 {
	public static final String MD5KEY = "Wx5515!!";

	public static String getMesKey(String data)
			throws NoSuchAlgorithmException, UnsupportedEncodingException,
			EncoderException {
		MessageDigest md5 = MessageDigest.getInstance("MD5");
		byte[] md5Bytes = md5.digest((data + MD5KEY).getBytes("utf-8"));
		StringBuffer hexValue = new StringBuffer();
		for (int i = 0; i < md5Bytes.length; i++) {
			int val = ((int) md5Bytes[i]) & 0xff;
			if (val < 16)
				hexValue.append("0");
			hexValue.append(Integer.toHexString(val));
		}
		return hexValue.toString();
	}

	/**
	 * base64转string
	 * 
	 * @param mesKey
	 * @return
	 */
	public static String getMD5rs(String mesKey) {
		Base64 base = new Base64();
		byte[] ba = null;
		try {
			ba = base.decode(mesKey.getBytes("utf-8"));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		String md5str = new String(ba);
		try {
			md5str = new DesUtils().decrypt(md5str, MD5KEY);
		} catch (Exception e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		return md5str;
	}

	/**
	 * 获取有效期和授权日期时间差
	 * 
	 * @param mesKey
	 * @return
	 */
	public static String getxqdate(String mesKey) {
		String md5str = MD5.getMD5rs(mesKey);
		return md5str.substring(md5str.indexOf("-") + 1, md5str.length());
	}

	static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

	/**
	 * 反向获取有效期
	 * 
	 * @param authdate
	 * @param xqdatexe
	 * @return
	 */
	public static Date getXqDateBytime(String authdate, String xqdatexe) {
		long time = 0;
		try {
			time = (format.parse(authdate).getTime()) + Long.valueOf(xqdatexe);
		} catch (NumberFormatException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		} catch (ParseException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		return new Date(time);
	}

	/**
	 * 
	 * @param data 当前时间 + (项目名 + MAC地址) + 有效期限 + 秘钥
	 * @param meskey 授权码
	 * @return
	 */
	public static boolean keyCheck(String data, String meskey) {
		try {
			// 将base64的授权码转为string
			String meskeytomd5 = MD5.getMD5rs(meskey);
			if (MD5.getMesKey(data).equals(
					meskeytomd5.substring(0, meskeytomd5.indexOf("-")))) {
				return true;
			}
		} catch (NoSuchAlgorithmException | UnsupportedEncodingException
				| EncoderException e) {
			e.printStackTrace();
			return false;
		}
		return false;
	}

	/**
	 * 校验时间
	 * 
	 * @param authdate
	 * @param meskey
	 * @return
	 */
	public static boolean datecheck(String authdate, String meskey) {
		String xqdatex = MD5.getxqdate(meskey);
		// System.out.println("xqdatex"+xqdatex);
		long yxqdate = MD5.getXqDateBytime(authdate, xqdatex).getTime();
		// System.out.println("yxqdate"+yxqdate);
		long auth = new Date().getTime();
		// System.out.println("auth"+auth);
		try {
			String istrdate = EOSlock.getIstruedate() == null ? format
					.format(new Date()) : EOSlock.getIstruedate();
			auth = format.parse(istrdate).getTime();
		} catch (ParseException e) {
			e.printStackTrace();
			return false;
		}
		return yxqdate - auth > 0 ? true : false;
	}

	public static void main(String[] args) throws NoSuchAlgorithmException,
			UnsupportedEncodingException, EncoderException {
		System.out.println(MD5.getMesKey("sdfasdfasdfasf"));
		Base64 base = new Base64();
		byte[] ba = base.decode("ZmY4OWJhOWI5MzU2MGI4NDcxNjMxZTgxYTZlMjc5Y2Q="
				.getBytes("utf-8"));
		String a = new String(ba);
		System.out.println(a);
		String s = MD5
				.getxqdate("MmNiZmJkNDI3ZDI1OWE1NjA5MGYxODNiOTYxNDFmYTktMTcyODAwMDAw");
		System.out.println(s);

		String meskeytomd5 = MD5
				.getMD5rs("MjU1NjhlNzk4ZWMwODhhYTZlYTI2YzBlYTM0Mjg4ZmQtNDUyOTk1MjAwMDAw");
		System.out.println(meskeytomd5);
		System.out.println(meskeytomd5.substring(0, meskeytomd5.indexOf("-")));
	}
}

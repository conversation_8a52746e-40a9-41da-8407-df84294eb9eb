<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<%@ page import="com.wx.utils.publicMemer.Session.SessionUtil" %>
<%@ page import="com.primeton.cap.AppUserManager" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!--
  - Author(s): ouwen
  - Date: 2022-05-17 17:58:13
  - Description: 帆软8.0单点登录集成
-->
<head>
<title>ReportServer</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>

</head>
<body>
	<script type="text/javascript">
       var ip = window.location.host.split(":")[0];
	   var port = "<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "reportServerPort") %>";
	   var currentUser = "<%=SessionUtil.getEmpName() != null ? SessionUtil.getEmpName() : AppUserManager.getCurrentUserId() %>";
	   var commonAttr = "<%=request.getAttribute("commonAttr") != null ? request.getAttribute("commonAttr") : "" %>";

	   // 帆软8.0单点登录用户名和密码（需要在帆软系统中预先配置）
	   var frUsername = "admin"; // 帆软系统管理员用户名，需要根据实际情况修改
	   var frPassword = "123456"; // 帆软系统管理员密码，需要根据实际情况修改

	   // 构建帆软服务器地址
	   var frServerUrl = window.location.protocol + "//" + ip + ":" + port;

	   // 单点登录接口地址
	   var ssoUrl = frServerUrl + "/WebReport/ReportServer?op=fs_load&cmd=sso&fr_username=" +
	                encodeURIComponent(frUsername) + "&fr_password=" + encodeURIComponent(frPassword);

	   // 目标报表地址
	   var targetUrl = frServerUrl + "/WebReport/ReportServer" + commonAttr;

	   // 创建隐藏的iframe进行单点登录
	   var ssoIframe = document.createElement("iframe");
	   ssoIframe.style.display = "none";
	   ssoIframe.src = ssoUrl;

	   // 监听iframe加载完成事件
	   ssoIframe.onload = function() {
	       // 单点登录完成后，跳转到目标报表页面
	       // 在URL中添加当前用户信息作为参数
	       var separator = commonAttr.indexOf('?') > -1 ? '&' : '?';
	       var finalUrl = targetUrl + separator + "current_user=" + encodeURIComponent(currentUser);
	       window.location.href = finalUrl;
	   };

	   // 错误处理
	   ssoIframe.onerror = function() {
	       console.error("帆软单点登录失败，直接跳转到报表页面");
	       window.location.href = targetUrl;
	   };

	   // 将iframe添加到页面
	   document.body.appendChild(ssoIframe);
    </script>
</body>
</html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
<%@ page import="java.util.Optional" %>
<%@ page import="com.eos.foundation.eoscommon.ConfigurationUtil" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!--
  - Author(s): ouwen
  - Date: 2022-05-17 17:58:13
  - Description: 帆软8.0 Ajax单点登录集成
-->
<head>
<title>ReportServer</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    <script src="<%= request.getContextPath()%>/coframe/auth/skin1/index/js/jquery.min.js"></script>
</head>
<body>
<%
	// 优化ip、port的取值
	String reportServerHost = Optional
			.ofNullable(ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "reportServerHost"))
			.orElse("127.0.0.1");
	String reportServerPort = Optional
			.ofNullable(ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "reportServerPort"))
			.orElse("38899");

	// 如果配置的是127.0.0.1，则使用当前应用的IP地址
	if ("127.0.0.1".equals(reportServerHost)) {
		reportServerHost = request.getServerName();
	}
%>
	<script type="text/javascript">
     var ip = "<%=reportServerHost%>";
	   var port = "<%=reportServerPort%>";
	   var commonAttr = "<%=request.getAttribute("commonAttr") != null ? request.getAttribute("commonAttr") : "" %>";

	   // 帆软8.0单点登录用户名和密码
	   var frUsername = "viewer"; // 帆软系统用户名
	   var frPassword = "viewer"; // 帆软系统密码

	   // 构建帆软服务器地址
	   var frServerUrl = window.location.protocol + "//" + ip + ":" + port;

	   // 目标报表地址
	   var targetUrl = frServerUrl + "/WebReport/ReportServer" + commonAttr;

	   // 执行Ajax登录
	   function doAjaxLogin() {
	       jQuery.ajax({
	           url: frServerUrl + "/WebReport/ReportServer?op=fs_load&cmd=sso",
	           dataType: "jsonp",
	           data: {
	               "fr_username": frUsername,
	               "fr_password": frPassword
	           },
	           jsonp: "callback",
	           timeout: 10000, // 10秒超时
	           success: function(data) {
	               if (data.status === "success") {
	                   // 登录成功，跳转到目标报表页面
	                   window.location.href = targetUrl;
	               } else if (data.status === "fail") {
	                   showError("帆软单点登录失败：用户名或密码错误");
	               } else {
	                   showError("帆软单点登录失败：未知错误");
	               }
	           },
	           error: function(xhr, status, error) {
	               if (status === 'timeout') {
	                   showError("帆软单点登录超时，请检查网络连接");
	               } else {
	                   showError("帆软单点登录失败：" + error);
	               }
	           }
	       });
	   }

	   // 显示错误信息
	   function showError(message) {
	       alert(message);
	       console.error(message);
	   }

	   // 页面加载完成后执行单点登录
	   if (document.readyState === 'loading') {
	       document.addEventListener('DOMContentLoaded', doAjaxLogin);
	   } else {
	       doAjaxLogin();
	   }
    </script>
</body>
</html>
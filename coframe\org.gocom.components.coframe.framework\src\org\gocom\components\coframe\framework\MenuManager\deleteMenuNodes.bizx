<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="deleteMenuNodes" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>loopstart0</targetNode>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link3</targetConnections>
    <location x="484" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="486" y="96"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tLoopStart" description="" id="loopstart0" name="循环" displayName="循环" grouped="true" type="loopstart" matchedName="loopend0" loopType="iterate">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopstart0</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="188" y="62"/>
    <size height="24" width="24"/>
    <nodeLabel>loopstart0label</nodeLabel>
    <process:iterate iterable="nodeList" iterableElement="node"/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopstart0label" name="label" nodeType="label">
    <location x="188" y="94"/>
    <figSize height="12" width="25"/>
    <node>loopstart0</node>
  </nodes>
  <nodes xsi:type="process:tLoopEnd" id="loopend0" name="循环结束" displayName="循环结束" grouped="true" type="loopend" matchedName="loopstart0">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>loopend0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="360" y="62"/>
    <size height="24" width="24"/>
    <nodeLabel>loopend0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="loopend0label" name="label" nodeType="label">
    <location x="348" y="94"/>
    <figSize height="12" width="49"/>
    <node>loopend0</node>
  </nodes>
  <nodes xsi:type="model:GroupNode" id="group0" name="group" grouped="true" gstart="loopstart0" gend="loopend0">
    <location x="188" y="49"/>
    <size height="50" width="196"/>
    <chidren>loopend0</chidren>
    <chidren>loopstart0</chidren>
    <chidren>subprocess0</chidren>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="deleteMenu" displayName="deleteMenu" grouped="true" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>loopend0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="272" y="60"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="1" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.deleteMenu</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="nodeId" type="query" value="String" valueType="Primitive" pattern="reference">node/id</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="255" y="95"/>
    <figSize height="12" width="61"/>
    <node>subprocess0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-09 16:34:11" date="2013-03-09Z" description="" name="deleteNodes" version="6.3"/>
  <process:variables>
    <process:variable anyType="java.util.Map" description="" historyStateLocation="client" isArray="false" name="node"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input anyType="java.util.List" description="" isArray="false" name="nodeList"/>
  </process:inputs>
  <process:outputs/>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

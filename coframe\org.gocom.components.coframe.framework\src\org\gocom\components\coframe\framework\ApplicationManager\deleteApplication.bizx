<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="deleteApplication" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="树右键删除" title="fangwl&#x9;13-3-9 下午3:56">
    <location x="45" y="403"/>
    <size height="100" width="190"/>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link14</targetConnections>
    <targetConnections>link15</targetConnections>
    <targetConnections>link17</targetConnections>
    <targetConnections>link5</targetConnections>
    <location x="650" y="282"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="652" y="318"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" collapsed="false" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <location x="60" y="282"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <location x="62" y="318"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="获得子节点列表" displayName="deleteApplicationNodes" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>subprocess1</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="208" y="283"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="2" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.queryApplicationTreeNode</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="nodeType" type="query" value="String" valueType="Primitive" pattern="reference">nodeType</process:inputVariable>
        <process:inputVariable id="1" name="nodeId" type="query" value="String" valueType="Primitive" pattern="reference">nodeId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="data" type="query" value="java.util.List" valueType="Java">nodeList</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="179" y="318"/>
    <figSize height="12" width="85"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="删除子节点" displayName="deleteApplicationNodes" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link11" name="link11" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">application</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link12" name="link12" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>invokeSpring2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">functiongroup</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link16" name="link16" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>invokeSpring3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">subfunctiongroup</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="355" y="283"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="0" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.deleteApplicationNodes</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="nodes" type="query" value="java.util.List" valueType="Java" pattern="reference">nodeList</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="338" y="318"/>
    <figSize height="12" width="61"/>
    <node>subprocess1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="删除功能" displayName="deleteFunctionById" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <targetConnections>link6</targetConnections>
    <location x="650" y="129"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFunctionService.deleteFunctionById</process:partner>
      <process:instance instanceName="AppFunctionBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="id" type="query" value="java.lang.String" valueType="Java" pattern="reference">nodeId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="640" y="165"/>
    <figSize height="12" width="49"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="删除应用" displayName="deleteApplicationById" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link14" name="link14" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link11</targetConnections>
    <location x="502" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.deleteApplicationById</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="id" type="query" value="java.lang.String" valueType="Java" pattern="reference">nodeId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="492" y="244"/>
    <figSize height="12" width="49"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="删除功能组" displayName="deleteFuncGroupById" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link12</targetConnections>
    <location x="502" y="356"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.deleteFuncGroupById</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="id" type="query" value="java.lang.String" valueType="Java" pattern="reference">nodeId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="486" y="392"/>
    <figSize height="12" width="61"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="删除子功能组" displayName="deleteFuncGroupById" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link16</targetConnections>
    <location x="502" y="504"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.deleteFuncGroupById</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="id" type="query" value="java.lang.String" valueType="Java" pattern="reference">nodeId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="480" y="540"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring3</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess2" name="deleteMenus" displayName="deleteMenus" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess2</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="552" y="75"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess2label</nodeLabel>
    <process:flow index="1" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.framework.MenuManager.deleteMenus</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="nodes" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appmenus</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess2label" name="label" nodeType="label">
    <location x="532" y="110"/>
    <figSize height="12" width="67"/>
    <node>subprocess2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokeSpring4</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="355" y="129"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">nodeId</process:from>
      <process:to type="query">criteria/_expr[1]/funccode</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="357" y="165"/>
    <figSize height="12" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring4" name="spring调用" displayName="queryAppMenus" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring4</sourceNode>
      <targetNode>subprocess2</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link6" name="link6" displayName="连接线" type="transition">
      <sourceNode>invokeSpring4</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NULLOREMPTY">
          <process:leftOperand type="query">appmenus</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="453" y="129"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring4label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppMenuService.queryAppMenus</process:partner>
      <process:instance instanceName="AppMenuBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteriaType" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppMenu[]" valueType="Java">appmenus</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring4label" name="label" nodeType="label">
    <location x="437" y="165"/>
    <figSize height="12" width="61"/>
    <node>invokeSpring4</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-09 13:51:00" date="2013-03-09Z" description="" name="deleteApplicaiton" version="6.3"/>
  <process:variables>
    <process:variable anyType="java.util.Map" description="" historyStateLocation="client" isArray="false" name="node"/>
    <process:variable anyType="java.util.List" description="" historyStateLocation="client" isArray="false" name="nodeList"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppMenu" description="" historyStateLocation="client" isArray="true" name="appmenus"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="nodeId" primitiveType="String"/>
    <process:input description="" isArray="false" name="nodeType" primitiveType="String"/>
  </process:inputs>
  <process:outputs/>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

<?xml version="1.0" encoding="UTF-8"?><root>
    <case1>
        <root>
            <data>
                <employee __parameterDataType="java:org.gocom.components.coframe.org.dataset.OrgEmployee" __type="java:org.gocom.components.coframe.org.dataset.OrgEmployee">
                    <empid __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <empcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <operatorid __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <userid __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <empname __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <realname __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <gender __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <birthdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <position __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <empstatus __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <cardtype __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <cardno __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <indate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <outdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <otel __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <oaddress __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <ozipcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <oemail __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <faxno __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <mobileno __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <qq __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <htel __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <haddress __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <hzipcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <pemail __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <party __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <degree __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <major __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <specialty __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <workexp __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <regdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <createtime __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <lastmodytime __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <orgidlist __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orgid __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <remark __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <tenantid __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <appid __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <weibo __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                </employee>
                <user __parameterDataType="java:org.gocom.components.coframe.rights.dataset.CapUser" __type="java:org.gocom.components.coframe.rights.dataset.CapUser">
                    <operatorId __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <tenantId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <password __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <invaldate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <userName __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <authmode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <status __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <unlocktime __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <menutype __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <lastlogin __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <errcount __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <startdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <enddate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <validtime __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <maccode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <ipaddress __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <email __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <createuser __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <createtime __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                </user>
                <org __parameterDataType="java:org.gocom.components.coframe.org.dataset.OrgOrganization" __type="java:org.gocom.components.coframe.org.dataset.OrgOrganization">
                    <orgid __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <orgcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orgname __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orglevel __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <orgdegree __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orgseq __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orgtype __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orgaddr __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <zipcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <manaposition __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <managerid __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <orgmanager __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <linkman __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <linktel __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <email __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <weburl __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <startdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <enddate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <status __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <area __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <createtime __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <lastupdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                    <updator __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <sortno __parameterDataType="java:int" __type="java:int"/>
                    <isleaf __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <subcount __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                    <remark __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <tenantid __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <appid __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <isowner __parameterDataType="java:int" __type="java:int"/>
                    <spellcd __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orgattri __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <hisdeptcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <orgOrganization __parameterDataType="java:org.gocom.components.coframe.org.dataset.OrgOrganization" __type="java:org.gocom.components.coframe.org.dataset.OrgOrganization">
                        <orgid __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                        <orgcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <orgname __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <orglevel __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                        <orgdegree __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <orgseq __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <orgtype __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <orgaddr __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <zipcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <manaposition __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                        <managerid __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                        <orgmanager __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <linkman __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <linktel __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <email __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <weburl __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <startdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                        <enddate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                        <status __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <area __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <createtime __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                        <lastupdate __parameterDataType="java:java.util.Date" __type="java:java.util.Date"/>
                        <updator __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                        <sortno __parameterDataType="java:int" __type="java:int"/>
                        <isleaf __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <subcount __parameterDataType="java:java.math.BigDecimal" __type="java:java.math.BigDecimal"/>
                        <remark __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <tenantid __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <appid __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <isowner __parameterDataType="java:int" __type="java:int"/>
                        <spellcd __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <orgattri __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                        <hisdeptcode __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    </orgOrganization>
                </org>
            </data>
            <MUOContext>
                <userObject __parameterDataType="java:com.eos.data.datacontext.UserObject" __type="java:com.eos.data.datacontext.UserObject">
                    <userId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userMail __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userName __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userOrgId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userOrgName __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userRealName __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <uniqueId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userRemoteIP __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <sessionId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                </userObject>
            </MUOContext>
        </root>
    </case1>
</root>

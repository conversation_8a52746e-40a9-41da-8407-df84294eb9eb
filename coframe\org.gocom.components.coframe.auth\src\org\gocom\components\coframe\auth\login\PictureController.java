package org.gocom.components.coframe.auth.login;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Random;

import com.eos.system.annotation.Bizlet;

public class PictureController {
	
	public static boolean isOSLinux() {
		Properties prop = System.getProperties();
		String os = prop.getProperty("os.name");
		if (os != null && os.toLowerCase().indexOf("linux") > -1) {
			return true;
		} else {
			return false;
		}
	}
	
	public String getAppPath() {
		String nodepath = this.getClass().getClassLoader().getResource("/")
				.getPath();
		int startIndex = PictureController.isOSLinux() ? 0 : 1;
		String appPath = nodepath.substring(startIndex, nodepath.length() - 16);
		return appPath + "image/";
	}
	/**
	 * 校验验证是否成功
	 * @return  n 
	 */
	@Bizlet
	public  boolean verifyimgflag(){
		boolean flag = false;
	    String verifyimgflag = LoginService.getSessionAttribute("verifyimgflag");
		if("TRUE".equals(verifyimgflag)) return true;
		return flag;
	}
	@Bizlet()
    public  Map<String, Object>  getImageVerifyCode() {
        Map<String, Object> resultMap = new HashMap<>();
        System.out.println(this.getAppPath());
        //读取本地路径下的图片,随机选一条
        File file = new File(this.getAppPath());
        File[] files = file.listFiles();
        int n = new Random().nextInt(files.length);
        File imageUrl = files[n];
        PictureSlideCaptcha.createImage(imageUrl, resultMap); 
        LoginService.sessionPut("xWidth", resultMap.get("xWidth").toString());
        resultMap.remove("xWidth");
        resultMap.put("errcode", 0);
        resultMap.put("errmsg", "success");
        LoginService.sessionPut("verifyimgflag", "FALSE");
        return resultMap;
    }
	@Bizlet()
    public Map<String, Object> verifyImageCode(String moveLength) {
        Double dMoveLength = Double.valueOf(moveLength);
        Map<String, Object> resultMap = new HashMap<>();
        Integer xWidth = Integer.parseInt(LoginService.getSessionAttribute("xWidth"));
        if (xWidth == null) {
            resultMap.put("errcode", 1);
            resultMap.put("errmsg", "验证过期，请重试");
            LoginService.sessionPut("verifyimgflag", "FALSE");
            return resultMap;
        }
        if (Math.abs(xWidth - dMoveLength) > 10) {
            resultMap.put("errcode", 1);
            resultMap.put("errmsg", "验证不通过");
            LoginService.sessionPut("verifyimgflag", "FALSE");
        } else {
            resultMap.put("errcode", 0);
            resultMap.put("errmsg", "验证通过");
            LoginService.sessionPut("verifyimgflag", "TRUE");
        }
        return resultMap;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="saveUsualMenu.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="105" y="159"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link3</targetConnections>
    <targetConnections>link5</targetConnections>
    <targetConnections>link9</targetConnections>
    <location x="495" y="159"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="String" name="status" type="query" valueType="Primitive">status</process:return>
      <process:return id="1" language="String" name="message" type="query" valueType="Primitive">message</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="107" y="195"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="497" y="195"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link7" name="link7" displayName="连接线" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">node/type</process:leftOperand>
          <process:rightOperand type="literal">delete</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="210" y="159"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">node/menuid</process:from>
      <process:to type="query">usualmenu/menuid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">node/name</process:from>
      <process:to type="query">usualmenu/name</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">node/title</process:from>
      <process:to type="query">usualmenu/title</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">node/url</process:from>
      <process:to type="query">usualmenu/url</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/userId</process:from>
      <process:to type="query">usualmenu/empid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/userId</process:from>
      <process:to type="query">usualmenu/adduserid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="expression" pattern="reference">new Date()</process:from>
      <process:to type="query">usualmenu/adddate</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/LoginUserInfo/S_DEPTAPPS</process:from>
      <process:to type="query">usualmenu/depttype</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="212" y="195"/>
    <figSize height="12" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="saveEntity" displayName="saveEntity" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>assign2</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="302" y="75"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.saveEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">usualmenu</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="283" y="111"/>
    <figSize height="12" width="61"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值1" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="389" y="75"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">1</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">设置成功</process:from>
      <process:to type="query">message</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="388" y="111"/>
    <figSize height="12" width="31"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="390" y="195"/>
    <figSize height="12" width="31"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值2" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <targetConnections>link6</targetConnections>
    <location x="391" y="159"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-1</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">设置失败</process:from>
      <process:to type="query">message</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="deleteEntity" displayName="deleteEntity" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>assign2</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>assign3</targetNode>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="302" y="240"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.deleteEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">usualmenu</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="277" y="276"/>
    <figSize height="12" width="73"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="388" y="276"/>
    <figSize height="12" width="31"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="赋值3" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="389" y="240"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">2</process:from>
      <process:to type="query">status</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">设置成功</process:from>
      <process:to type="query">message</process:to>
    </process:copy>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2016-06-24 14:46:15" date="2016-06-24Z" description="" name="saveUsualMenu" version="*******"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.auth.queryentity.AppUsualmenu" name="usualmenu"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input anyType="commonj.sdo.DataObject" description="" isArray="false" name="node"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="status" primitiveType="String"/>
    <process:output description="" isArray="false" name="message" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

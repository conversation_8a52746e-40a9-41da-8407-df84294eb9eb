<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="storePartyAuth.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link4</targetConnections>
    <location x="652" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Boolean" name="saveResult" type="query" valueType="Primitive">saveResult</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="654" y="96"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="504" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="expression" pattern="reference">delSuccess&amp;&amp;saveSuccess</process:from>
      <process:to type="query">saveResult</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="506" y="96"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="deletePartyAuthBatch" displayName="deletePartyAuthBatch" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="208" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.org.PartyAuthService.deletePartyAuthBatch</process:partner>
      <process:instance instanceName="partyAuthServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="roleId" type="query" value="java.lang.String" valueType="Java" pattern="reference">roleId</process:inputVariable>
      <process:inputVariable id="1" name="partyList" type="query" value="com.primeton.cap.party.Party[]" valueType="Java" pattern="reference">partyNoAuth</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out" type="query" value="boolean" valueType="Java">delSuccess</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="160" y="96"/>
    <figSize height="17" width="124"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="savePartyAuthBatch" displayName="savePartyAuthBatch" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="356" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.org.PartyAuthService.savePartyAuthBatch</process:partner>
      <process:instance instanceName="partyAuthServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="roleId" type="query" value="java.lang.String" valueType="Java" pattern="reference">roleId</process:inputVariable>
      <process:inputVariable id="1" name="partyList" type="query" value="com.primeton.cap.party.Party[]" valueType="Java" pattern="reference">partyWithAuth</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out" type="query" value="boolean" valueType="Java">saveSuccess</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="313" y="96"/>
    <figSize height="17" width="114"/>
    <node>invokeSpring1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="liuzn" createTime="2013-03-14 19:25:06" date="2013-03-14Z" description="保存Party授权信息" name="保存Party授权信息" version="6.3"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" name="delSuccess" primitiveType="Boolean"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="saveSuccess" primitiveType="Boolean"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="roleId" primitiveType="String"/>
    <process:input anyType="com.primeton.cap.party.Party" description="" isArray="true" name="partyWithAuth"/>
    <process:input anyType="com.primeton.cap.party.Party" description="" isArray="true" name="partyNoAuth"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="saveResult" primitiveType="Boolean"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

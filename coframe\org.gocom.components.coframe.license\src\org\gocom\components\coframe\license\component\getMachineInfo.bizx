<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getMachineInfo" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="获取本地机器信息" title="luo_jj&#x9;22-1-17 上午1:57">
    <location x="60" y="555"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link1</targetConnections>
    <location x="350" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="java.util.Map" name="result" type="query" valueType="Java">result</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="352" y="96"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="获取本地机器信息" displayName="获取机器信息" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="208" y="60"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="static" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.license.AuthorizationService.getMachineInfo</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables/>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.Map&lt;java.lang.String,java.lang.String>" valueType="Java">result</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="171" y="96"/>
    <figSize height="17" width="97"/>
    <node>invokePojo0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="ouwen" createTime="2022-01-17 01:57:38" date="2022-01-16Z" description="" name="getAachineInfo" version="*******"/>
  <process:variables/>
  <process:inputs varArgs="false"/>
  <process:outputs>
    <process:output anyType="java.util.Map" description="" isArray="false" name="result"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

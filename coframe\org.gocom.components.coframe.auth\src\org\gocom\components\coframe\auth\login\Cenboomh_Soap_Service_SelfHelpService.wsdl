<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:s0="http://Cenboomh.org" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" targetNamespace="http://Cenboomh.org">
    <types>
        <s:schema elementFormDefault="qualified" targetNamespace="http://Cenboomh.org">
            <s:element name="XMLService">
                <s:complexType>
                    <s:sequence>
                        <s:element minOccurs="0" name="pInput" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
            <s:element name="XMLServiceResponse">
                <s:complexType>
                    <s:sequence>
                        <s:element name="XMLServiceResult" type="s:string"/>
                    </s:sequence>
                </s:complexType>
            </s:element>
        </s:schema>
    </types>
    <message name="XMLServiceSoapIn">
        <part element="s0:XMLService" name="parameters"/>
    </message>
    <message name="XMLServiceSoapOut">
        <part element="s0:XMLServiceResponse" name="parameters"/>
    </message>
    <portType name="ReportServiceSoap">
        <operation name="XMLService">
            <input message="s0:XMLServiceSoapIn"/>
            <output message="s0:XMLServiceSoapOut"/>
        </operation>
    </portType>
    <binding name="ReportServiceSoap" type="s0:ReportServiceSoap">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="XMLService">
            <soap:operation soapAction="http://Cenboomh.org/Cenboomh.Soap.Service.SelfHelpService.XMLService" style="document"/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="ReportService">
        <port binding="s0:ReportServiceSoap" name="ReportServiceSoap">
            <soap:address location="http://*************:8080/soap/cenboomh/Cenboomh.Soap.Service.SelfHelpService.cls"/>
        </port>
    </service>
</definitions>

package org.gocom.components.coframe.auth;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;

import com.eos.system.annotation.Bizlet;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;

@Bizlet
public class DesDecryptUtil {

	public static final String key = "@Neu&Key";

	/**
	 * 解密
	 * 
	 * @param src      byte[]
	 * @param password String
	 * @return byte[]
	 * @throws Exception
	 */
	public static byte[] decrypt(byte[] src, String password) throws Exception {
		IvParameterSpec iv = new IvParameterSpec(password.getBytes("UTF-8"));
		DESKeySpec desKey = new DESKeySpec(password.getBytes("UTF-8"));
		SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
		SecretKey securekey = keyFactory.generateSecret(desKey);
		Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
		cipher.init(Cipher.DECRYPT_MODE, securekey, iv);
		return cipher.doFinal(src);
	}

	public static byte[] desEncrypt(byte[] data, String key, String charset) {
		try {
			Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
			byte[] k = charset == null || charset.trim().isEmpty() ? key.getBytes() : key.getBytes(charset);
			SecretKey secretKey = SecretKeyFactory.getInstance("DES").generateSecret(new DESKeySpec(k));
			cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(k));
			return cipher.doFinal(data);
		} catch (Exception e) {
			return null;
		}
	}

	public static void main(String[] args) throws Exception {
		String encode = Base64.encode(desEncrypt("000000".getBytes(), "X!xO@kA)", "utf-8"));
//		byte[] decode2 = Base64.decode("3EFBE2E3EC6E125D");
		System.out.println(encode);
	}

	@Bizlet
	public static String decryptUserId(String userId) {
		byte[] bs = Base64.decode(userId);
		try {
			userId = new String(decrypt(bs, key));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return userId;
	}
}

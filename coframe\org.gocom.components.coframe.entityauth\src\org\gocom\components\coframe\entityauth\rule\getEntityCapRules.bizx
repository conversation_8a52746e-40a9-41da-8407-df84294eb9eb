<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getEntityCapRules" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node1" name="注释1" displayName="注释" collapsed="false" nodeType="note" type="note" content="获取实体规则信息列表" title="lijuntao 13-4-28 上午9:51">
    <location x="105" y="197"/>
    <size height="100" width="171"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <location x="105" y="92"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link7</targetConnections>
    <location x="450" y="92"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="org.gocom.components.coframe.entityauth.pojo.RuleAndAuth[]" name="data" type="query" valueType="Java">data</process:return>
      <process:return id="1" language="Int" name="total" type="query" valueType="Primitive">total</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="107" y="128"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="452" y="128"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="查询实体规则" displayName="queryCapRules" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="230" y="92"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.entityauth.EntityCapRuleService.queryCapRules</process:partner>
      <process:instance instanceName="entityCapRuleServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="namespace" type="query" value="java.lang.String" valueType="Java" pattern="reference">namespace</process:inputVariable>
      <process:inputVariable id="1" name="searchType" type="query" value="java.lang.String" valueType="Java" pattern="reference">searchType</process:inputVariable>
      <process:inputVariable id="2" name="ruleName" type="query" value="java.lang.String" valueType="Java" pattern="reference">ruleName</process:inputVariable>
      <process:inputVariable id="3" name="party" type="query" value="com.primeton.cap.party.Party" valueType="Java" pattern="reference">party</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.entityauth.pojo.RuleAndAuth[]" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="208" y="128"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="返回总条件数" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="333" y="92"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="expression" pattern="reference">data==null?0:data.length</process:from>
      <process:to type="query">total</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="311" y="128"/>
    <figSize height="12" width="73"/>
    <node>assign0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="wanghl" createTime="2013-03-04 15:53:43" date="2013-03-04Z" description="" name="queryCapRules" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="namespace" primitiveType="String"/>
    <process:input description="" isArray="false" name="searchType" primitiveType="Int"/>
    <process:input description="" isArray="false" name="ruleName" primitiveType="String"/>
    <process:input anyType="com.primeton.cap.party.Party" description="" isArray="false" name="party"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="org.gocom.components.coframe.entityauth.pojo.RuleAndAuth" description="" isArray="true" name="data"/>
    <process:output description="" isArray="false" name="total" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

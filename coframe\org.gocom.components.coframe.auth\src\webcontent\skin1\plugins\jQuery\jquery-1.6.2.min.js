﻿(function(K0,$1){var v0=K0.document,I0=K0.navigator,M=K0.location,k=(function(){var H=function($,_){return new H.fn.init($,_,A)},B=K0.jQuery,G=K0.$,A,N=/^(?:[^<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,d=/\S/,O=/^\s+/,Q=/\s+$/,R=/\d/,F=/^<(\w+)\s*\/?>(?:<\/\1>)?$/,I=/^[\],:{}\s]*$/,c=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,S=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,Z=/(?:^|:|,)(?:\s*\[)+/g,b=/(webkit)[ \/]([\w.]+)/,E=/(opera)(?:.*version)?[ \/]([\w.]+)/,M=/(msie) ([\w.]+)/,a=/(mozilla)(?:.*? rv:([\w.]+))?/,K=/-([a-z])/ig,U=function(_,$){return $.toUpperCase()},T=I0.userAgent,$,V,X,D=Object.prototype.toString,L=Object.prototype.hasOwnProperty,W=Array.prototype.push,P=Array.prototype.slice,C=String.prototype.trim,_=Array.prototype.indexOf,Y={};H.fn=H.prototype={constructor:H,init:function(C,D,B){var $,A,E,_;if(!C)return this;if(C.nodeType){this.context=this[0]=C;this.length=1;return this}if(C==="body"&&!D&&v0.body){this.context=v0;this[0]=v0.body;this.selector=C;this.length=1;return this}if(typeof C==="string"){if(C.charAt(0)==="<"&&C.charAt(C.length-1)===">"&&C.length>=3)$=[null,C,null];else $=N.exec(C);if($&&($[1]||!D)){if($[1]){D=D instanceof H?D[0]:D;_=(D?D.ownerDocument||D:v0);E=F.exec(C);if(E){if(H.isPlainObject(D)){C=[v0.createElement(E[1])];H.fn.attr.call(C,D,true)}else C=[_.createElement(E[1])]}else{E=H.buildFragment([$[1]],[_]);C=(E.cacheable?H.clone(E.fragment):E.fragment).childNodes}return H.merge(this,C)}else{A=v0.getElementById($[2]);if(A&&A.parentNode){if(A.id!==$[2])return B.find(C);this.length=1;this[0]=A}this.context=v0;this.selector=C;return this}}else if(!D||D.jquery)return(D||B).find(C);else return this.constructor(D).find(C)}else if(H.isFunction(C))return B.ready(C);if(C.selector!==$1){this.selector=C.selector;this.context=C.context}return H.makeArray(C,this)},selector:"",jquery:"1.6.2",length:0,size:function(){return this.length},toArray:function(){return P.call(this,0)},get:function($){return $==null?this.toArray():($<0?this[this.length+$]:this[$])},pushStack:function(_,B,$){var A=this.constructor();if(H.isArray(_))W.apply(A,_);else H.merge(A,_);A.prevObject=this;A.context=this.context;if(B==="find")A.selector=this.selector+(this.selector?" ":"")+$;else if(B)A.selector=this.selector+"."+B+"("+$+")";return A},each:function($,_){return H.each(this,$,_)},ready:function($){H.bindReady();V.done($);return this},eq:function($){return $===-1?this.slice($):this.slice($,+$+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},slice:function(){return this.pushStack(P.apply(this,arguments),"slice",P.call(arguments).join(","))},map:function($){return this.pushStack(H.map(this,function(A,_){return $.call(A,_,A)}))},end:function(){return this.prevObject||this.constructor(null)},push:W,sort:[].sort,splice:[].splice};H.fn.init.prototype=H.fn;H.extend=H.fn.extend=function(){var I,G,C,B,F,D,A=arguments[0]||{},$=1,_=arguments.length,E=false;if(typeof A==="boolean"){E=A;A=arguments[1]||{};$=2}if(typeof A!=="object"&&!H.isFunction(A))A={};if(_===$){A=this;--$}for(;$<_;$++)if((I=arguments[$])!=null)for(G in I){C=A[G];B=I[G];if(A===B)continue;if(E&&B&&(H.isPlainObject(B)||(F=H.isArray(B)))){if(F){F=false;D=C&&H.isArray(C)?C:[]}else D=C&&H.isPlainObject(C)?C:{};A[G]=H.extend(E,D,B)}else if(B!==$1)A[G]=B}return A};H.extend({noConflict:function($){if(K0.$===H)K0.$=G;if($&&K0.jQuery===H)K0.jQuery=B;return H},isReady:false,readyWait:1,holdReady:function($){if($)H.readyWait++;else H.ready(true)},ready:function($){if(($===true&&!--H.readyWait)||($!==true&&!H.isReady)){if(!v0.body)return setTimeout(H.ready,1);H.isReady=true;if($!==true&&--H.readyWait>0)return;V.resolveWith(v0,[H]);if(H.fn.trigger)H(v0).trigger("ready").unbind("ready")}},bindReady:function(){if(V)return;V=H._Deferred();if(v0.readyState==="complete")return setTimeout(H.ready,1);if(v0.addEventListener){v0.addEventListener("DOMContentLoaded",X,false);K0.addEventListener("load",H.ready,false)}else if(v0.attachEvent){v0.attachEvent("onreadystatechange",X);K0.attachEvent("onload",H.ready);var $=false;try{$=K0.frameElement==null}catch(_){}if(v0.documentElement.doScroll&&$)J()}},isFunction:function($){return H.type($)==="function"},isArray:Array.isArray||function($){return H.type($)==="array"},isWindow:function($){return $&&typeof $==="object"&&"setInterval"in $},isNaN:function($){return $==null||!R.test($)||isNaN($)},type:function($){return $==null?String($):Y[D.call($)]||"object"},isPlainObject:function(_){if(!_||H.type(_)!=="object"||_.nodeType||H.isWindow(_))return false;if(_.constructor&&!L.call(_,"constructor")&&!L.call(_.constructor.prototype,"isPrototypeOf"))return false;var $;for($ in _);return $===$1||L.call(_,$)},isEmptyObject:function($){for(var _ in $)return false;return true},error:function($){throw $},parseJSON:function($){if(typeof $!=="string"||!$)return null;$=H.trim($);if(K0.JSON&&K0.JSON.parse)return K0.JSON.parse($);if(I.test($.replace(c,"@").replace(S,"]").replace(Z,"")))return(new Function("return "+$))();H.error("Invalid JSON: "+$)},parseXML:function(_,A,$){if(K0.DOMParser){$=new DOMParser();A=$.parseFromString(_,"text/xml")}else{A=new ActiveXObject("Microsoft.XMLDOM");A.async="false";A.loadXML(_)}$=A.documentElement;if(!$||!$.nodeName||$.nodeName==="parsererror")H.error("Invalid XML: "+_);return A},noop:function(){},globalEval:function($){if($&&d.test($))(K0.execScript||function($){K0["eval"].call(K0,$)})($)},camelCase:function($){return $.replace(K,U)},nodeName:function($,_){return $.nodeName&&$.nodeName.toUpperCase()===_.toUpperCase()},each:function(_,A,D){var E,$=0,C=_.length,B=C===$1||H.isFunction(_);if(D){if(B){for(E in _)if(A.apply(_[E],D)===false)break}else for(;$<C;)if(A.apply(_[$++],D)===false)break}else if(B){for(E in _)if(A.call(_[E],E,_[E])===false)break}else for(;$<C;)if(A.call(_[$],$,_[$++])===false)break;return _},trim:C?function($){return $==null?"":C.call($)}:function($){return $==null?"":$.toString().replace(O,"").replace(Q,"")},makeArray:function(B,A){var _=A||[];if(B!=null){var $=H.type(B);if(B.length==null||$==="string"||$==="function"||$==="regexp"||H.isWindow(B))W.call(_,B);else H.merge(_,B)}return _},inArray:function(A,C){if(_)return _.call(C,A);for(var $=0,B=C.length;$<B;$++)if(C[$]===A)return $;return-1},merge:function(A,C){var $=A.length,_=0;if(typeof C.length==="number"){for(var B=C.length;_<B;_++)A[$++]=C[_]}else while(C[_]!==$1)A[$++]=C[_++];A.length=$;return A},grep:function(D,_,B){var E=[],A;B=!!B;for(var $=0,C=D.length;$<C;$++){A=!!_(D[$],$);if(B!==A)E.push(D[$])}return E},map:function(E,C,A){var B,$,F=[],_=0,D=E.length,G=E instanceof H||D!==$1&&typeof D==="number"&&((D>0&&E[0]&&E[D-1])||D===0||H.isArray(E));if(G){for(;_<D;_++){B=C(E[_],_,A);if(B!=null)F[F.length]=B}}else for($ in E){B=C(E[$],$,A);if(B!=null)F[F.length]=B}return F.concat.apply([],F)},guid:1,proxy:function(C,A){if(typeof A==="string"){var $=C[A];A=C;C=$}if(!H.isFunction(C))return $1;var _=P.call(arguments,2),B=function(){return C.apply(A,_.concat(P.call(arguments)))};B.guid=C.guid=C.guid||B.guid||H.guid++;return B},access:function(E,$,B,C,G,F){var D=E.length;if(typeof $==="object"){for(var A in $)H.access(E,A,$[A],C,G,B);return E}if(B!==$1){C=!F&&C&&H.isFunction(B);for(var _=0;_<D;_++)G(E[_],$,C?B.call(E[_],_,G(E[_],$)):B,F);return E}return D?G(E[0],$):$1},now:function(){return(new Date()).getTime()},uaMatch:function(_){_=_.toLowerCase();var $=b.exec(_)||E.exec(_)||M.exec(_)||_.indexOf("compatible")<0&&a.exec(_)||[];return{browser:$[1]||"",version:$[2]||"0"}},sub:function(){function _($,A){return new _.fn.init($,A)}H.extend(true,_,this);_.superclass=this;_.fn=_.prototype=this();_.fn.constructor=_;_.sub=this.sub;_.fn.init=function $(B,C){if(C&&C instanceof H&&!(C instanceof _))C=_(C);return H.fn.init.call(this,B,C,A)};_.fn.init.prototype=_.fn;var A=_(v0);return _},browser:{}});H.each("Boolean Number String Function Array Date RegExp Object".split(" "),function($,_){Y["[object "+_+"]"]=_.toLowerCase()});$=H.uaMatch(T);if($.browser){H.browser[$.browser]=true;H.browser.version=$.version}if(H.browser.webkit)H.browser.safari=true;if(d.test("\xa0")){O=/^[\s\xA0]+/;Q=/[\s\xA0]+$/}A=H(v0);if(v0.addEventListener)X=function(){v0.removeEventListener("DOMContentLoaded",X,false);H.ready()};else if(v0.attachEvent)X=function(){if(v0.readyState==="complete"){v0.detachEvent("onreadystatechange",X);H.ready()}};function J(){if(H.isReady)return;try{v0.documentElement.doScroll("left")}catch($){setTimeout(J,1);return}H.ready()}return H})(),T0="done fail isResolved isRejected promise then always pipe".split(" "),K=[].slice;k.extend({_Deferred:function(){var A=[],C,B,_,$={done:function(){if(!_){var G=arguments,B,F,D,E,H;if(C){H=C;C=0}for(B=0,F=G.length;B<F;B++){D=G[B];E=k.type(D);if(E==="array")$.done.apply($,D);else if(E==="function")A.push(D)}if(H)$.resolveWith(H[0],H[1])}return this},resolveWith:function(D,$){if(!_&&!C&&!B){$=$||[];B=1;while(A[0])A.shift().apply(D,$);C=[D,$];B=0}return this},resolve:function(){$.resolveWith(this,arguments);return this},isResolved:function(){return!!(B||C)},cancel:function(){_=1;A=[];return this}};return $},Deferred:function(B){var $=k._Deferred(),A=k._Deferred(),_;k.extend($,{then:function(_,A){$.done(_).fail(A);return this},always:function(){return $.done.apply($,arguments).fail.apply(this,arguments)},fail:A.done,rejectWith:A.resolveWith,reject:A.resolve,isRejected:A.isResolved,pipe:function(_,A){return k.Deferred(function(B){k.each({done:[_,"resolve"],fail:[A,"reject"]},function(A,C){var E=C[0],_=C[1],D;if(k.isFunction(E))$[A](function(){D=E.apply(this,arguments);if(D&&k.isFunction(D.promise))D.promise().then(B.resolve,B.reject);else B[_](D)});else $[A](B[_])})}).promise()},promise:function(B){if(B==null){if(_)return _;_=B={}}var A=T0.length;while(A--)B[T0[A]]=$[T0[A]];return B}});$.done(A.cancel).fail($.cancel);delete $.cancel;if(B)B.call($,$);return $},when:function(B){var D=arguments,_=0,C=D.length,E=C,$=C<=1&&B&&k.isFunction(B.promise)?B:k.Deferred();function A(_){return function(A){D[_]=arguments.length>1?K.call(arguments,0):A;if(!(--E))$.resolveWith($,K.call(D,0))}}if(C>1){for(;_<C;_++)if(D[_]&&k.isFunction(D[_].promise))D[_].promise().then(A(_),$.reject);else--E;if(!E)$.resolveWith($,D)}else if($!==B)$.resolveWith($,C?[B]:[]);return $.promise()}});k.support=(function(){var I=v0.createElement("div"),C=v0.documentElement,L,J,F,M,_,N,P,K,Q,E,A,D,B,$,O,G,H;I.setAttribute("className","t");I.innerHTML="   <link/><table></table><a href='/a' style='top:1px;float:left;opacity:.55;'>a</a><input type='checkbox'/>";L=I.getElementsByTagName("*");J=I.getElementsByTagName("a")[0];if(!L||!L.length||!J)return{};F=v0.createElement("select");M=F.appendChild(v0.createElement("option"));_=I.getElementsByTagName("input")[0];P={leadingWhitespace:(I.firstChild.nodeType===3),tbody:!I.getElementsByTagName("tbody").length,htmlSerialize:!!I.getElementsByTagName("link").length,style:/top/.test(J.getAttribute("style")),hrefNormalized:(J.getAttribute("href")==="/a"),opacity:/^0.55$/.test(J.style.opacity),cssFloat:!!J.style.cssFloat,checkOn:(_.value==="on"),optSelected:M.selected,getSetAttribute:I.className!=="t",submitBubbles:true,changeBubbles:true,focusinBubbles:false,deleteExpando:true,noCloneEvent:true,inlineBlockNeedsLayout:false,shrinkWrapBlocks:false,reliableMarginRight:true};_.checked=true;P.noCloneChecked=_.cloneNode(true).checked;F.disabled=true;P.optDisabled=!M.disabled;try{delete I.test}catch(R){P.deleteExpando=false}if(!I.addEventListener&&I.attachEvent&&I.fireEvent){I.attachEvent("onclick",function(){P.noCloneEvent=false});I.cloneNode(true).fireEvent("onclick")}_=v0.createElement("input");_.value="t";_.setAttribute("type","radio");P.radioValue=_.value==="t";_.setAttribute("checked","checked");I.appendChild(_);K=v0.createDocumentFragment();K.appendChild(I.firstChild);P.checkClone=K.cloneNode(true).cloneNode(true).lastChild.checked;I.innerHTML="";I.style.width=I.style.paddingLeft="1px";Q=v0.getElementsByTagName("body")[0];A=v0.createElement(Q?"div":"body");D={visibility:"hidden",width:0,height:0,border:0,margin:0};if(Q)k.extend(D,{position:"absolute",left:-1000,top:-1000});for(G in D)A.style[G]=D[G];A.appendChild(I);E=Q||C;E.insertBefore(A,E.firstChild);P.appendChecked=_.checked;P.boxModel=I.offsetWidth===2;if("zoom"in I.style){I.style.display="inline";I.style.zoom=1;P.inlineBlockNeedsLayout=(I.offsetWidth===2);I.style.display="";I.innerHTML="<div style='width:4px;'></div>";P.shrinkWrapBlocks=(I.offsetWidth!==2)}I.innerHTML="<table><tr><td style='padding:0;border:0;display:none'></td><td>t</td></tr></table>";B=I.getElementsByTagName("td");H=(B[0].offsetHeight===0);B[0].style.display="";B[1].style.display="none";P.reliableHiddenOffsets=H&&(B[0].offsetHeight===0);I.innerHTML="";if(v0.defaultView&&v0.defaultView.getComputedStyle){N=v0.createElement("div");N.style.width="0";N.style.marginRight="0";I.appendChild(N);P.reliableMarginRight=(parseInt((v0.defaultView.getComputedStyle(N,null)||{marginRight:0}).marginRight,10)||0)===0}A.innerHTML="";E.removeChild(A);if(I.attachEvent)for(G in{submit:1,change:1,focusin:1}){O="on"+G;H=(O in I);if(!H){I.setAttribute(O,"return;");H=(typeof I[O]==="function")}P[G+"Bubbles"]=H}A=K=F=M=Q=N=I=_=null;return P})();k.boxModel=k.support.boxModel;var y0=/^(?:\{.*\}|\[.*\])$/,A1=/([a-z])([A-Z])/g;k.extend({cache:{},uuid:0,expando:"jQuery"+(k.fn.jquery+Math.random()).replace(/\D/g,""),noData:{"embed":true,"object":"clsid:D27CDB6E-AE6D-11cf-96B8-************","applet":true},hasData:function($){$=$.nodeType?k.cache[$[k.expando]]:$[k.expando];return!!$&&!t0($)},data:function(B,G,E,_){if(!k.acceptData(B))return;var D=k.expando,F=typeof G==="string",A,C=B.nodeType,$=C?k.cache:B,H=C?B[k.expando]:B[k.expando]&&k.expando;if((!H||(_&&H&&!$[H][D]))&&F&&E===$1)return;if(!H)if(C)B[k.expando]=H=++k.uuid;else H=k.expando;if(!$[H]){$[H]={};if(!C)$[H].toJSON=k.noop}if(typeof G==="object"||typeof G==="function")if(_)$[H][D]=k.extend($[H][D],G);else $[H]=k.extend($[H],G);A=$[H];if(_){if(!A[D])A[D]={};A=A[D]}if(E!==$1)A[k.camelCase(G)]=E;if(G==="events"&&!A[G])return A[D]&&A[D].events;return F?A[k.camelCase(G)]||A[G]:A},removeData:function(C,F,A){if(!k.acceptData(C))return;var E=k.expando,D=C.nodeType,$=D?k.cache:C,G=D?C[k.expando]:k.expando;if(!$[G])return;if(F){var B=A?$[G][E]:$[G];if(B){delete B[F];if(!t0(B))return}}if(A){delete $[G][E];if(!t0($[G]))return}var _=$[G][E];if(k.support.deleteExpando||$!=K0)delete $[G];else $[G]=null;if(_){$[G]={};if(!D)$[G].toJSON=k.noop;$[G][E]=_}else if(D)if(k.support.deleteExpando)delete C[k.expando];else if(C.removeAttribute)C.removeAttribute(k.expando);else C[k.expando]=null},_data:function($,A,_){return k.data($,A,_,true)},acceptData:function(_){if(_.nodeName){var $=k.noData[_.nodeName.toLowerCase()];if($)return!($===true||_.getAttribute("classid")!==$)}return true}});k.fn.extend({data:function(_,B){var C=null;if(typeof _==="undefined"){if(this.length){C=k.data(this[0]);if(this[0].nodeType===1){var D=this[0].attributes,F;for(var A=0,E=D.length;A<E;A++){F=D[A].name;if(F.indexOf("data-")===0){F=k.camelCase(F.substring(5));a0(this[0],F,C[F])}}}}return C}else if(typeof _==="object")return this.each(function(){k.data(this,_)});var $=_.split(".");$[1]=$[1]?"."+$[1]:"";if(B===$1){C=this.triggerHandler("getData"+$[1]+"!",[$[0]]);if(C===$1&&this.length){C=k.data(this[0],_);C=a0(this[0],_,C)}return C===$1&&$[1]?this.data($[0]):C}else return this.each(function(){var C=k(this),A=[$[0],B];C.triggerHandler("setData"+$[1]+"!",A);k.data(this,_,B);C.triggerHandler("changeData"+$[1]+"!",A)})},removeData:function($){return this.each(function(){k.removeData(this,$)})}});function a0(_,$,A){if(A===$1&&_.nodeType===1){var B="data-"+$.replace(A1,"$1-$2").toLowerCase();A=_.getAttribute(B);if(typeof A==="string"){try{A=A==="true"?true:A==="false"?false:A==="null"?null:!k.isNaN(A)?parseFloat(A):y0.test(A)?k.parseJSON(A):A}catch(C){}k.data(_,$,A)}else A=$1}return A}function t0($){for(var _ in $)if(_!=="toJSON")return false;return true}function F($,_,B){var E=_+"defer",C=_+"queue",A=_+"mark",D=k.data($,E,$1,true);if(D&&(B==="queue"||!k.data($,C,$1,true))&&(B==="mark"||!k.data($,A,$1,true)))setTimeout(function(){if(!k.data($,C,$1,true)&&!k.data($,A,$1,true)){k.removeData($,E,true);D.resolve()}},0)}k.extend({_mark:function($,_){if($){_=(_||"fx")+"mark";k.data($,_,(k.data($,_,$1,true)||0)+1,true)}},_unmark:function(B,_,A){if(B!==true){A=_;_=B;B=false}if(_){A=A||"fx";var $=A+"mark",C=B?0:((k.data(_,$,$1,true)||1)-1);if(C)k.data(_,$,C,true);else{k.removeData(_,$,true);F(_,A,"mark")}}},queue:function($,_,A){if($){_=(_||"fx")+"queue";var B=k.data($,_,$1,true);if(A)if(!B||k.isArray(A))B=k.data($,_,k.makeArray(A),true);else B.push(A);return B||[]}},dequeue:function($,_){_=_||"fx";var A=k.queue($,_),C=A.shift(),B;if(C==="inprogress")C=A.shift();if(C){if(_==="fx")A.unshift("inprogress");C.call($,function(){k.dequeue($,_)})}if(!A.length){k.removeData($,_+"queue",true);F($,_,"queue")}}});k.fn.extend({queue:function($,_){if(typeof $!=="string"){_=$;$="fx"}if(_===$1)return k.queue(this[0],$);return this.each(function(){var A=k.queue(this,$,_);if($==="fx"&&A[0]!=="inprogress")k.dequeue(this,$)})},dequeue:function($){return this.each(function(){k.dequeue(this,$)})},delay:function(_,$){_=k.fx?k.fx.speeds[_]||_:_;$=$||"fx";return this.queue($,function(){var A=this;setTimeout(function(){k.dequeue(A,$)},_)})},clearQueue:function($){return this.queue($||"fx",[])},promise:function(B,_){if(typeof B!=="string"){_=B;B=$1}B=B||"fx";var I=k.Deferred(),H=this,$=H.length,E=1,G=B+"defer",F=B+"queue",C=B+"mark",A;function D(){if(!(--E))I.resolveWith(H,[H])}while($--)if((A=k.data(H[$],G,$1,true)||(k.data(H[$],F,$1,true)||k.data(H[$],C,$1,true))&&k.data(H[$],G,k._Deferred(),true))){E++;A.done(D)}D();return I.promise()}});var R=/[\n\t\r]/g,f0=/\s+/,I1=/\r/g,R1=/^(?:button|input)$/i,h0=/^(?:button|input|object|select|textarea)$/i,C0=/^a(?:rea)?$/i,$0=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,L=/\:|^on/,z,i0;k.fn.extend({attr:function(_,$){return k.access(this,_,$,true,k.attr)},removeAttr:function($){return this.each(function(){k.removeAttr(this,$)})},prop:function(_,$){return k.access(this,_,$,true,k.prop)},removeProp:function($){$=k.propFix[$]||$;return this.each(function(){try{this[$]=$1;delete this[$]}catch(_){}})},addClass:function(C){var B,$,E,A,F,D,_;if(k.isFunction(C))return this.each(function($){k(this).addClass(C.call(this,$,this.className))});if(C&&typeof C==="string"){B=C.split(f0);for($=0,E=this.length;$<E;$++){A=this[$];if(A.nodeType===1)if(!A.className&&B.length===1)A.className=C;else{F=" "+A.className+" ";for(D=0,_=B.length;D<_;D++)if(!~F.indexOf(" "+B[D]+" "))F+=B[D]+" ";A.className=k.trim(F)}}}return this},removeClass:function(C){var B,$,F,A,E,D,_;if(k.isFunction(C))return this.each(function($){k(this).removeClass(C.call(this,$,this.className))});if((C&&typeof C==="string")||C===$1){B=(C||"").split(f0);for($=0,F=this.length;$<F;$++){A=this[$];if(A.nodeType===1&&A.className)if(C){E=(" "+A.className+" ").replace(R," ");for(D=0,_=B.length;D<_;D++)E=E.replace(" "+B[D]+" "," ");A.className=k.trim(E)}else A.className=""}}return this},toggleClass:function($,A){var _=typeof $,B=typeof A==="boolean";if(k.isFunction($))return this.each(function(_){k(this).toggleClass($.call(this,_,this.className,A),A)});return this.each(function(){if(_==="string"){var G,C=0,E=k(this),F=A,D=$.split(f0);while((G=D[C++])){F=B?F:!E.hasClass(G);E[F?"addClass":"removeClass"](G)}}else if(_==="undefined"||_==="boolean"){if(this.className)k._data(this,"__className__",this.className);this.className=this.className||$===false?"":k._data(this,"__className__")||""}})},hasClass:function(_){var A=" "+_+" ";for(var $=0,B=this.length;$<B;$++)if((" "+this[$].className+" ").replace(R," ").indexOf(A)>-1)return true;return false},val:function(_){var A,B,$=this[0];if(!arguments.length){if($){A=k.valHooks[$.nodeName.toLowerCase()]||k.valHooks[$.type];if(A&&"get"in A&&(B=A.get($,"value"))!==$1)return B;B=$.value;return typeof B==="string"?B.replace(I1,""):B==null?"":B}return $1}var C=k.isFunction(_);return this.each(function(B){var D=k(this),$;if(this.nodeType!==1)return;if(C)$=_.call(this,B,D.val());else $=_;if($==null)$="";else if(typeof $==="number")$+="";else if(k.isArray($))$=k.map($,function($){return $==null?"":$+""});A=k.valHooks[this.nodeName.toLowerCase()]||k.valHooks[this.type];if(!A||!("set"in A)||A.set(this,$,"value")===$1)this.value=$})}});k.extend({valHooks:{option:{get:function(_){var $=_.attributes.value;return!$||$.specified?_.value:_.text}},select:{get:function(C){var B,$=C.selectedIndex,E=[],G=C.options,A=C.type==="select-one";if($<0)return null;for(var _=A?$:0,F=A?$+1:G.length;_<F;_++){var D=G[_];if(D.selected&&(k.support.optDisabled?!D.disabled:D.getAttribute("disabled")===null)&&(!D.parentNode.disabled||!k.nodeName(D.parentNode,"optgroup"))){B=k(D).val();if(A)return B;E.push(B)}}if(A&&!E.length&&G.length)return k(G[$]).val();return E},set:function(_,$){var A=k.makeArray($);k(_).find("option").each(function(){this.selected=k.inArray(k(this).val(),A)>=0});if(!A.length)_.selectedIndex=-1;return A}}},attrFn:{val:true,css:true,html:true,text:true,data:true,width:true,height:true,offset:true},attrFix:{tabindex:"tabIndex"},attr:function(_,F,$,C){var E=_.nodeType;if(!_||E===3||E===8||E===2)return $1;if(C&&F in k.attrFn)return k(_)[F]($);if(!("getAttribute"in _))return k.prop(_,F,$);var D,A,B=E!==1||!k.isXMLDoc(_);if(B){F=k.attrFix[F]||F;A=k.attrHooks[F];if(!A)if($0.test(F))A=i0;else if(z&&F!=="className"&&(k.nodeName(_,"form")||L.test(F)))A=z}if($!==$1){if($===null){k.removeAttr(_,F);return $1}else if(A&&"set"in A&&B&&(D=A.set(_,$,F))!==$1)return D;else{_.setAttribute(F,""+$);return $}}else if(A&&"get"in A&&B&&(D=A.get(_,F))!==null)return D;else{D=_.getAttribute(F);return D===null?$1:D}},removeAttr:function($,A){var _;if($.nodeType===1){A=k.attrFix[A]||A;if(k.support.getSetAttribute)$.removeAttribute(A);else{k.attr($,A,"");$.removeAttributeNode($.getAttributeNode(A))}if($0.test(A)&&(_=k.propFix[A]||A)in $)$[_]=false}},attrHooks:{type:{set:function(A,_){if(R1.test(A.nodeName)&&A.parentNode)k.error("type property can't be changed");else if(!k.support.radioValue&&_==="radio"&&k.nodeName(A,"input")){var $=A.value;A.setAttribute("type",_);if($)A.value=$;return _}}},tabIndex:{get:function(_){var $=_.getAttributeNode("tabIndex");return $&&$.specified?parseInt($.value,10):h0.test(_.nodeName)||C0.test(_.nodeName)&&_.href?0:$1}},value:{get:function($,_){if(z&&k.nodeName($,"button"))return z.get($,_);return _ in $?$.value:null},set:function(_,$,A){if(z&&k.nodeName(_,"button"))return z.set(_,$,A);_.value=$}}},propFix:{tabindex:"tabIndex",readonly:"readOnly","for":"htmlFor","class":"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},prop:function(_,E,$){var D=_.nodeType;if(!_||D===3||D===8||D===2)return $1;var C,A,B=D!==1||!k.isXMLDoc(_);if(B){E=k.propFix[E]||E;A=k.propHooks[E]}if($!==$1){if(A&&"set"in A&&(C=A.set(_,$,E))!==$1)return C;else return(_[E]=$)}else if(A&&"get"in A&&(C=A.get(_,E))!==$1)return C;else return _[E]},propHooks:{}});i0={get:function($,_){return k.prop($,_)?_.toLowerCase():$1},set:function(_,$,B){var A;if($===false)k.removeAttr(_,B);else{A=k.propFix[B]||B;if(A in _)_[A]=true;_.setAttribute(B,B.toLowerCase())}return B}};if(!k.support.getSetAttribute){k.attrFix=k.propFix;z=k.attrHooks.name=k.attrHooks.title=k.valHooks.button={get:function($,A){var _;_=$.getAttributeNode(A);return _&&_.nodeValue!==""?_.nodeValue:$1},set:function(_,$,B){var A=_.getAttributeNode(B);if(A){A.nodeValue=$;return $}}};k.each(["width","height"],function($,_){k.attrHooks[_]=k.extend(k.attrHooks[_],{set:function(A,$){if($===""){A.setAttribute(_,"auto");return $}}})})}if(!k.support.hrefNormalized)k.each(["href","src","width","height"],function($,_){k.attrHooks[_]=k.extend(k.attrHooks[_],{get:function($){var A=$.getAttribute(_,2);return A===null?$1:A}})});if(!k.support.style)k.attrHooks.style={get:function($){return $.style.cssText.toLowerCase()||$1},set:function(_,$){return(_.style.cssText=""+$)}};if(!k.support.optSelected)k.propHooks.selected=k.extend(k.propHooks.selected,{get:function($){var _=$.parentNode;if(_){_.selectedIndex;if(_.parentNode)_.parentNode.selectedIndex}}});if(!k.support.checkOn)k.each(["radio","checkbox"],function(){k.valHooks[this]={get:function($){return $.getAttribute("value")===null?"on":$.value}}});k.each(["radio","checkbox"],function(){k.valHooks[this]=k.extend(k.valHooks[this],{set:function(_,$){if(k.isArray($))return(_.checked=k.inArray(k(_).val(),$)>=0)}})});var s0=/\.(.*)$/,o0=/^(?:textarea|input|select)$/i,D1=/\./g,e=/ /g,g0=/[^\w\s.|`]/g,Q0=function($){return $.replace(g0,"\\$&")};k.event={add:function(_,B,A,M){if(_.nodeType===3||_.nodeType===8)return;if(A===false)A=H;else if(!A)return;var L,F;if(A.handler){L=A;A=L.handler}if(!A.guid)A.guid=k.guid++;var I=k._data(_);if(!I)return;var $=I.events,C=I.handle;if(!$)I.events=$={};if(!C)I.handle=C=function($){return typeof k!=="undefined"&&(!$||k.event.triggered!==$.type)?k.event.handle.apply(C.elem,arguments):$1};C.elem=_;B=B.split(" ");var E,J=0,G;while((E=B[J++])){F=L?k.extend({},L):{handler:A,data:M};if(E.indexOf(".")>-1){G=E.split(".");E=G.shift();F.namespace=G.slice(0).sort().join(".")}else{G=[];F.namespace=""}F.type=E;if(!F.guid)F.guid=A.guid;var K=$[E],D=k.event.special[E]||{};if(!K){K=$[E]=[];if(!D.setup||D.setup.call(_,M,G,C)===false)if(_.addEventListener)_.addEventListener(E,C,false);else if(_.attachEvent)_.attachEvent("on"+E,C)}if(D.add){D.add.call(_,F);if(!F.handler.guid)F.handler.guid=A.guid}K.push(F);k.event.global[E]=true}_=null},global:{},remove:function(B,D,C,M){if(B.nodeType===3||B.nodeType===8)return;if(C===false)C=H;var Q,F,K,O,L=0,N,I,_,E,R,G,P,J=k.hasData(B)&&k._data(B),A=J&&J.events;if(!J||!A)return;if(D&&D.type){C=D.handler;D=D.type}if(!D||typeof D==="string"&&D.charAt(0)==="."){D=D||"";for(F in A)k.event.remove(B,F+D);return}D=D.split(" ");while((F=D[L++])){P=F;G=null;N=F.indexOf(".")<0;I=[];if(!N){I=F.split(".");F=I.shift();_=new RegExp("(^|\\.)"+k.map(I.slice(0).sort(),Q0).join("\\.(?:.*\\.)?")+"(\\.|$)")}R=A[F];if(!R)continue;if(!C){for(O=0;O<R.length;O++){G=R[O];if(N||_.test(G.namespace)){k.event.remove(B,P,G.handler,O);R.splice(O--,1)}}continue}E=k.event.special[F]||{};for(O=M||0;O<R.length;O++){G=R[O];if(C.guid===G.guid){if(N||_.test(G.namespace)){if(M==null)R.splice(O--,1);if(E.remove)E.remove.call(B,G)}if(M!=null)break}}if(R.length===0||M!=null&&R.length===1){if(!E.teardown||E.teardown.call(B,I)===false)k.removeEvent(B,F,J.handle);Q=null;delete A[F]}}if(k.isEmptyObject(A)){var $=J.handle;if($)$.elem=null;delete J.events;delete J.handle;if(k.isEmptyObject(J))k.removeData(B,$1,true)}},customEvent:{"getData":true,"setData":true,"changeData":true},trigger:function(J,I,_,H){var A=J.type||J,F=[],D;if(A.indexOf("!")>=0){A=A.slice(0,-1);D=true}if(A.indexOf(".")>=0){F=A.split(".");A=F.shift();F.sort()}if((!_||k.event.customEvent[A])&&!k.event.global[A])return;J=typeof J==="object"?J[k.expando]?J:new k.Event(A,J):new k.Event(A);J.type=A;J.exclusive=D;J.namespace=F.join(".");J.namespace_re=new RegExp("(^|\\.)"+F.join("\\.(?:.*\\.)?")+"(\\.|$)");if(H||!_){J.preventDefault();J.stopPropagation()}if(!_){k.each(k.cache,function(){var _=k.expando,$=this[_];if($&&$.events&&$.events[A])k.event.trigger(J,I,$.handle.elem)});return}if(_.nodeType===3||_.nodeType===8)return;J.result=$1;J.target=_;I=I!=null?k.makeArray(I):[];I.unshift(J);var C=_,K=A.indexOf(":")<0?"on"+A:"";do{var $=k._data(C,"handle");J.currentTarget=C;if($)$.apply(C,I);if(K&&k.acceptData(C)&&C[K]&&C[K].apply(C,I)===false){J.result=false;J.preventDefault()}C=C.parentNode||C.ownerDocument||C===J.target.ownerDocument&&K0}while(C&&!J.isPropagationStopped());if(!J.isDefaultPrevented()){var G,B=k.event.special[A]||{};if((!B._default||B._default.call(_.ownerDocument,J)===false)&&!(A==="click"&&k.nodeName(_,"a"))&&k.acceptData(_)){try{if(K&&_[A]){G=_[K];if(G)_[K]=null;k.event.triggered=A;_[A]()}}catch(E){}if(G)_[K]=G;k.event.triggered=$1}}return J.result},handle:function(C){C=k.event.fix(C||K0.event);var $=((k._data(this,"events")||{})[C.type]||[]).slice(0),F=!C.exclusive&&!C.namespace,_=Array.prototype.slice.call(arguments,0);_[0]=C;C.currentTarget=this;for(var B=0,E=$.length;B<E;B++){var A=$[B];if(F||C.namespace_re.test(A.namespace)){C.handler=A.handler;C.data=A.data;C.handleObj=A;var D=A.handler.apply(this,_);if(D!==$1){C.result=D;if(D===false){C.preventDefault();C.stopPropagation()}}if(C.isImmediatePropagationStopped())break}}return C.result},props:"altKey attrChange attrName bubbles button cancelable charCode clientX clientY ctrlKey currentTarget data detail eventPhase fromElement handler keyCode layerX layerY metaKey newValue offsetX offsetY pageX pageY prevValue relatedNode relatedTarget screenX screenY shiftKey srcElement target toElement view wheelDelta which".split(" "),fix:function(D){if(D[k.expando])return D;var C=D;D=k.Event(C);for(var _=this.props.length,A;_;){A=this.props[--_];D[A]=C[A]}if(!D.target)D.target=D.srcElement||v0;if(D.target.nodeType===3)D.target=D.target.parentNode;if(!D.relatedTarget&&D.fromElement)D.relatedTarget=D.fromElement===D.target?D.toElement:D.fromElement;if(D.pageX==null&&D.clientX!=null){var B=D.target.ownerDocument||v0,$=B.documentElement,E=B.body;D.pageX=D.clientX+($&&$.scrollLeft||E&&E.scrollLeft||0)-($&&$.clientLeft||E&&E.clientLeft||0);D.pageY=D.clientY+($&&$.scrollTop||E&&E.scrollTop||0)-($&&$.clientTop||E&&E.clientTop||0)}if(D.which==null&&(D.charCode!=null||D.keyCode!=null))D.which=D.charCode!=null?D.charCode:D.keyCode;if(!D.metaKey&&D.ctrlKey)D.metaKey=D.ctrlKey;if(!D.which&&D.button!==$1)D.which=(D.button&1?1:(D.button&2?3:(D.button&4?2:0)));return D},guid:100000000,proxy:k.proxy,special:{ready:{setup:k.bindReady,teardown:k.noop},live:{add:function($){k.event.add(this,o($.origType,$.selector),k.extend({},$,{handler:K1,guid:$.handler.guid}))},remove:function($){k.event.remove(this,o($.origType,$.selector),$)}},beforeunload:{setup:function(_,A,$){if(k.isWindow(this))this.onbeforeunload=$},teardown:function(_,$){if(this.onbeforeunload===$)this.onbeforeunload=null}}}};k.removeEvent=v0.removeEventListener?function(_,A,$){if(_.removeEventListener)_.removeEventListener(A,$,false)}:function(_,A,$){if(_.detachEvent)_.detachEvent("on"+A,$)};k.Event=function(_,$){if(!this.preventDefault)return new k.Event(_,$);if(_&&_.type){this.originalEvent=_;this.type=_.type;this.isDefaultPrevented=(_.defaultPrevented||_.returnValue===false||_.getPreventDefault&&_.getPreventDefault())?L0:H}else this.type=_;if($)k.extend(this,$);this.timeStamp=k.now();this[k.expando]=true};function H(){return false}function L0(){return true}k.Event.prototype={preventDefault:function(){this.isDefaultPrevented=L0;var $=this.originalEvent;if(!$)return;if($.preventDefault)$.preventDefault();else $.returnValue=false},stopPropagation:function(){this.isPropagationStopped=L0;var $=this.originalEvent;if(!$)return;if($.stopPropagation)$.stopPropagation();$.cancelBubble=true},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=L0;this.stopPropagation()},isDefaultPrevented:H,isPropagationStopped:H,isImmediatePropagationStopped:H};var n0=function(_){var B=_.relatedTarget,$=false,A=_.type;_.type=_.data;if(B!==this){if(B)$=k.contains(this,B);if(!$){k.event.handle.apply(this,arguments);_.type=A}}},x=function($){$.type=$.data;k.event.handle.apply(this,arguments)};k.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(_,$){k.event.special[_]={setup:function(A){k.event.add(this,$,A&&A.selector?x:n0,_)},teardown:function(_){k.event.remove(this,$,_&&_.selector?x:n0)}}});if(!k.support.submitBubbles)k.event.special.submit={setup:function($,_){if(!k.nodeName(this,"form")){k.event.add(this,"click.specialSubmit",function(A){var $=A.target,_=$.type;if((_==="submit"||_==="image")&&k($).closest("form").length)f("submit",this,arguments)});k.event.add(this,"keypress.specialSubmit",function(A){var $=A.target,_=$.type;if((_==="text"||_==="password")&&k($).closest("form").length&&A.keyCode===13)f("submit",this,arguments)})}else return false},teardown:function($){k.event.remove(this,".specialSubmit")}};if(!k.support.changeBubbles){var J0,U0=function(_){var A=_.type,$=_.value;if(A==="radio"||A==="checkbox")$=_.checked;else if(A==="select-multiple")$=_.selectedIndex>-1?k.map(_.options,function($){return $.selected}).join("-"):"";else if(k.nodeName(_,"select"))$=_.selectedIndex;return $},G=function G(B){var _=B.target,A,$;if(!o0.test(_.nodeName)||_.readOnly)return;A=k._data(_,"_change_data");$=U0(_);if(B.type!=="focusout"||_.type!=="radio")k._data(_,"_change_data",$);if(A===$1||$===A)return;if(A!=null||$){B.type="change";B.liveFired=$1;k.event.trigger(B,arguments[1],_)}};k.event.special.change={filters:{focusout:G,beforedeactivate:G,click:function(A){var $=A.target,_=k.nodeName($,"input")?$.type:"";if(_==="radio"||_==="checkbox"||k.nodeName($,"select"))G.call(this,A)},keydown:function(A){var $=A.target,_=k.nodeName($,"input")?$.type:"";if((A.keyCode===13&&!k.nodeName($,"textarea"))||(A.keyCode===32&&(_==="checkbox"||_==="radio"))||_==="select-multiple")G.call(this,A)},beforeactivate:function(_){var $=_.target;k._data($,"_change_data",U0($))}},setup:function(_,A){if(this.type==="file")return false;for(var $ in J0)k.event.add(this,$+".specialChange",J0[$]);return o0.test(this.nodeName)},teardown:function($){k.event.remove(this,".specialChange");return o0.test(this.nodeName)}};J0=k.event.special.change.filters;J0.focus=J0.beforeactivate}function f(_,$,A){var B=k.extend({},A[0]);B.type=_;B.originalEvent={};B.liveFired=$1;k.event.handle.call($,B);if(B.isDefaultPrevented())A[0].preventDefault()}if(!k.support.focusinBubbles)k.each({focus:"focusin",blur:"focusout"},function(B,_){var A=0;k.event.special[_]={setup:function(){if(A++===0)v0.addEventListener(B,$,true)},teardown:function(){if(--A===0)v0.removeEventListener(B,$,true)}};function $($){var A=k.event.fix($);A.type=_;A.originalEvent={};k.event.trigger(A,null,A.target);if(A.isDefaultPrevented())$.preventDefault()}});k.each(["bind","one"],function($,_){k.fn[_]=function(C,D,F){var B;if(typeof C==="object"){for(var $ in C)this[_]($,D,C[$],F);return this}if(arguments.length===2||D===false){F=D;D=$1}if(_==="one"){B=function($){k(this).unbind($,B);return F.apply(this,arguments)};B.guid=F.guid||k.guid++}else B=F;if(C==="unload"&&_!=="one")this.one(C,D,F);else for(var A=0,E=this.length;A<E;A++)k.event.add(this[A],C,B,D);return this}});k.fn.extend({unbind:function(A,C){if(typeof A==="object"&&!A.preventDefault){for(var $ in A)this.unbind($,A[$])}else for(var _=0,B=this.length;_<B;_++)k.event.remove(this[_],A,C);return this},delegate:function(_,$,A,B){return this.live($,A,B,_)},undelegate:function(_,$,A){if(arguments.length===0)return this.unbind("live");else return this.die($,null,A,_)},trigger:function($,_){return this.each(function(){k.event.trigger($,_,this)})},triggerHandler:function($,_){if(this[0])return k.event.trigger($,_,this[0],true)},toggle:function(C){var A=arguments,B=C.guid||k.guid++,$=0,_=function(B){var _=(k.data(this,"lastToggle"+C.guid)||0)%$;k.data(this,"lastToggle"+C.guid,_+1);B.preventDefault();return A[_].apply(this,arguments)||false};_.guid=B;while($<A.length)A[$++].guid=B;return this.click(_)},hover:function($,_){return this.mouseenter($).mouseleave(_||$)}});var Z0={focus:"focusin",blur:"focusout",mouseenter:"mouseover",mouseleave:"mouseout"};k.each(["live","die"],function($,_){k.fn[_]=function($,L,F,K){var B,J=0,G,D,C,A=K||this.selector,M=K?this:k(this.context);if(typeof $==="object"&&!$.preventDefault){for(var I in $)M[_](I,L,$[I],A);return this}if(_==="die"&&!$&&K&&K.charAt(0)==="."){M.unbind(K);return this}if(L===false||k.isFunction(L)){F=L||H;L=$1}$=($||"").split(" ");while((B=$[J++])!=null){G=s0.exec(B);D="";if(G){D=G[0];B=B.replace(s0,"")}if(B==="hover"){$.push("mouseenter"+D,"mouseleave"+D);continue}C=B;if(Z0[B]){$.push(Z0[B]+D);B=B+D}else B=(Z0[B]||B)+D;if(_==="live"){for(var N=0,E=M.length;N<E;N++)k.event.add(M[N],"live."+o(B,A),{data:L,selector:A,handler:F,origType:B,origHandler:F,preType:C})}else M.unbind("live."+o(B,A),F)}return this}});function K1(N){var J,H,F,I,C,A,O,K,E,M,B,$,P,L=[],G=[],_=k._data(this,"events");if(N.liveFired===this||!_||!_.live||N.target.disabled||N.button&&N.type==="click")return;if(N.namespace)$=new RegExp("(^|\\.)"+N.namespace.split(".").join("\\.(?:.*\\.)?")+"(\\.|$)");N.liveFired=this;var D=_.live.slice(0);for(O=0;O<D.length;O++){C=D[O];if(C.origType.replace(s0,"")===N.type)G.push(C.selector);else D.splice(O--,1)}I=k(N.target).closest(G,N.currentTarget);for(K=0,E=I.length;K<E;K++){B=I[K];for(O=0;O<D.length;O++){C=D[O];if(B.selector===C.selector&&(!$||$.test(C.namespace))&&!B.elem.disabled){A=B.elem;F=null;if(C.preType==="mouseenter"||C.preType==="mouseleave"){N.type=C.preType;F=k(N.relatedTarget).closest(C.selector)[0];if(F&&k.contains(A,F))F=A}if(!F||F!==A)L.push({elem:A,handleObj:C,level:B.level})}}}for(K=0,E=L.length;K<E;K++){I=L[K];if(H&&I.level>H)break;N.currentTarget=I.elem;N.data=I.handleObj.data;N.handleObj=I.handleObj;P=I.handleObj.origHandler.apply(I.elem,arguments);if(P===false||N.isPropagationStopped()){H=I.level;if(P===false)J=false;if(N.isImmediatePropagationStopped())break}}return J}function o(_,$){return(_&&_!=="*"?_+".":"")+$.replace(D1,"`").replace(e,"&")}k.each(("blur focus focusin focusout load resize scroll unload click dblclick "+"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave "+"change select submit keydown keypress keyup error").split(" "),function($,_){k.fn[_]=function($,A){if(A==null){A=$;$=null}return arguments.length>0?this.bind(_,$,A):this.trigger(_)};if(k.attrFn)k.attrFn[_]=true});(function(){var I=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,E=0,D=Object.prototype.toString,Q=false,A=true,O=/\\/g,B=/\W/;[0,0].sort(function(){A=false;return 0});var $=function(B,Q,G,T){G=G||[];Q=Q||v0;var V=Q;if(Q.nodeType!==1&&Q.nodeType!==9)return[];if(!B||typeof B!=="string")return G;var O,P,U,E,R,C,S,M,F=true,L=$.isXML(Q),_=[],A=B;do{I.exec("");O=I.exec(A);if(O){A=O[3];_.push(O[1]);if(O[2]){E=O[3];break}}}while(O);if(_.length>1&&J.exec(B)){if(_.length===2&&N.relative[_[0]])P=K(_[0]+_[1],Q);else{P=N.relative[_[0]]?[Q]:$(_.shift(),Q);while(_.length){B=_.shift();if(N.relative[B])B+=_.shift();P=K(B,P)}}}else{if(!T&&_.length>1&&Q.nodeType===9&&!L&&N.match.ID.test(_[0])&&!N.match.ID.test(_[_.length-1])){R=$.find(_.shift(),Q,L);Q=R.expr?$.filter(R.expr,R.set)[0]:R.set[0]}if(Q){R=T?{expr:_.pop(),set:H(T)}:$.find(_.pop(),_.length===1&&(_[0]==="~"||_[0]==="+")&&Q.parentNode?Q.parentNode:Q,L);P=R.expr?$.filter(R.expr,R.set):R.set;if(_.length>0)U=H(P);else F=false;while(_.length){C=_.pop();S=C;if(!N.relative[C])C="";else S=_.pop();if(S==null)S=Q;N.relative[C](U,S,L)}}else U=_=[]}if(!U)U=P;if(!U)$.error(C||B);if(D.call(U)==="[object Array]"){if(!F)G.push.apply(G,U);else if(Q&&Q.nodeType===1){for(M=0;U[M]!=null;M++)if(U[M]&&(U[M]===true||U[M].nodeType===1&&$.contains(Q,U[M])))G.push(P[M])}else for(M=0;U[M]!=null;M++)if(U[M]&&U[M].nodeType===1)G.push(P[M])}else H(U,G);if(E){$(E,V,G,T);$.uniqueSort(G)}return G};$.uniqueSort=function(_){if(G){Q=A;_.sort(G);if(Q)for(var $=1;$<_.length;$++)if(_[$]===_[$-1])_.splice($--,1)}return _};$.matches=function(_,A){return $(_,null,null,A)};$.matchesSelector=function(A,_){return $(_,null,null,[A]).length>0};$.find=function(A,E,G){var D;if(!A)return[];for(var _=0,F=N.order.length;_<F;_++){var $,B=N.order[_];if(($=N.leftMatch[B].exec(A))){var C=$[1];$.splice(1,1);if(C.substr(C.length-1)!=="\\"){$[1]=($[1]||"").replace(O,"");D=N.find[B]($,E,G);if(D!=null){A=A.replace(N.match[B],"");break}}}}if(!D)D=typeof E.getElementsByTagName!=="undefined"?E.getElementsByTagName("*"):[];return{set:D,expr:A}};$.filter=function(I,M,F,C){var G,Q,J=I,O=[],P=M,K=M&&M[0]&&$.isXML(M[0]);while(I&&M.length){for(var A in N.filter)if((G=N.leftMatch[A].exec(I))!=null&&G[2]){var D,_,E=N.filter[A],L=G[1];Q=false;G.splice(1,1);if(L.substr(L.length-1)==="\\")continue;if(P===O)O=[];if(N.preFilter[A]){G=N.preFilter[A](G,P,F,O,C,K);if(!G)Q=D=true;else if(G===true)continue}if(G)for(var H=0;(_=P[H])!=null;H++)if(_){D=E(_,G,H,P);var B=C^!!D;if(F&&D!=null){if(B)Q=true;else P[H]=false}else if(B){O.push(_);Q=true}}if(D!==$1){if(!F)P=O;I=I.replace(N.match[A],"");if(!Q)return[];break}}if(I===J)if(Q==null)$.error(I);else break;J=I}return P};$.error=function($){throw"Syntax error, unrecognized expression: "+$};var N=$.selectors={order:["ID","NAME","TAG"],match:{ID:/#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/,CLASS:/\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/,NAME:/\[name=['"]*((?:[\w\u00c0-\uFFFF\-]|\\.)+)['"]*\]/,ATTR:/\[\s*((?:[\w\u00c0-\uFFFF\-]|\\.)+)\s*(?:(\S?=)\s*(?:(['"])(.*?)\3|(#?(?:[\w\u00c0-\uFFFF\-]|\\.)*)|)|)\s*\]/,TAG:/^((?:[\w\u00c0-\uFFFF\*\-]|\\.)+)/,CHILD:/:(only|nth|last|first)-child(?:\(\s*(even|odd|(?:[+\-]?\d+|(?:[+\-]?\d*)?n\s*(?:[+\-]\s*\d+)?))\s*\))?/,POS:/:(nth|eq|gt|lt|first|last|even|odd)(?:\((\d*)\))?(?=[^\-]|$)/,PSEUDO:/:((?:[\w\u00c0-\uFFFF\-]|\\.)+)(?:\((['"]?)((?:\([^\)]+\)|[^\(\)]*)+)\2\))?/},leftMatch:{},attrMap:{"class":"className","for":"htmlFor"},attrHandle:{href:function($){return $.getAttribute("href")},type:function($){return $.getAttribute("type")}},relative:{"+":function(H,F){var C=typeof F==="string",E=C&&!B.test(F),_=C&&!E;if(E)F=F.toLowerCase();for(var A=0,G=H.length,D;A<G;A++)if((D=H[A])){while((D=D.previousSibling)&&D.nodeType!==1);H[A]=_||D&&D.nodeName.toLowerCase()===F?D||false:D===F}if(_)$.filter(F,H,true)},">":function(F,D){var C,A=typeof D==="string",_=0,E=F.length;if(A&&!B.test(D)){D=D.toLowerCase();for(;_<E;_++){C=F[_];if(C){var G=C.parentNode;F[_]=G.nodeName.toLowerCase()===D?G:false}}}else{for(;_<E;_++){C=F[_];if(C)F[_]=A?C.parentNode:C.parentNode===D}if(A)$.filter(D,F,true)}},"":function(D,A,G){var _,C=E++,$=F;if(typeof A==="string"&&!B.test(A)){A=A.toLowerCase();_=A;$=L}$("parentNode",A,C,D,_,G)},"~":function(D,A,G){var _,C=E++,$=F;if(typeof A==="string"&&!B.test(A)){A=A.toLowerCase();_=A;$=L}$("previousSibling",A,C,D,_,G)}},find:{ID:function($,A,B){if(typeof A.getElementById!=="undefined"&&!B){var _=A.getElementById($[1]);return _&&_.parentNode?[_]:[]}},NAME:function($,A){if(typeof A.getElementsByName!=="undefined"){var B=[],D=A.getElementsByName($[1]);for(var _=0,C=D.length;_<C;_++)if(D[_].getAttribute("name")===$[1])B.push(D[_]);return B.length===0?null:B}},TAG:function($,_){if(typeof _.getElementsByTagName!=="undefined")return _.getElementsByTagName($[1])}},preFilter:{CLASS:function(_,E,$,C,D,F){_=" "+_[1].replace(O,"")+" ";if(F)return _;for(var A=0,B;(B=E[A])!=null;A++)if(B)if(D^(B.className&&(" "+B.className+" ").replace(/[\t\n\r]/g," ").indexOf(_)>=0)){if(!$)C.push(B)}else if($)E[A]=false;return false},ID:function($){return $[1].replace(O,"")},TAG:function($,_){return $[1].replace(O,"").toLowerCase()},CHILD:function(_){if(_[1]==="nth"){if(!_[2])$.error(_[0]);_[2]=_[2].replace(/^\+|\s*/g,"");var A=/(-?)(\d*)(?:n([+\-]?\d*))?/.exec(_[2]==="even"&&"2n"||_[2]==="odd"&&"2n+1"||!/\D/.test(_[2])&&"0n+"+_[2]||_[2]);_[2]=(A[1]+(A[2]||1))-0;_[3]=A[3]-0}else if(_[2])$.error(_[0]);_[0]=E++;return _},ATTR:function(_,C,$,A,B,E){var D=_[1]=_[1].replace(O,"");if(!E&&N.attrMap[D])_[1]=N.attrMap[D];_[4]=(_[4]||_[5]||"").replace(O,"");if(_[2]==="~=")_[4]=" "+_[4]+" ";return _},PSEUDO:function(A,E,_,B,D){if(A[1]==="not"){if((I.exec(A[3])||"").length>1||/^\w/.test(A[3]))A[3]=$(A[3],null,null,E);else{var C=$.filter(A[3],E,_,true^D);if(!_)B.push.apply(B,C);return false}}else if(N.match.POS.test(A[0])||N.match.CHILD.test(A[0]))return true;return A},POS:function($){$.unshift(true);return $}},filters:{enabled:function($){return $.disabled===false&&$.type!=="hidden"},disabled:function($){return $.disabled===true},checked:function($){return $.checked===true},selected:function($){if($.parentNode)$.parentNode.selectedIndex;return $.selected===true},parent:function($){return!!$.firstChild},empty:function($){return!$.firstChild},has:function(B,A,_){return!!$(_[3],B).length},header:function($){return(/h\d/i).test($.nodeName)},text:function($){var A=$.getAttribute("type"),_=$.type;return $.nodeName.toLowerCase()==="input"&&"text"===_&&(A===_||A===null)},radio:function($){return $.nodeName.toLowerCase()==="input"&&"radio"===$.type},checkbox:function($){return $.nodeName.toLowerCase()==="input"&&"checkbox"===$.type},file:function($){return $.nodeName.toLowerCase()==="input"&&"file"===$.type},password:function($){return $.nodeName.toLowerCase()==="input"&&"password"===$.type},submit:function($){var _=$.nodeName.toLowerCase();return(_==="input"||_==="button")&&"submit"===$.type},image:function($){return $.nodeName.toLowerCase()==="input"&&"image"===$.type},reset:function($){var _=$.nodeName.toLowerCase();return(_==="input"||_==="button")&&"reset"===$.type},button:function($){var _=$.nodeName.toLowerCase();return _==="input"&&"button"===$.type||_==="button"},input:function($){return(/input|select|textarea|button/i).test($.nodeName)},focus:function($){return $===$.ownerDocument.activeElement}},setFilters:{first:function(_,$){return $===0},last:function(A,_,$,B){return _===B.length-1},even:function(_,$){return $%2===0},odd:function(_,$){return $%2===1},lt:function(A,_,$){return _<$[3]-0},gt:function(A,_,$){return _>$[3]-0},nth:function(A,_,$){return $[3]-0===_},eq:function(A,_,$){return $[3]-0===_}},filter:{PSEUDO:function(C,A,B,H){var G=A[1],_=N.filters[G];if(_)return _(C,B,A,H);else if(G==="contains")return(C.textContent||C.innerText||$.getText([C])||"").indexOf(A[3])>=0;else if(G==="not"){var E=A[3];for(var D=0,F=E.length;D<F;D++)if(E[D]===C)return false;return true}else $.error(G)},CHILD:function(A,$){var B=$[1],C=A;switch(B){case"only":case"first":while((C=C.previousSibling))if(C.nodeType===1)return false;if(B==="first")return true;C=A;case"last":while((C=C.nextSibling))if(C.nodeType===1)return false;return true;case"nth":var D=$[2],_=$[3];if(D===1&&_===0)return true;var E=$[0],H=A.parentNode;if(H&&(H.sizcache!==E||!A.nodeIndex)){var G=0;for(C=H.firstChild;C;C=C.nextSibling)if(C.nodeType===1)C.nodeIndex=++G;H.sizcache=E}var F=A.nodeIndex-_;if(D===0)return F===0;else return(F%D===0&&F/D>=0)}},ID:function(_,$){return _.nodeType===1&&_.getAttribute("id")===$},TAG:function(_,$){return($==="*"&&_.nodeType===1)||_.nodeName.toLowerCase()===$},CLASS:function(_,$){return(" "+(_.className||_.getAttribute("class"))+" ").indexOf($)>-1},ATTR:function(A,$){var E=$[1],C=N.attrHandle[E]?N.attrHandle[E](A):A[E]!=null?A[E]:A.getAttribute(E),_=C+"",B=$[2],D=$[4];return C==null?B==="!=":B==="="?_===D:B==="*="?_.indexOf(D)>=0:B==="~="?(" "+_+" ").indexOf(D)>=0:!D?_&&C!==false:B==="!="?_!==D:B==="^="?_.indexOf(D)===0:B==="$="?_.substr(_.length-D.length)===D:B==="|="?_===D||_.substr(0,D.length+1)===D+"-":false},POS:function(B,_,A,D){var C=_[2],$=N.setFilters[C];if($)return $(B,A,_,D)}}},J=N.match.POS,M=function(_,$){return"\\"+($-0+1)};for(var C in N.match){N.match[C]=new RegExp(N.match[C].source+(/(?![^\[]*\])(?![^\(]*\))/.source));N.leftMatch[C]=new RegExp(/(^(?:.|\r|\n)*?)/.source+N.match[C].source.replace(/\\(\d+)/g,M))}var H=function(_,$){_=Array.prototype.slice.call(_,0);if($){$.push.apply($,_);return $}return _};try{Array.prototype.slice.call(v0.documentElement.childNodes,0)[0].nodeType}catch(P){H=function(C,B){var $=0,_=B||[];if(D.call(C)==="[object Array]")Array.prototype.push.apply(_,C);else if(typeof C.length==="number"){for(var A=C.length;$<A;$++)_.push(C[$])}else for(;C[$];$++)_.push(C[$]);return _}}var G,_;if(v0.documentElement.compareDocumentPosition)G=function($,_){if($===_){Q=true;return 0}if(!$.compareDocumentPosition||!_.compareDocumentPosition)return $.compareDocumentPosition?-1:1;return $.compareDocumentPosition(_)&4?-1:1};else{G=function(D,I){if(D===I){Q=true;return 0}else if(D.sourceIndex&&I.sourceIndex)return D.sourceIndex-I.sourceIndex;var A,F,C=[],H=[],B=D.parentNode,G=I.parentNode,E=B;if(B===G)return _(D,I);else if(!B)return-1;else if(!G)return 1;while(E){C.unshift(E);E=E.parentNode}E=G;while(E){H.unshift(E);E=E.parentNode}A=C.length;F=H.length;for(var $=0;$<A&&$<F;$++)if(C[$]!==H[$])return _(C[$],H[$]);return $===A?_(D,H[$],-1):_(C[$],I,1)};_=function($,B,A){if($===B)return A;var _=$.nextSibling;while(_){if(_===B)return-1;_=_.nextSibling}return 1}}$.getText=function(B){var C="",A;for(var _=0;B[_];_++){A=B[_];if(A.nodeType===3||A.nodeType===4)C+=A.nodeValue;else if(A.nodeType!==8)C+=$.getText(A.childNodes)}return C};(function(){var $=v0.createElement("div"),A="script"+(new Date()).getTime(),_=v0.documentElement;$.innerHTML="<a name='"+A+"'/>";_.insertBefore($,_.firstChild);if(v0.getElementById(A)){N.find.ID=function($,A,B){if(typeof A.getElementById!=="undefined"&&!B){var _=A.getElementById($[1]);return _?_.id===$[1]||typeof _.getAttributeNode!=="undefined"&&_.getAttributeNode("id").nodeValue===$[1]?[_]:$1:[]}};N.filter.ID=function(_,$){var A=typeof _.getAttributeNode!=="undefined"&&_.getAttributeNode("id");return _.nodeType===1&&A&&A.nodeValue===$}}_.removeChild($);_=$=null})();(function(){var $=v0.createElement("div");$.appendChild(v0.createComment(""));if($.getElementsByTagName("*").length>0)N.find.TAG=function($,B){var C=B.getElementsByTagName($[1]);if($[1]==="*"){var A=[];for(var _=0;C[_];_++)if(C[_].nodeType===1)A.push(C[_]);C=A}return C};$.innerHTML="<a href='#'></a>";if($.firstChild&&typeof $.firstChild.getAttribute!=="undefined"&&$.firstChild.getAttribute("href")!=="#")N.attrHandle.href=function($){return $.getAttribute("href",2)};$=null})();if(v0.querySelectorAll)(function(){var _=$,A=v0.createElement("div"),C="__sizzle__";A.innerHTML="<p class='TEST'></p>";if(A.querySelectorAll&&A.querySelectorAll(".TEST").length===0)return;$=function(M,O,I,P){O=O||v0;if(!P&&!$.isXML(O)){var K=/^(\w+$)|^\.([\w\-]+$)|^#([\w\-]+$)/.exec(M);if(K&&(O.nodeType===1||O.nodeType===9))if(K[1])return H(O.getElementsByTagName(M),I);else if(K[2]&&N.find.CLASS&&O.getElementsByClassName)return H(O.getElementsByClassName(K[2]),I);if(O.nodeType===9){if(M==="body"&&O.body)return H([O.body],I);else if(K&&K[3]){var B=O.getElementById(K[3]);if(B&&B.parentNode){if(B.id===K[3])return H([B],I)}else return H([],I)}try{return H(O.querySelectorAll(M),I)}catch(E){}}else if(O.nodeType===1&&O.nodeName.toLowerCase()!=="object"){var D=O,L=O.getAttribute("id"),F=L||C,G=O.parentNode,J=/^\s*[+~]/.test(M);if(!L)O.setAttribute("id",F);else F=F.replace(/'/g,"\\$&");if(J&&G)O=O.parentNode;try{if(!J||G)return H(O.querySelectorAll("[id='"+F+"'] "+M),I)}catch(A){}finally{if(!L)D.removeAttribute("id")}}}return _(M,O,I,P)};for(var B in _)$[B]=_[B];A=null})();(function(){var D=v0.documentElement,C=D.matchesSelector||D.mozMatchesSelector||D.webkitMatchesSelector||D.msMatchesSelector;if(C){var A=!C.call(v0.createElement("div"),"div"),B=false;try{C.call(v0.documentElement,"[test!='']:sizzle")}catch(_){B=true}$.matchesSelector=function(D,_){_=_.replace(/\=\s*([^'"\]]*)\s*\]/g,"='$1']");if(!$.isXML(D)){try{if(B||!N.match.PSEUDO.test(_)&&!/!=/.test(_)){var E=C.call(D,_);if(E||!A||D.document&&D.document.nodeType!==11)return E}}catch(F){}}return $(_,null,null,[D]).length>0}}})();(function(){var $=v0.createElement("div");$.innerHTML="<div class='test e'></div><div class='test'></div>";if(!$.getElementsByClassName||$.getElementsByClassName("e").length===0)return;$.lastChild.className="e";if($.getElementsByClassName("e").length===1)return;N.order.splice(1,0,"CLASS");N.find.CLASS=function($,_,A){if(typeof _.getElementsByClassName!=="undefined"&&!A)return _.getElementsByClassName($[1])};$=null})();function L(G,C,D,F,A,H){for(var _=0,E=F.length;_<E;_++){var B=F[_];if(B){var $=false;B=B[G];while(B){if(B.sizcache===D){$=F[B.sizset];break}if(B.nodeType===1&&!H){B.sizcache=D;B.sizset=_}if(B.nodeName.toLowerCase()===C){$=B;break}B=B[G]}F[_]=$}}}function F(H,D,E,G,B,I){for(var A=0,F=G.length;A<F;A++){var C=G[A];if(C){var _=false;C=C[H];while(C){if(C.sizcache===E){_=G[C.sizset];break}if(C.nodeType===1){if(!I){C.sizcache=E;C.sizset=A}if(typeof D!=="string"){if(C===D){_=true;break}}else if($.filter(D,[C]).length>0){_=C;break}}C=C[H]}G[A]=_}}}if(v0.documentElement.contains)$.contains=function($,_){return $!==_&&($.contains?$.contains(_):true)};else if(v0.documentElement.compareDocumentPosition)$.contains=function($,_){return!!($.compareDocumentPosition(_)&16)};else $.contains=function(){return false};$.isXML=function($){var _=($?$.ownerDocument||$:0).documentElement;return _?_.nodeName!=="HTML":false};var K=function(D,E){var _,C=[],B="",F=E.nodeType?[E]:E;while((_=N.match.PSEUDO.exec(D))){B+=_[0];D=D.replace(N.match.PSEUDO,"")}D=N.relative[D]?D+"*":D;for(var A=0,G=F.length;A<G;A++)$(D,F[A],C);return $.filter(B,C)};k.find=$;k.expr=$.selectors;k.expr[":"]=k.expr.filters;k.unique=$.uniqueSort;k.text=$.getText;k.isXMLDoc=$.isXML;k.contains=$.contains})();var F0=/Until$/,I=/^(?:parents|prevUntil|prevAll)/,u0=/,/,_=/^.[^:#\[\.,]*$/,S0=Array.prototype.slice,a=k.expr.match.POS,Y0={children:true,contents:true,next:true,prev:true};k.fn.extend({find:function(_){var B=this,$,F;if(typeof _!=="string")return k(_).filter(function(){for($=0,F=B.length;$<F;$++)if(k.contains(B[$],this))return true});var D=this.pushStack("","find",_),C,E,A;for($=0,F=this.length;$<F;$++){C=D.length;k.find(_,this[$],D);if($>0)for(E=C;E<D.length;E++)for(A=0;A<C;A++)if(D[A]===D[E]){D.splice(E--,1);break}}return D},has:function($){var _=k($);return this.filter(function(){for(var $=0,A=_.length;$<A;$++)if(k.contains(this,_[$]))return true})},not:function($){return this.pushStack(h(this,$,false),"not",$)},filter:function($){return this.pushStack(h(this,$,true),"filter",$)},is:function($){return!!$&&(typeof $==="string"?k.filter($,this).length>0:this.filter($).length>0)},closest:function(I,F){var G=[],A,H,E=this[0];if(k.isArray(I)){var _,C,B={},$=1;if(E&&I.length){for(A=0,H=I.length;A<H;A++){C=I[A];if(!B[C])B[C]=a.test(C)?k(C,F||this.context):C}while(E&&E.ownerDocument&&E!==F){for(C in B){_=B[C];if(_.jquery?_.index(E)>-1:k(E).is(_))G.push({selector:C,elem:E,level:$})}E=E.parentNode;$++}}return G}var D=a.test(I)||typeof I!=="string"?k(I,F||this.context):0;for(A=0,H=this.length;A<H;A++){E=this[A];while(E)if(D?D.index(E)>-1:k.find.matchesSelector(E,I)){G.push(E);break}else{E=E.parentNode;if(!E||!E.ownerDocument||E===F||E.nodeType===11)break}}G=G.length>1?k.unique(G):G;return this.pushStack(G,"closest",I)},index:function($){if(!$||typeof $==="string")return k.inArray(this[0],$?k($):this.parent().children());return k.inArray($.jquery?$[0]:$,this)},add:function($,A){var _=typeof $==="string"?k($,A):k.makeArray($&&$.nodeType?[$]:$),B=k.merge(this.get(),_);return this.pushStack(w(_[0])||w(B[0])?B:k.unique(B))},andSelf:function(){return this.add(this.prevObject)}});function w($){return!$||!$.parentNode||$.parentNode.nodeType===11}k.each({parent:function($){var _=$.parentNode;return _&&_.nodeType!==11?_:null},parents:function($){return k.dir($,"parentNode")},parentsUntil:function(A,_,$){return k.dir(A,"parentNode",$)},next:function($){return k.nth($,2,"nextSibling")},prev:function($){return k.nth($,2,"previousSibling")},nextAll:function($){return k.dir($,"nextSibling")},prevAll:function($){return k.dir($,"previousSibling")},nextUntil:function(A,_,$){return k.dir(A,"nextSibling",$)},prevUntil:function(A,_,$){return k.dir(A,"previousSibling",$)},siblings:function($){return k.sibling($.parentNode.firstChild,$)},children:function($){return k.sibling($.firstChild)},contents:function($){return k.nodeName($,"iframe")?$.contentDocument||$.contentWindow.document:k.makeArray($.childNodes)}},function($,_){k.fn[$]=function(A,B){var D=k.map(this,_,A),C=S0.call(arguments);if(!F0.test($))B=A;if(B&&typeof B==="string")D=k.filter(B,D);D=this.length>1&&!Y0[$]?k.unique(D):D;if((this.length>1||u0.test(B))&&I.test($))D=D.reverse();return this.pushStack(D,$,C.join(","))}});k.extend({filter:function($,_,A){if(A)$=":not("+$+")";return _.length===1?k.find.matchesSelector(_[0],$)?[_[0]]:[]:k.find.matches($,_)},dir:function(_,C,$){var A=[],B=_[C];while(B&&B.nodeType!==9&&($===$1||B.nodeType!==1||!k(B).is($))){if(B.nodeType===1)A.push(B);B=B[C]}return A},nth:function(B,A,C,_){A=A||1;var $=0;for(;B;B=B[C])if(B.nodeType===1&&++$===A)break;return B},sibling:function(A,$){var _=[];for(;A;A=A.nextSibling)if(A.nodeType===1&&A!==$)_.push(A);return _}});function h(C,A,$){A=A||0;if(k.isFunction(A))return k.grep(C,function(B,_){var C=!!A.call(B,_,B);return C===$});else if(A.nodeType)return k.grep(C,function(B,_){return(B===A)===$});else if(typeof A==="string"){var B=k.grep(C,function($){return $.nodeType===1});if(_.test(A))return k.filter(A,B,!$);else A=k.filter(A,B)}return k.grep(C,function(B,_){return(k.inArray(B,A)>=0)===$})}var e0=/ jQuery\d+="(?:\d+|null)"/g,E=/^\s+/,q0=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/ig,H1=/<([\w:]+)/,b=/<tbody/i,x0=/<|&#?\w+;/,_0=/<(?:script|object|embed|option|style)/i,r=/checked\s*(?:[^=]|=\s*.checked.)/i,D=/\/(java|ecma)script/i,_1=/^\s*<!(?:\[CDATA\[|\-\-)/,i={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>","</map>"],_default:[0,"",""]};i.optgroup=i.option;i.tbody=i.tfoot=i.colgroup=i.caption=i.thead;i.th=i.td;if(!k.support.htmlSerialize)i._default=[1,"div<div>","</div>"];k.fn.extend({text:function($){if(k.isFunction($))return this.each(function(_){var A=k(this);A.text($.call(this,_,A.text()))});if(typeof $!=="object"&&$!==$1)return this.empty().append((this[0]&&this[0].ownerDocument||v0).createTextNode($));return k.text(this)},wrapAll:function($){if(k.isFunction($))return this.each(function(_){k(this).wrapAll($.call(this,_))});if(this[0]){var _=k($,this[0].ownerDocument).eq(0).clone(true);if(this[0].parentNode)_.insertBefore(this[0]);_.map(function(){var $=this;while($.firstChild&&$.firstChild.nodeType===1)$=$.firstChild;return $}).append(this)}return this},wrapInner:function($){if(k.isFunction($))return this.each(function(_){k(this).wrapInner($.call(this,_))});return this.each(function(){var A=k(this),_=A.contents();if(_.length)_.wrapAll($);else A.append($)})},wrap:function($){return this.each(function(){k(this).wrapAll($)})},unwrap:function(){return this.parent().each(function(){if(!k.nodeName(this,"body"))k(this).replaceWith(this.childNodes)}).end()},append:function(){return this.domManip(arguments,true,function($){if(this.nodeType===1)this.appendChild($)})},prepend:function(){return this.domManip(arguments,true,function($){if(this.nodeType===1)this.insertBefore($,this.firstChild)})},before:function(){if(this[0]&&this[0].parentNode)return this.domManip(arguments,false,function($){this.parentNode.insertBefore($,this)});else if(arguments.length){var $=k(arguments[0]);$.push.apply($,this.toArray());return this.pushStack($,"before",arguments)}},after:function(){if(this[0]&&this[0].parentNode)return this.domManip(arguments,false,function($){this.parentNode.insertBefore($,this.nextSibling)});else if(arguments.length){var $=this.pushStack(this,"after",arguments);$.push.apply($,k(arguments[0]).toArray());return $}},remove:function(B,A){for(var $=0,_;(_=this[$])!=null;$++)if(!B||k.filter(B,[_]).length){if(!A&&_.nodeType===1){k.cleanData(_.getElementsByTagName("*"));k.cleanData([_])}if(_.parentNode)_.parentNode.removeChild(_)}return this},empty:function(){for(var $=0,_;(_=this[$])!=null;$++){if(_.nodeType===1)k.cleanData(_.getElementsByTagName("*"));while(_.firstChild)_.removeChild(_.firstChild)}return this},clone:function(_,$){_=_==null?false:_;$=$==null?_:$;return this.map(function(){return k.clone(this,_,$)})},html:function(_){if(_===$1)return this[0]&&this[0].nodeType===1?this[0].innerHTML.replace(e0,""):null;else if(typeof _==="string"&&!_0.test(_)&&(k.support.leadingWhitespace||!E.test(_))&&!i[(H1.exec(_)||["",""])[1].toLowerCase()]){_=_.replace(q0,"<$1></$2>");try{for(var $=0,A=this.length;$<A;$++)if(this[$].nodeType===1){k.cleanData(this[$].getElementsByTagName("*"));this[$].innerHTML=_}}catch(B){this.empty().append(_)}}else if(k.isFunction(_))this.each(function($){var A=k(this);A.html(_.call(this,$,A.html()))});else this.empty().append(_);return this},replaceWith:function($){if(this[0]&&this[0].parentNode){if(k.isFunction($))return this.each(function(_){var B=k(this),A=B.html();B.replaceWith($.call(this,_,A))});if(typeof $!=="string")$=k($).detach();return this.each(function(){var _=this.nextSibling,A=this.parentNode;k(this).remove();if(_)k(_).before($);else k(A).append($)})}else return this.length?this.pushStack(k(k.isFunction($)?$():$),"replaceWith",$):this},detach:function($){return this.remove($,true)},domManip:function(B,$,H){var F,C,I,J,_=B[0],A=[];if(!k.support.checkClone&&arguments.length===3&&typeof _==="string"&&r.test(_))return this.each(function(){k(this).domManip(B,$,H,true)});if(k.isFunction(_))return this.each(function(A){var C=k(this);B[0]=_.call(this,A,$?C.html():$1);C.domManip(B,$,H)});if(this[0]){J=_&&_.parentNode;if(k.support.parentNode&&J&&J.nodeType===11&&J.childNodes.length===this.length)F={fragment:J};else F=k.buildFragment(B,this,A);I=F.fragment;if(I.childNodes.length===1)C=I=I.firstChild;else C=I.firstChild;if(C){$=$&&k.nodeName(C,"tr");for(var G=0,E=this.length,D=E-1;G<E;G++)H.call($?Q1(this[G],C):this[G],F.cacheable||(E>1&&G<D)?k.clone(I,true,true):I)}if(A.length)k.each(A,p)}return this}});function Q1($,_){return k.nodeName($,"table")?($.getElementsByTagName("tbody")[0]||$.appendChild($.ownerDocument.createElement("tbody"))):$}function L1(E,G){if(G.nodeType!==1||!k.hasData(E))return;var B=k.expando,A=k.data(E),D=k.data(G,A);if((A=A[B])){var _=A.events;D=D[B]=k.extend({},A);if(_){delete D.handle;D.events={};for(var C in _)for(var $=0,F=_[C].length;$<F;$++)k.event.add(G,C+(_[C][$].namespace?".":"")+_[C][$].namespace,_[C][$],_[C][$].data)}}}function $(_,A){var $;if(A.nodeType!==1)return;if(A.clearAttributes)A.clearAttributes();if(A.mergeAttributes)A.mergeAttributes(_);$=A.nodeName.toLowerCase();if($==="object")A.outerHTML=_.outerHTML;else if($==="input"&&(_.type==="checkbox"||_.type==="radio")){if(_.checked)A.defaultChecked=A.checked=_.checked;if(A.value!==_.value)A.value=_.value}else if($==="option")A.selected=_.defaultSelected;else if($==="input"||$==="textarea")A.defaultValue=_.defaultValue;A.removeAttribute(k.expando)}k.buildFragment=function(C,E,A){var B,D,_,$;if(E&&E[0])$=E[0].ownerDocument||E[0];if(!$.createDocumentFragment)$=v0;if(C.length===1&&typeof C[0]==="string"&&C[0].length<512&&$===v0&&C[0].charAt(0)==="<"&&!_0.test(C[0])&&(k.support.checkClone||!r.test(C[0]))){D=true;_=k.fragments[C[0]];if(_&&_!==1)B=_}if(!B){B=$.createDocumentFragment();k.clean(C,$,B,A)}if(D)k.fragments[C[0]]=_?B:1;return{fragment:B,cacheable:D}};k.fragments={};k.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(_,$){k.fn[_]=function(B){var E=[],D=k(B),G=this.length===1&&this[0].parentNode;if(G&&G.nodeType===11&&G.childNodes.length===1&&D.length===1){D[$](this[0]);return this}else{for(var A=0,F=D.length;A<F;A++){var C=(A>0?this.clone(true):this).get();k(D[A])[$](C);E=E.concat(C)}return this.pushStack(E,_,D.selector)}}});function H0($){if("getElementsByTagName"in $)return $.getElementsByTagName("*");else if("querySelectorAll"in $)return $.querySelectorAll("*");else return[]}function c0($){if($.type==="checkbox"||$.type==="radio")$.defaultChecked=$.checked}function m($){if(k.nodeName($,"input"))c0($);else if("getElementsByTagName"in $)k.grep($.getElementsByTagName("input"),c0)}k.extend({clone:function(A,F,E){var D=A.cloneNode(true),C,B,_;if((!k.support.noCloneEvent||!k.support.noCloneChecked)&&(A.nodeType===1||A.nodeType===11)&&!k.isXMLDoc(A)){$(A,D);C=H0(A);B=H0(D);for(_=0;C[_];++_)$(C[_],B[_])}if(F){L1(A,D);if(E){C=H0(A);B=H0(D);for(_=0;C[_];++_)L1(C[_],B[_])}}C=B=null;return D},clean:function(L,M,K,B){var C;M=M||v0;if(typeof M.createElement==="undefined")M=M.ownerDocument||M[0]&&M[0].ownerDocument||v0;var P=[],O;for(var I=0,A;(A=L[I])!=null;I++){if(typeof A==="number")A+="";if(!A)continue;if(typeof A==="string")if(!x0.test(A))A=M.createTextNode(A);else{A=A.replace(q0,"<$1></$2>");var N=(H1.exec(A)||["",""])[1].toLowerCase(),G=i[N]||i._default,F=G[0],J=M.createElement("div");J.innerHTML=G[1]+A+G[2];while(F--)J=J.lastChild;if(!k.support.tbody){var H=b.test(A),Q=N==="table"&&!H?J.firstChild&&J.firstChild.childNodes:G[1]==="<table>"&&!H?J.childNodes:[];for(O=Q.length-1;O>=0;--O)if(k.nodeName(Q[O],"tbody")&&!Q[O].childNodes.length)Q[O].parentNode.removeChild(Q[O])}if(!k.support.leadingWhitespace&&E.test(A))J.insertBefore(M.createTextNode(E.exec(A)[0]),J.firstChild);A=J.childNodes}var $;if(!k.support.appendChecked)if(A[0]&&typeof($=A.length)==="number"){for(O=0;O<$;O++)m(A[O])}else m(A);if(A.nodeType)P.push(A);else P=k.merge(P,A)}if(K){C=function($){return!$.type||D.test($.type)};for(I=0;P[I];I++)if(B&&k.nodeName(P[I],"script")&&(!P[I].type||P[I].type.toLowerCase()==="text/javascript"))B.push(P[I].parentNode?P[I].parentNode.removeChild(P[I]):P[I]);else{if(P[I].nodeType===1){var _=k.grep(P[I].getElementsByTagName("script"),C);P.splice.apply(P,[I+1,0].concat(_))}K.appendChild(P[I])}}return P},cleanData:function(F){var E,H,$=k.cache,B=k.expando,D=k.event.special,G=k.support.deleteExpando;for(var _=0,A;(A=F[_])!=null;_++){if(A.nodeName&&k.noData[A.nodeName.toLowerCase()])continue;H=A[k.expando];if(H){E=$[H]&&$[H][B];if(E&&E.events){for(var C in E.events)if(D[C])k.event.remove(A,C);else k.removeEvent(A,C,E.handle);if(E.handle)E.handle.elem=null}if(G)delete A[k.expando];else if(A.removeAttribute)A.removeAttribute(k.expando);delete $[H]}}}});function p($,_){if(_.src)k.ajax({url:_.src,async:false,dataType:"script"});else k.globalEval((_.text||_.textContent||_.innerHTML||"").replace(_1,"/*$0*/"));if(_.parentNode)_.parentNode.removeChild(_)}var g=/alpha\([^)]*\)/i,O1=/opacity=([^)]*)/,l=/([A-Z]|^ms)/g,N0=/^-?\d+(?:px)?$/i,v=/^-?\d/,B0=/^[+\-]=/,P=/[^+\-\.\de]+/g,N={position:"absolute",visibility:"hidden",display:"block"},s=["Left","Right"],A0=["Top","Bottom"],G0,M0,A;k.fn.css=function(_,$){if(arguments.length===2&&$===$1)return this;return k.access(this,_,$,true,function(_,A,$){return $!==$1?k.style(_,A,$):k.css(_,A)})};k.extend({cssHooks:{opacity:{get:function($,A){if(A){var _=G0($,"opacity","opacity");return _===""?"1":_}else return $.style.opacity}}},cssNumber:{"fillOpacity":true,"fontWeight":true,"lineHeight":true,"opacity":true,"orphans":true,"widows":true,"zIndex":true,"zoom":true},cssProps:{"float":k.support.cssFloat?"cssFloat":"styleFloat"},style:function(_,G,$,C){if(!_||_.nodeType===3||_.nodeType===8||!_.style)return;var F,A,E=k.camelCase(G),D=_.style,B=k.cssHooks[E];G=k.cssProps[E]||E;if($!==$1){A=typeof $;if(A==="number"&&isNaN($)||$==null)return;if(A==="string"&&B0.test($)){$=+$.replace(P,"")+parseFloat(k.css(_,G));A="number"}if(A==="number"&&!k.cssNumber[E])$+="px";if(!B||!("set"in B)||($=B.set(_,$))!==$1){try{D[G]=$}catch(H){}}}else{if(B&&"get"in B&&(F=B.get(_,false,C))!==$1)return F;return D[G]}},css:function($,C,A){var B,_;C=k.camelCase(C);_=k.cssHooks[C];C=k.cssProps[C]||C;if(C==="cssFloat")C="float";if(_&&"get"in _&&(B=_.get($,true,A))!==$1)return B;else if(G0)return G0($,C)},swap:function(_,C,$){var A={};for(var B in C){A[B]=_.style[B];_.style[B]=C[B]}$.call(_);for(B in C)_.style[B]=A[B]}});k.curCSS=k.css;k.each(["height","width"],function($,_){k.cssHooks[_]={get:function(A,C,B){var $;if(C){if(A.offsetWidth!==0)return d(A,_,B);else k.swap(A,N,function(){$=d(A,_,B)});return $}},set:function(_,$){if(N0.test($)){$=parseFloat($);if($>=0)return $+"px"}else return $}}});if(!k.support.opacity)k.cssHooks.opacity={get:function($,_){return O1.test((_&&$.currentStyle?$.currentStyle.filter:$.style.filter)||"")?(parseFloat(RegExp.$1)/100)+"":_?"1":""},set:function(A,_){var D=A.style,B=A.currentStyle;D.zoom=1;var C=k.isNaN(_)?"":"alpha(opacity="+_*100+")",$=B&&B.filter||D.filter||"";D.filter=g.test($)?$.replace(g,C):$+" "+C}};k(function(){if(!k.support.reliableMarginRight)k.cssHooks.marginRight={get:function($,A){var _;k.swap($,{"display":"inline-block"},function(){if(A)_=G0($,"margin-right","marginRight");else _=$.style.marginRight});return _}}});if(v0.defaultView&&v0.defaultView.getComputedStyle)M0=function($,B){var A,C,_;B=B.replace(l,"-$1").toLowerCase();if(!(C=$.ownerDocument.defaultView))return $1;if((_=C.getComputedStyle($,null))){A=_.getPropertyValue(B);if(A===""&&!k.contains($.ownerDocument.documentElement,$))A=k.style($,B)}return A};if(v0.documentElement.currentStyle)A=function(_,D){var A,C=_.currentStyle&&_.currentStyle[D],$=_.runtimeStyle&&_.runtimeStyle[D],B=_.style;if(!N0.test(C)&&v.test(C)){A=B.left;if($)_.runtimeStyle.left=_.currentStyle.left;B.left=D==="fontSize"?"1em":(C||0);C=B.pixelLeft+"px";B.left=A;if($)_.runtimeStyle.left=$}return C===""?"auto":C};G0=M0||A;function d(_,C,B){var $=C==="width"?_.offsetWidth:_.offsetHeight,A=C==="width"?s:A0;if($>0){if(B!=="border")k.each(A,function(){if(!B)$-=parseFloat(k.css(_,"padding"+this))||0;if(B==="margin")$+=parseFloat(k.css(_,B+this))||0;else $-=parseFloat(k.css(_,"border"+this+"Width"))||0});return $+"px"}$=G0(_,C,C);if($<0||$==null)$=_.style[C]||0;$=parseFloat($)||0;if(B)k.each(A,function(){$+=parseFloat(k.css(_,"padding"+this))||0;if(B!=="padding")$+=parseFloat(k.css(_,"border"+this+"Width"))||0;if(B==="margin")$+=parseFloat(k.css(_,B+this))||0});return $+"px"}if(k.expr&&k.expr.filters){k.expr.filters.hidden=function($){var _=$.offsetWidth,A=$.offsetHeight;return(_===0&&A===0)||(!k.support.reliableHiddenOffsets&&($.style.display||k.css($,"display"))==="none")};k.expr.filters.visible=function($){return!k.expr.filters.hidden($)}}var Z=/%20/g,D0=/\[\]$/,E1=/\r?\n/g,z0=/#.*$/,S1=/^(.*?):[ \t]*([^\r\n]*)\r?$/mg,P1=/^(?:color|date|datetime|email|hidden|month|number|password|range|search|tel|text|time|url|week)$/i,X=/^(?:about|app|app\-storage|.+\-extension|file|widget):$/,p0=/^(?:GET|HEAD)$/,P0=/^\/\//,O=/\?/,B1=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,j0=/^(?:select|textarea)/i,m0=/\s+/,T1=/([?&])_=[^&]*/,T=/^([\w\+\.\-]+:)(?:\/\/([^\/?#:]*)(?::(\d+))?)?/,y=k.fn.load,Q={},B={},X0,k0;try{X0=M.href}catch(d0){X0=v0.createElement("a");X0.href="";X0=X0.href}k0=T.exec(X0.toLowerCase())||[];function Y($){return function(E,D){if(typeof E!=="string"){D=E;E="*"}if(k.isFunction(D)){var G=E.toLowerCase().split(m0),A=0,C=G.length,B,_,F;for(;A<C;A++){B=G[A];F=/^\+/.test(B);if(F)B=B.substr(1)||"*";_=$[B]=$[B]||[];_[F?"unshift":"push"](D)}}}}function W(G,I,B,_,D,C){D=D||I.dataTypes[0];C=C||{};C[D]=true;var $=G[D],A=0,E=$?$.length:0,H=(G===Q),F;for(;A<E&&(H||!F);A++){F=$[A](I,B,_);if(typeof F==="string")if(!H||C[F])F=$1;else{I.dataTypes.unshift(F);F=W(G,I,B,_,F,C)}}if((H||!F)&&!C["*"])F=W(G,I,B,_,"*",C);return F}k.fn.extend({load:function(D,C,$){if(typeof D!=="string"&&y)return y.apply(this,arguments);else if(!this.length)return this;var E=D.indexOf(" ");if(E>=0){var _=D.slice(E,D.length);D=D.slice(0,E)}var B="GET";if(C)if(k.isFunction(C)){$=C;C=$1}else if(typeof C==="object"){C=k.param(C,k.ajaxSettings.traditional);B="POST"}var A=this;k.ajax({url:D,type:B,dataType:"html",data:C,complete:function(B,C,D){D=B.responseText;if(B.isResolved()){B.done(function($){D=$});A.html(_?k("<div>").append(D.replace(B1,"")).find(_):D)}if($)A.each($,[D,C,B])}});return this},serialize:function(){return k.param(this.serializeArray())},serializeArray:function(){return this.map(function(){return this.elements?k.makeArray(this.elements):this}).filter(function(){return this.name&&!this.disabled&&(this.checked||j0.test(this.nodeName)||P1.test(this.type))}).map(function(_,A){var $=k(this).val();return $==null?null:k.isArray($)?k.map($,function(_,$){return{name:A.name,value:_.replace(E1,"\r\n")}}):{name:A.name,value:$.replace(E1,"\r\n")}}).get()}});k.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(_,$){k.fn[$]=function(_){return this.bind($,_)}});k.each(["get","post"],function($,_){k[_]=function(C,B,$,A){if(k.isFunction(B)){A=A||$;$=B;B=$1}return k.ajax({type:_,url:C,data:B,success:$,dataType:A})}});k.extend({getScript:function(_,$){return k.get(_,$1,$,"script")},getJSON:function(A,_,$){return k.get(A,_,$,"json")},ajaxSetup:function(_,$){if(!$){$=_;_=k.extend(true,k.ajaxSettings,$)}else k.extend(true,_,k.ajaxSettings,$);for(var A in{context:1,url:1})if(A in $)_[A]=$[A];else if(A in k.ajaxSettings)_[A]=k.ajaxSettings[A];return _},ajaxSettings:{url:X0,isLocal:X.test(k0[1]),global:true,type:"GET",contentType:"application/x-www-form-urlencoded",processData:true,async:true,accepts:{xml:"application/xml, text/xml",html:"text/html",text:"text/plain",json:"application/json, text/javascript","*":"*/*"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText"},converters:{"* text":K0.String,"text html":true,"text json":k.parseJSON,"text xml":k.parseXML}},ajaxPrefilter:Y(Q),ajaxTransport:Y(B),ajax:function(K,Y){if(typeof K==="object"){Y=K;K=$1}Y=Y||{};var L=k.ajaxSetup({},Y),U=L.context||L,_=U!==L&&(U.nodeType||U instanceof k)?k(U):k.event,N=k.Deferred(),C=k._Deferred(),M=L.statusCode||{},J,R={},A={},X,G,F,E,$,Z=0,V,H,P={readyState:0,setRequestHeader:function(B,_){if(!Z){var $=B.toLowerCase();B=A[$]=A[$]||B;R[B]=_}return this},getAllResponseHeaders:function(){return Z===2?X:null},getResponseHeader:function(_){var $;if(Z===2){if(!G){G={};while(($=S1.exec(X)))G[$[1].toLowerCase()]=$[2]}$=G[_.toLowerCase()]}return $===$1?null:$},overrideMimeType:function($){if(!Z)L.mimeType=$;return this},abort:function($){$=$||"abort";if(F)F.abort($);D(0,$);return this}};function D(H,D,$,B){if(Z===2)return;Z=2;if(E)clearTimeout(E);F=$1;X=B||"";P.readyState=H?4:0;var A,K,I,Q=$?O0(L,P,$):$1,G,O;if(H>=200&&H<300||H===304){if(L.ifModified){if((G=P.getResponseHeader("Last-Modified")))k.lastModified[J]=G;if((O=P.getResponseHeader("Etag")))k.etag[J]=O}if(H===304){D="notmodified";A=true}else{try{K=S(L,Q);D="success";A=true}catch(R){D="parsererror";I=R}}}else{I=D;if(!D||H){D="error";if(H<0)H=0}}P.status=H;P.statusText=D;if(A)N.resolveWith(U,[K,D,P]);else N.rejectWith(U,[P,D,I]);P.statusCode(M);M=$1;if(V)_.trigger("ajax"+(A?"Success":"Error"),[P,L,A?K:I]);C.resolveWith(U,[P,D]);if(V){_.trigger("ajaxComplete",[P,L]);if(!(--k.active))k.event.trigger("ajaxStop")}}N.promise(P);P.success=P.done;P.error=P.fail;P.complete=C.done;P.statusCode=function(_){if(_){var $;if(Z<2){for($ in _)M[$]=[M[$],_[$]]}else{$=_[P.status];P.then($,$)}}return this};L.url=((K||L.url)+"").replace(z0,"").replace(P0,k0[1]+"//");L.dataTypes=k.trim(L.dataType||"*").toLowerCase().split(m0);if(L.crossDomain==null){$=T.exec(L.url.toLowerCase());L.crossDomain=!!($&&($[1]!=k0[1]||$[2]!=k0[2]||($[3]||($[1]==="http:"?80:443))!=(k0[3]||(k0[1]==="http:"?80:443))))}if(L.data&&L.processData&&typeof L.data!=="string")L.data=k.param(L.data,L.traditional);W(Q,L,Y,P);if(Z===2)return false;V=L.global;L.type=L.type.toUpperCase();L.hasContent=!p0.test(L.type);if(V&&k.active++===0)k.event.trigger("ajaxStart");if(!L.hasContent){if(L.data)L.url+=(O.test(L.url)?"&":"?")+L.data;J=L.url;if(L.cache===false){var I=k.now(),a=L.url.replace(T1,"$1_="+I);L.url=a+((a===L.url)?(O.test(L.url)?"&":"?")+"_="+I:"")}}if(L.data&&L.hasContent&&L.contentType!==false||Y.contentType)P.setRequestHeader("Content-Type",L.contentType);if(L.ifModified){J=J||L.url;if(k.lastModified[J])P.setRequestHeader("If-Modified-Since",k.lastModified[J]);if(k.etag[J])P.setRequestHeader("If-None-Match",k.etag[J])}P.setRequestHeader("Accept",L.dataTypes[0]&&L.accepts[L.dataTypes[0]]?L.accepts[L.dataTypes[0]]+(L.dataTypes[0]!=="*"?", */*; q=0.01":""):L.accepts["*"]);for(H in L.headers)P.setRequestHeader(H,L.headers[H]);if(L.beforeSend&&(L.beforeSend.call(U,P,L)===false||Z===2)){P.abort();return false}for(H in{success:1,error:1,complete:1})P[H](L[H]);F=W(B,L,Y,P);if(!F)D(-1,"No Transport");else{P.readyState=1;if(V)_.trigger("ajaxSend",[P,L]);if(L.async&&L.timeout>0)E=setTimeout(function(){P.abort("timeout")},L.timeout);try{Z=1;F.send(R,D)}catch(b){if(status<2)D(-1,b);else k.error(b)}}return P},param:function(A,$){var C=[],_=function($,_){_=k.isFunction(_)?_():_;C[C.length]=encodeURIComponent($)+"="+encodeURIComponent(_)};if($===$1)$=k.ajaxSettings.traditional;if(k.isArray(A)||(A.jquery&&!k.isPlainObject(A)))k.each(A,function(){_(this.name,this.value)});else for(var B in A)q(B,A[B],$,_);return C.join("&").replace(Z,"+")}});function q(B,A,$,_){if(k.isArray(A))k.each(A,function(A,C){if($||D0.test(B))_(B,C);else q(B+"["+(typeof C==="object"||k.isArray(C)?A:"")+"]",C,$,_)});else if(!$&&A!=null&&typeof A==="object"){for(var C in A)q(B+"["+C+"]",A[C],$,_)}else _(B,A)}k.extend({active:0,lastModified:{},etag:{}});function O0(G,A,_){var $=G.contents,E=G.dataTypes,D=G.responseFields,F,B,C,H;for(B in D)if(B in _)A[D[B]]=_[B];while(E[0]==="*"){E.shift();if(F===$1)F=G.mimeType||A.getResponseHeader("content-type")}if(F)for(B in $)if($[B]&&$[B].test(F)){E.unshift(B);break}if(E[0]in _)C=E[0];else{for(B in _){if(!E[0]||G.converters[B+" "+E[0]]){C=B;break}if(!H)H=B}C=C||H}if(C){if(C!==E[0])E.unshift(C);return _[C]}}function S(K,E){if(K.dataFilter)E=K.dataFilter(E,K.dataType);var D=K.dataTypes,_={},G,F,A=D.length,H,$=D[0],I,L,B,J,C;for(G=1;G<A;G++){if(G===1)for(F in K.converters)if(typeof F==="string")_[F.toLowerCase()]=K.converters[F];I=$;$=D[G];if($==="*")$=I;else if(I!=="*"&&I!==$){L=I+" "+$;B=_[L]||_["* "+$];if(!B){C=$1;for(J in _){H=J.split(" ");if(H[0]===I||H[0]==="*"){C=_[H[1]+" "+$];if(C){J=_[J];if(J===true)B=C;else if(C===true)B=J;break}}}}if(!(B||C))k.error("No conversion from "+L.replace(" "," to "));if(B!==true)E=B?B(E):C(J(E))}}return E}var c=k.now(),t=/(\=)\?(&|$)|\?\?/i;k.ajaxSetup({jsonp:"callback",jsonpCallback:function(){return k.expando+"_"+(c++)}});k.ajaxPrefilter("json jsonp",function(H,B,$){var _=H.contentType==="application/x-www-form-urlencoded"&&(typeof H.data==="string");if(H.dataTypes[0]==="jsonp"||H.jsonp!==false&&(t.test(H.url)||_&&t.test(H.data))){var A,G=H.jsonpCallback=k.isFunction(H.jsonpCallback)?H.jsonpCallback():H.jsonpCallback,D=K0[G],E=H.url,C=H.data,F="$1"+G+"$2";if(H.jsonp!==false){E=E.replace(t,F);if(H.url===E){if(_)C=C.replace(t,F);if(H.data===C)E+=(/\?/.test(E)?"&":"?")+H.jsonp+"="+G}}H.url=E;H.data=C;K0[G]=function($){A=[$]};$.always(function(){K0[G]=D;if(A&&k.isFunction(D))K0[G](A[0])});H.converters["script json"]=function(){if(!A)k.error(G+" was not called");return A[0]};H.dataTypes[0]="json";return"script"}});k.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/javascript|ecmascript/},converters:{"text script":function($){k.globalEval($);return $}}});k.ajaxPrefilter("script",function($){if($.cache===$1)$.cache=false;if($.crossDomain){$.type="GET";$.global=false}});k.ajaxTransport("script",function(A){if(A.crossDomain){var $,_=v0.head||v0.getElementsByTagName("head")[0]||v0.documentElement;return{send:function(C,B){$=v0.createElement("script");$.async="async";if(A.scriptCharset)$.charset=A.scriptCharset;$.src=A.url;$.onload=$.onreadystatechange=function(C,A){if(A||!$.readyState||/loaded|complete/.test($.readyState)){$.onload=$.onreadystatechange=null;if(_&&$.parentNode)_.removeChild($);$=$1;if(!A)B(200,"success")}};_.insertBefore($,_.firstChild)},abort:function(){if($)$.onload(0,1)}}}});var J=K0.ActiveXObject?function(){for(var $ in w0)w0[$](0,1)}:false,j=0,w0;function C1(){try{return new K0.XMLHttpRequest()}catch($){}}function r0(){try{return new K0.ActiveXObject("Microsoft.XMLHTTP")}catch($){}}k.ajaxSettings.xhr=K0.ActiveXObject?function(){return!this.isLocal&&C1()||r0()}:C1;(function($){k.extend(k.support,{ajax:!!$,cors:!!$&&("withCredentials"in $)})})(k.ajaxSettings.xhr());if(k.support.ajax)k.ajaxTransport(function(_){if(!_.crossDomain||k.support.cors){var $;return{send:function(D,F){var C=_.xhr(),A,B;if(_.username)C.open(_.type,_.url,_.async,_.username,_.password);else C.open(_.type,_.url,_.async);if(_.xhrFields)for(B in _.xhrFields)C[B]=_.xhrFields[B];if(_.mimeType&&C.overrideMimeType)C.overrideMimeType(_.mimeType);if(!_.crossDomain&&!D["X-Requested-With"])D["X-Requested-With"]="XMLHttpRequest";try{for(B in D)C.setRequestHeader(B,D[B])}catch(E){}C.send((_.hasContent&&_.data)||null);$=function(H,D){var I,E,M,B,K;try{if($&&(D||C.readyState===4)){$=$1;if(A){C.onreadystatechange=k.noop;if(J)delete w0[A]}if(D){if(C.readyState!==4)C.abort()}else{I=C.status;M=C.getAllResponseHeaders();B={};K=C.responseXML;if(K&&K.documentElement)B.xml=K;B.text=C.responseText;try{E=C.statusText}catch(L){E=""}if(!I&&_.isLocal&&!_.crossDomain)I=B.text?200:404;else if(I===1223)I=204}}}catch(G){if(!D)F(-1,G)}if(B)F(I,E,B,M)};if(!_.async||C.readyState===4)$();else{A=++j;if(J){if(!w0){w0={};k(K0).unload(J)}w0[A]=$}C.onreadystatechange=$}},abort:function(){if($)$(0,1)}}}});var U={},R0,C,n=/^(?:toggle|show|hide)$/,u=/^([+\-]=)?([\d+.\-]+)([a-z%]*)$/i,l0,F1=[["height","marginTop","marginBottom","paddingTop","paddingBottom"],["width","marginLeft","marginRight","paddingLeft","paddingRight"],["opacity"]],G1,M1=K0.webkitRequestAnimationFrame||K0.mozRequestAnimationFrame||K0.oRequestAnimationFrame;k.fn.extend({show:function(A,E,C){var B,$;if(A||A===0)return this.animate(W0("show",3),A,E,C);else{for(var _=0,D=this.length;_<D;_++){B=this[_];if(B.style){$=B.style.display;if(!k._data(B,"olddisplay")&&$==="none")$=B.style.display="";if($===""&&k.css(B,"display")==="none")k._data(B,"olddisplay",V0(B.nodeName))}}for(_=0;_<D;_++){B=this[_];if(B.style){$=B.style.display;if($===""||$==="none")B.style.display=k._data(B,"olddisplay")||""}}return this}},hide:function(A,D,B){if(A||A===0)return this.animate(W0("hide",3),A,D,B);else{for(var _=0,C=this.length;_<C;_++)if(this[_].style){var $=k.css(this[_],"display");if($!=="none"&&!k._data(this[_],"olddisplay"))k._data(this[_],"olddisplay",$)}for(_=0;_<C;_++)if(this[_].style)this[_].style.display="none";return this}},_toggle:k.fn.toggle,toggle:function(B,_,$){var A=typeof B==="boolean";if(k.isFunction(B)&&k.isFunction(_))this._toggle.apply(this,arguments);else if(B==null||A)this.each(function(){var $=A?B:k(this).is(":hidden");k(this)[$?"show":"hide"]()});else this.animate(W0("toggle",3),B,_,$);return this},fadeTo:function(_,$,B,A){return this.filter(":hidden").css("opacity",0).show().end().animate({opacity:$},_,B,A)},animate:function(A,$,B,_){var C=k.speed($,B,_);if(k.isEmptyObject(A))return this.each(C.complete,[false]);A=k.extend({},A);return this[C.queue===false?"each":"queue"](function(){if(C.queue===false)k._mark(this);var J=k.extend({},C),D=this.nodeType===1,I=D&&k(this).is(":hidden"),K,B,H,$,L,_,E,G,F;J.animatedProperties={};for(H in A){K=k.camelCase(H);if(H!==K){A[K]=A[H];delete A[H]}B=A[K];if(k.isArray(B)){J.animatedProperties[K]=B[1];B=A[K]=B[0]}else J.animatedProperties[K]=J.specialEasing&&J.specialEasing[K]||J.easing||"swing";if(B==="hide"&&I||B==="show"&&!I)return J.complete.call(this);if(D&&(K==="height"||K==="width")){J.overflow=[this.style.overflow,this.style.overflowX,this.style.overflowY];if(k.css(this,"display")==="inline"&&k.css(this,"float")==="none")if(!k.support.inlineBlockNeedsLayout)this.style.display="inline-block";else{$=V0(this.nodeName);if($==="inline")this.style.display="inline-block";else{this.style.display="inline";this.style.zoom=1}}}}if(J.overflow!=null)this.style.overflow="hidden";for(H in A){L=new k.fx(this,J,H);B=A[H];if(n.test(B))L[B==="toggle"?I?"show":"hide":B]();else{_=u.exec(B);E=L.cur();if(_){G=parseFloat(_[2]);F=_[3]||(k.cssNumber[H]?"":"px");if(F!=="px"){k.style(this,H,(G||1)+F);E=((G||1)/L.cur())*E;k.style(this,H,E+F)}if(_[1])G=((_[1]==="-="?-1:1)*G)+E;L.custom(E,G,F)}else L.custom(E,B,"")}}return true})},stop:function($,_){if($)this.queue([]);this.each(function(){var A=k.timers,$=A.length;if(!_)k._unmark(true,this);while($--)if(A[$].elem===this){if(_)A[$](true);A.splice($,1)}});if(!_)this.dequeue();return this}});function b0(){setTimeout(J1,0);return(G1=k.now())}function J1(){G1=$1}function W0(A,$){var _={};k.each(F1.concat.apply([],F1.slice(0,$)),function(){_[this]=A});return _}k.each({slideDown:W0("show",1),slideUp:W0("hide",1),slideToggle:W0("toggle",1),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(_,$){k.fn[_]=function(_,B,A){return this.animate($,_,B,A)}});k.extend({speed:function($,A,B){var _=$&&typeof $==="object"?k.extend({},$):{complete:B||!B&&A||k.isFunction($)&&$,duration:$,easing:B&&A||A&&!k.isFunction(A)&&A};_.duration=k.fx.off?0:typeof _.duration==="number"?_.duration:_.duration in k.fx.speeds?k.fx.speeds[_.duration]:k.fx.speeds._default;_.old=_.complete;_.complete=function($){if(k.isFunction(_.old))_.old.call(this);if(_.queue!==false)k.dequeue(this);else if($!==false)k._unmark(this)};return _},easing:{linear:function(_,A,$,B){return $+B*_},swing:function(_,A,$,B){return((-Math.cos(_*Math.PI)/2)+0.5)*B+$}},timers:[],fx:function($,A,_){this.options=A;this.elem=$;this.prop=_;A.orig=A.orig||{}}});k.fx.prototype={update:function(){if(this.options.step)this.options.step.call(this.elem,this.now,this);(k.fx.step[this.prop]||k.fx.step._default)(this)},cur:function(){if(this.elem[this.prop]!=null&&(!this.elem.style||this.elem.style[this.prop]==null))return this.elem[this.prop];var _,$=k.css(this.elem,this.prop);return isNaN(_=parseFloat($))?!$||$==="auto"?0:$:_},custom:function(E,_,C){var B=this,A=k.fx,$;this.startTime=G1||b0();this.start=E;this.end=_;this.unit=C||this.unit||(k.cssNumber[this.prop]?"":"px");this.now=this.start;this.pos=this.state=0;function D($){return B.step($)}D.elem=this.elem;if(D()&&k.timers.push(D)&&!l0)if(M1){l0=true;$=function(){if(l0){M1($);A.tick()}};M1($)}else l0=setInterval(A.tick,A.interval)},show:function(){this.options.orig[this.prop]=k.style(this.elem,this.prop);this.options.show=true;this.custom(this.prop==="width"||this.prop==="height"?1:0,this.cur());k(this.elem).show()},hide:function(){this.options.orig[this.prop]=k.style(this.elem,this.prop);this.options.hide=true;this.custom(this.cur(),0)},step:function(D){var B=G1||b0(),A=true,_=this.elem,F=this.options,$,E;if(D||B>=F.duration+this.startTime){this.now=this.end;this.pos=this.state=1;this.update();F.animatedProperties[this.prop]=true;for($ in F.animatedProperties)if(F.animatedProperties[$]!==true)A=false;if(A){if(F.overflow!=null&&!k.support.shrinkWrapBlocks)k.each(["","X","Y"],function($,A){_.style["overflow"+A]=F.overflow[$]});if(F.hide)k(_).hide();if(F.hide||F.show)for(var C in F.animatedProperties)k.style(_,C,F.orig[C]);F.complete.call(_)}return false}else{if(F.duration==Infinity)this.now=B;else{E=B-this.startTime;this.state=E/F.duration;this.pos=k.easing[F.animatedProperties[this.prop]](this.state,E,0,1,F.duration);this.now=this.start+((this.end-this.start)*this.pos)}this.update()}return true}};k.extend(k.fx,{tick:function(){for(var _=k.timers,$=0;$<_.length;++$)if(!_[$]())_.splice($--,1);if(!_.length)k.fx.stop()},interval:13,stop:function(){clearInterval(l0);l0=null},speeds:{slow:600,fast:200,_default:400},step:{opacity:function($){k.style($.elem,"opacity",$.now)},_default:function($){if($.elem.style&&$.elem.style[$.prop]!=null)$.elem.style[$.prop]=($.prop==="width"||$.prop==="height"?Math.max(0,$.now):$.now)+$.unit;else $.elem[$.prop]=$.now}}});if(k.expr&&k.expr.filters)k.expr.filters.animated=function($){return k.grep(k.timers,function(_){return $===_.elem}).length};function V0(A){if(!U[A]){var B=v0.body,_=k("<"+A+">").appendTo(B),$=_.css("display");_.remove();if($==="none"||$===""){if(!R0){R0=v0.createElement("iframe");R0.frameBorder=R0.width=R0.height=0}B.appendChild(R0);if(!C||!R0.createElement){C=(R0.contentWindow||R0.contentDocument).document;C.write((v0.compatMode==="CSS1Compat"?"<!doctype html>":"")+"<html><body>");C.close()}_=C.createElement(A);C.body.appendChild(_);$=k.css(_,"display");B.removeChild(R0)}U[A]=$}return U[A]}var E0=/^t(?:able|d|h)$/i,V=/^(?:body|html)$/i;if("getBoundingClientRect"in v0.documentElement)k.fn.offset=function(G){var $=this[0],C;if(G)return this.each(function($){k.offset.setOffset(this,G,$)});if(!$||!$.ownerDocument)return null;if($===$.ownerDocument.body)return k.offset.bodyOffset($);try{C=$.getBoundingClientRect()}catch(L){}var H=$.ownerDocument,_=H.documentElement;if(!C||!k.contains(_,$))return C?{top:C.top,left:C.left}:{top:0,left:0};var J=H.body,K=N1(H),E=_.clientTop||J.clientTop||0,A=_.clientLeft||J.clientLeft||0,F=K.pageYOffset||k.support.boxModel&&_.scrollTop||J.scrollTop,D=K.pageXOffset||k.support.boxModel&&_.scrollLeft||J.scrollLeft,B=C.top+F-E,I=C.left+D-A;return{top:B,left:I}};else k.fn.offset=function(E){var $=this[0];if(E)return this.each(function($){k.offset.setOffset(this,E,$)});if(!$||!$.ownerDocument)return null;if($===$.ownerDocument.body)return k.offset.bodyOffset($);k.offset.initialize();var A,H=$.offsetParent,D=$,F=$.ownerDocument,_=F.documentElement,I=F.body,J=F.defaultView,C=J?J.getComputedStyle($,null):$.currentStyle,B=$.offsetTop,G=$.offsetLeft;while(($=$.parentNode)&&$!==I&&$!==_){if(k.offset.supportsFixedPosition&&C.position==="fixed")break;A=J?J.getComputedStyle($,null):$.currentStyle;B-=$.scrollTop;G-=$.scrollLeft;if($===H){B+=$.offsetTop;G+=$.offsetLeft;if(k.offset.doesNotAddBorder&&!(k.offset.doesAddBorderForTableAndCells&&E0.test($.nodeName))){B+=parseFloat(A.borderTopWidth)||0;G+=parseFloat(A.borderLeftWidth)||0}D=H;H=$.offsetParent}if(k.offset.subtractsBorderForOverflowNotVisible&&A.overflow!=="visible"){B+=parseFloat(A.borderTopWidth)||0;G+=parseFloat(A.borderLeftWidth)||0}C=A}if(C.position==="relative"||C.position==="static"){B+=I.offsetTop;G+=I.offsetLeft}if(k.offset.supportsFixedPosition&&C.position==="fixed"){B+=Math.max(_.scrollTop,I.scrollTop);G+=Math.max(_.scrollLeft,I.scrollLeft)}return{top:B,left:G}};k.offset={initialize:function(){var F=v0.body,B=v0.createElement("div"),A,D,_,$,E=parseFloat(k.css(F,"marginTop"))||0,C="<div style='position:absolute;top:0;left:0;margin:0;border:5px solid #000;padding:0;width:1px;height:1px;'><div></div></div><table style='position:absolute;top:0;left:0;margin:0;border:5px solid #000;padding:0;width:1px;height:1px;' cellpadding='0' cellspacing='0'><tr><td></td></tr></table>";k.extend(B.style,{position:"absolute",top:0,left:0,margin:0,border:0,width:"1px",height:"1px",visibility:"hidden"});B.innerHTML=C;F.insertBefore(B,F.firstChild);A=B.firstChild;D=A.firstChild;$=A.nextSibling.firstChild.firstChild;this.doesNotAddBorder=(D.offsetTop!==5);this.doesAddBorderForTableAndCells=($.offsetTop===5);D.style.position="fixed";D.style.top="20px";this.supportsFixedPosition=(D.offsetTop===20||D.offsetTop===15);D.style.position=D.style.top="";A.style.overflow="hidden";A.style.position="relative";this.subtractsBorderForOverflowNotVisible=(D.offsetTop===-5);this.doesNotIncludeMarginInBodyOffset=(F.offsetTop!==E);F.removeChild(B);k.offset.initialize=k.noop},bodyOffset:function(A){var $=A.offsetTop,_=A.offsetLeft;k.offset.initialize();if(k.offset.doesNotIncludeMarginInBodyOffset){$+=parseFloat(k.css(A,"marginTop"))||0;_+=parseFloat(k.css(A,"marginLeft"))||0}return{top:$,left:_}},setOffset:function(_,F,H){var B=k.css(_,"position");if(B==="static")_.style.position="relative";var $=k(_),I=$.offset(),K=k.css(_,"top"),D=k.css(_,"left"),G=(B==="absolute"||B==="fixed")&&k.inArray("auto",[K,D])>-1,A={},E={},J,C;if(G){E=$.position();J=E.top;C=E.left}else{J=parseFloat(K)||0;C=parseFloat(D)||0}if(k.isFunction(F))F=F.call(_,H,I);if(F.top!=null)A.top=(F.top-I.top)+J;if(F.left!=null)A.left=(F.left-I.left)+C;if("using"in F)F.using.call(_,A);else $.css(A)}};k.fn.extend({position:function(){if(!this[0])return null;var $=this[0],_=this.offsetParent(),B=this.offset(),A=V.test(_[0].nodeName)?{top:0,left:0}:_.offset();B.top-=parseFloat(k.css($,"marginTop"))||0;B.left-=parseFloat(k.css($,"marginLeft"))||0;A.top+=parseFloat(k.css(_[0],"borderTopWidth"))||0;A.left+=parseFloat(k.css(_[0],"borderLeftWidth"))||0;return{top:B.top-A.top,left:B.left-A.left}},offsetParent:function(){return this.map(function(){var $=this.offsetParent||v0.body;while($&&(!V.test($.nodeName)&&k.css($,"position")==="static"))$=$.offsetParent;return $})}});k.each(["Left","Top"],function($,_){var A="scroll"+_;k.fn[A]=function(_){var B,C;if(_===$1){B=this[0];if(!B)return null;C=N1(B);return C?("pageXOffset"in C)?C[$?"pageYOffset":"pageXOffset"]:k.support.boxModel&&C.document.documentElement[A]||C.document.body[A]:B[A]}return this.each(function(){C=N1(this);if(C)C.scrollTo(!$?_:k(C).scrollLeft(),$?_:k(C).scrollTop());else this[A]=_})}});function N1($){return k.isWindow($)?$:$.nodeType===9?$.defaultView||$.parentWindow:false}k.each(["Height","Width"],function($,A){var _=A.toLowerCase();k.fn["inner"+A]=function(){var $=this[0];return $&&$.style?parseFloat(k.css($,_,"padding")):null};k.fn["outer"+A]=function(A){var $=this[0];return $&&$.style?parseFloat(k.css($,_,A?"margin":"border")):null};k.fn[_]=function(E){var $=this[0];if(!$)return E==null?null:this;if(k.isFunction(E))return this.each(function($){var A=k(this);A[_](E.call(this,$,A[_]()))});if(k.isWindow($)){var C=$.document.documentElement["client"+A];return $.document.compatMode==="CSS1Compat"&&C||$.document.body["client"+A]||C}else if($.nodeType===9)return Math.max($.documentElement["client"+A],$.body["scroll"+A],$.documentElement["scroll"+A],$.body["offset"+A],$.documentElement["offset"+A]);else if(E===$1){var B=k.css($,_),D=parseFloat(B);return k.isNaN(D)?B:D}else return this.css(_,typeof E==="string"?E:E+"px")}});K0.jQuery=K0.$=k})(window)
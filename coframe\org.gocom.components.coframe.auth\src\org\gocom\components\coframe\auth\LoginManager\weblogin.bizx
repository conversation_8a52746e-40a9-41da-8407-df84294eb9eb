<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="weblogin" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="用户登录" title="shitf&#x9;13-3-12 下午4:05">
    <location x="8" y="420"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link23" name="link23" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo3</targetNode>
    </sourceConnections>
    <location x="30" y="30"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link22</targetConnections>
    <location x="976" y="61"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Int" name="retCode" type="query" valueType="Primitive">retCode</process:return>
      <process:return id="1" language="String" name="msg" type="query" valueType="Primitive">msg</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="32" y="66"/>
    <figSize height="16" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="978" y="97"/>
    <figSize height="16" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="验证用户" displayName="authentication" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="link3" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">retCode</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>assign4</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link27" name="link27" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>assign6</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">retCode</process:leftOperand>
          <process:rightOperand type="literal">-7</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link29" name="link29" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>assign7</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">retCode</process:leftOperand>
          <process:rightOperand type="literal">-2</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link31" name="link31" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>assign8</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">retCode</process:leftOperand>
          <process:rightOperand type="literal">-6</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link21</targetConnections>
    <location x="30" y="307"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" transactionType="join" varArgs="false">
      <process:partner type="literal">this.authentication</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
        <process:inputVariable id="1" name="password" type="query" value="String" valueType="Primitive" pattern="reference">password</process:inputVariable>
        <process:inputVariable id="2" name="verifyimgflag" type="literal" value="String" valueType="Primitive" pattern="reference">true</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="returnValue" type="query" value="Int" valueType="Primitive">retCode</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="19" y="342"/>
    <figSize height="16" width="49"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="构造登录用户实例" displayName="login" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="225" y="307"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.login</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userObject" type="query" value="com.eos.data.datacontext.UserObject" valueType="Java" pattern="reference">userObject</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="191" y="343"/>
    <figSize height="16" width="97"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="初始化用户" displayName="initUserObject" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="135" y="307"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.initUserObject</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userId" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="com.eos.data.datacontext.UserObject" valueType="Java">userObject</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="119" y="343"/>
    <figSize height="16" width="61"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="302" y="307"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">userId</process:from>
      <process:to type="query">empObject/userId</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="304" y="343"/>
    <figSize height="16" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="608" y="464"/>
    <figSize height="16" width="185"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="初始化用户上下文数据empObject" displayName="initEmpObject" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>invokeSpring3</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link11</targetConnections>
    <location x="686" y="428"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.initEmpObject</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userObject" type="query" value="com.eos.data.datacontext.UserObject" valueType="Java" pattern="reference">userObject</process:inputVariable>
      <process:inputVariable id="1" name="obj" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">empObject</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="expandEntityByTemplate" displayName="expandEntityByTemplate" type="invoke" index="3" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="394" y="307"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.expandEntityByTemplate</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="template" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">empObject</process:inputVariable>
      <process:inputVariable id="2" name="dataObject" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">empObject</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">nn</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="334" y="343"/>
    <figSize height="16" width="143"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="callSysRoute" displayName="callSysRoute" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link10" name="link10" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">LoginUserInfo/RETN_CODE</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">LoginUserInfo/RETN_CODE</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="510" y="428"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="6" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">com.wx.utils.publicMemer.SysRoute.callSysRoute</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="dbName" type="literal" value="String" valueType="Primitive" pattern="reference">default</process:inputVariable>
        <process:inputVariable id="1" name="datas" type="query" value="java.util.HashMap[]" valueType="Java" pattern="reference">json_argsdetail</process:inputVariable>
        <process:inputVariable id="2" name="json_args" type="query" value="java.util.HashMap" valueType="Java" pattern="reference">json_args</process:inputVariable>
        <process:inputVariable id="3" name="json_argsdetail" type="expression" value="java.util.HashMap" valueType="Java" pattern="reference">null</process:inputVariable>
        <process:inputVariable id="4" name="json_columns" type="expression" value="String[]" valueType="Primitive" pattern="reference">null</process:inputVariable>
        <process:inputVariable id="5" name="tagkey" type="literal" value="String" valueType="Primitive" pattern="reference">查询登录人员信息</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="result" type="query" value="java.util.HashMap" valueType="Java">LoginUserInfo</process:outputVariable>
        <process:outputVariable id="1" name="message" type="query" value="String" valueType="Primitive"></process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="487" y="463"/>
    <figSize height="16" width="73"/>
    <node>subprocess1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值1" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>subprocess1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="394" y="428"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">PX_LOGIN.P_LOGIN</process:from>
      <process:to type="query">json_args/EXECSQL</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">userId</process:from>
      <process:to type="query">json_argsdetail[1]/LOGINNAME</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">empObject/orgid</process:from>
      <process:to type="query">json_argsdetail[1]/DEPTID</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="392" y="464"/>
    <figSize height="16" width="32"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="613" y="464"/>
    <figSize height="16" width="32"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值2" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokeSpring2</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link10</targetConnections>
    <location x="615" y="428"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">LoginUserInfo</process:from>
      <process:to type="query">empObject/LoginUserInfo</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="赋值3" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link12" name="link12" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="510" y="170"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">LoginUserInfo/RETN_MSG</process:from>
      <process:to type="query">msg</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">-3</process:from>
      <process:to type="query">retCode</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="508" y="206"/>
    <figSize height="16" width="32"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="初始化用户1" displayName="sessionPut" collapsed="false" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link14" name="link14" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand>a</process:leftOperand>
          <process:rightOperand>2</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="778" y="428"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginService.sessionPut</process:partner>
      <process:instance instanceName="LoginServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="keyvalue" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.String>" valueType="Java" pattern="reference">LoginUserInfo</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="758" y="464"/>
    <figSize height="16" width="68"/>
    <node>invokeSpring3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="setSessionId" displayName="setSessionId" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link13" name="link13" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>subprocess2</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>subprocess2</targetNode>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="781" y="296"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.LoginOnlySession.setSessionId</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userObject/userId</process:inputVariable>
      <process:inputVariable id="1" name="sessionId" type="query" value="java.lang.String" valueType="Java" pattern="reference">userObject/uniqueId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="756" y="332"/>
    <figSize height="16" width="72"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess2" name="userbtn" displayName="authentication" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess2</sourceNode>
      <targetNode>assign5</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="连接线" lineType="note" type="exception">
      <sourceNode>subprocess2</sourceNode>
      <targetNode>assign5</targetNode>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <targetConnections>link15</targetConnections>
    <location x="781" y="201"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess2label</nodeLabel>
    <process:flow index="1" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.userbtn</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userid" type="query" value="String" valueType="Primitive" pattern="reference">userObject/userId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess2label" name="label" nodeType="label">
    <location x="772" y="236"/>
    <figSize height="16" width="45"/>
    <node>subprocess2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo3" name="getRSAkey" displayName="getRSAkey" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo3</sourceNode>
      <targetNode>invokePojo4</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link24" name="link24" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo3</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link23</targetConnections>
    <location x="33" y="109"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo3label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.RSAUtil.getRSAkey</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables/>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">privatey</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo3label" name="label" nodeType="label">
    <location x="12" y="145"/>
    <figSize height="16" width="64"/>
    <node>invokePojo3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo4" name="decrypt" displayName="decrypt" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link20" name="link20" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo4</sourceNode>
      <targetNode>invokePojo5</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link25" name="link25" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo4</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="33" y="170"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo4label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.RSAUtil.decrypt</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="str" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
      <process:inputVariable id="1" name="privateKey" type="query" value="java.lang.String" valueType="Java" pattern="reference">privatey</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">userId</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo4label" name="label" nodeType="label">
    <location x="22" y="206"/>
    <figSize height="16" width="45"/>
    <node>invokePojo4</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo5" name="decrypt1" displayName="decrypt" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link21" name="link21" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo5</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link26" name="link26" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo5</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link20</targetConnections>
    <location x="34" y="219"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo5label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.RSAUtil.decrypt</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="str" type="query" value="java.lang.String" valueType="Java" pattern="reference">password</process:inputVariable>
      <process:inputVariable id="1" name="privateKey" type="query" value="java.lang.String" valueType="Java" pattern="reference">privatey</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">password</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo5label" name="label" nodeType="label">
    <location x="19" y="255"/>
    <figSize height="16" width="52"/>
    <node>invokePojo5</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="saveLoginLog" displayName="saveLoginLog" collapsed="false" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link22" name="link22" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link18</targetConnections>
    <targetConnections>link19</targetConnections>
    <location x="784" y="57"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginLogService.saveLoginLog</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="loginflag" type="query" value="java.lang.String" valueType="Java" pattern="reference">loginflag</process:inputVariable>
      <process:inputVariable id="1" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="756" y="93"/>
    <figSize height="16" width="79"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="赋值4" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link19" name="link19" displayName="link18" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <targetConnections>link12</targetConnections>
    <targetConnections>link24</targetConnections>
    <targetConnections>link25</targetConnections>
    <targetConnections>link26</targetConnections>
    <targetConnections>link28</targetConnections>
    <targetConnections>link30</targetConnections>
    <targetConnections>link32</targetConnections>
    <location x="417" y="57"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">1</process:from>
      <process:to type="query">loginflag</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="415" y="93"/>
    <figSize height="16" width="32"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign5" name="赋值5" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link18" name="link18" displayName="link19" isDefault="true" type="transition">
      <sourceNode>assign5</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <targetConnections>link17</targetConnections>
    <targetConnections>link16</targetConnections>
    <location x="781" y="117"/>
    <size height="28" width="28"/>
    <nodeLabel>assign5label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">0</process:from>
      <process:to type="query">loginflag</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign5label" name="label" nodeType="label">
    <location x="779" y="153"/>
    <figSize height="16" width="32"/>
    <node>assign5</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign6" name="赋值6" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link28" name="link28" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign6</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link27</targetConnections>
    <location x="300" y="239"/>
    <size height="28" width="28"/>
    <nodeLabel>assign6label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">用户密码已过期，请联系管理员修改密码！</process:from>
      <process:to type="query">msg</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign6label" name="label" nodeType="label">
    <location x="298" y="275"/>
    <figSize height="16" width="32"/>
    <node>assign6</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign7" name="赋值7" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link30" name="link30" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign7</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link29</targetConnections>
    <location x="345" y="266"/>
    <size height="28" width="28"/>
    <nodeLabel>assign7label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">非法修改授权页面，系统已损坏！</process:from>
      <process:to type="query">msg</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign7label" name="label" nodeType="label">
    <location x="343" y="302"/>
    <figSize height="16" width="32"/>
    <node>assign7</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign8" name="赋值8" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link32" name="link32" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign8</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link31</targetConnections>
    <location x="260" y="201"/>
    <size height="28" width="28"/>
    <nodeLabel>assign8label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">验证码错误，请重试！</process:from>
      <process:to type="query">msg</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign8label" name="label" nodeType="label">
    <location x="258" y="237"/>
    <figSize height="16" width="32"/>
    <node>assign8</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="shitf" createTime="2013-03-07 18:58:23" date="2013-03-07Z" description="" name="login" version="6.3"/>
  <process:variables>
    <process:variable anyType="com.eos.data.datacontext.UserObject" description="" historyStateLocation="client" isArray="false" name="userObject"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.auth.queryentity.QueryEmpObject" name="empObject"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="false" name="json_args"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="true" name="json_argsdetail"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="false" name="LoginUserInfo"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="privatey" primitiveType="String"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="userId" primitiveType="String"/>
    <process:input description="" isArray="false" name="password" primitiveType="String"/>
    <process:input description="" isArray="false" name="url" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="retCode" primitiveType="Int"/>
    <process:output description="" isArray="false" name="msg" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

package org.gocom.components.coframe.auth;

import org.apache.commons.codec.binary.Base64;

import com.eos.data.datacontext.DataContextManager;
import com.eos.data.datacontext.ISessionMap;
import com.eos.runtime.core.TraceLoggerFactory;
import com.eos.system.annotation.Bizlet;
import com.eos.system.logging.Logger;
import com.primeton.ext.common.muo.MUODataContextHelper;

import javax.crypto.Cipher;
import javax.servlet.http.HttpSession;

import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

public class RSAUtil {
	
	private static Logger log = TraceLoggerFactory.getLogger(RSAUtil.class);

	private static Map<Integer, String> keyMap = new HashMap<Integer, String>();  //用于封装随机产生的公钥与私钥
	public static void main(String[] args) throws Exception {
		//生成公钥和私钥
		genKeyPair();
		//加密字符串
		String message = "sh";
		//System.out.println("随机生成的公钥为:" + keyMap.get(0));
		//System.out.println("随机生成的私钥为:" + keyMap.get(1));
		String messageEn = encrypt(message,"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCcR7bXrmYuCWBgbq03dTgNPWTM85Qzsok/dhhABhawXjdw9aqR0dQEBZNrIF9b2bUDvB6dTKckL0EnICCyScsIACZ9QZXIk1+qUGuivqjR7+o3Jd37+ZL/8cGzXuqs3Xd4fQX6BhQz4IXudQZp4oHzaxk6/8aXZgX/oM8FqYg6yQIDAQAB");
		System.out.println(message + "\t加密后的字符串为:" + messageEn);
		String messageDe = decrypt("QXfhOfjXtDsvAyt0bTCg58erMJbEsKnv042FuQGAozmKLeftu/Hrlz2i+CF6F4bl9mkzmPdZdf2XsPaZQwtlsuhg12lQIhpw3J5ZTRw3huDCGFs5vj/f6RRDnUtSMURSNUmmDJycPWcdVMAk0m9KdbTI+EOUMySfLsbZCqnVIhc","MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJxHtteuZi4JYGBurTd1OA09ZMzzlDOyiT92GEAGFrBeN3D1qpHR1AQFk2sgX1vZtQO8Hp1MpyQvQScgILJJywgAJn1BlciTX6pQa6K+qNHv6jcl3fv5kv/xwbNe6qzdd3h9BfoGFDPghe51BmnigfNrGTr/xpdmBf+gzwWpiDrJAgMBAAECgYBE6kry6XEds/zQCX5RClvhWsVsq60a7wwe9BDVitnOhH2mTm8dRo5cB6lugCQM+QAd2YMWkorSJhRxEHXaqTfiaiz54c3DcpMxv1CUcLdkw67IVaXxLsjbmP4lNgzBTkbPiU99L3/9mAbb/2/ZZ8IfQfk84IH6wKfyjhCuiAVaAQJBANr19bUbiEb4hmN+RSVvbl/ZOGTGQB1MDPyL12WEocQHIriYlnkT8JJ1U4tUR2/shjTg1gYkjMDCpWAfUfZpcfECQQC2t2Kaef33PDF5U2DGpm/mxhcy6tDUf9UkiGXFAwzzT/gVJrbcknKkhCZ/Js5wx+QobsITi1X8uNqSS1D6e35ZAkBzi8gVwmmvygZhColCsAL24ZI428WJsFBKFSGdmNgksf9Imn0Nd9BD+VYU+GyaS56m2N8YbsE4F39dsil2dz6BAkBNc0a0vGlO15ERIKqxCfP9GpLS3n5O1TEAH2EkNbrTmF5MePCiZuF5oxq+YzC21cR87xYNOs7kJWdhA9P7mUVhAkBMTq0H9CnNwU2ysrNjVQJbqsV626qa5zTLu4zL9H9NjSs2iPdC2pSedl+0/vYwZRQLCW3y0COp2POlIBGWYN3P");
		System.out.println("还原后的字符串为:" + messageDe);
		
		//String messageDe2 = decrypt(messageEn,keyMap.get(1));
		//System.out.println("还原后的字符串为:" + messageDe2);
		
	}
	
	/** 
	 * 随机生成密钥对 
	 * @throws NoSuchAlgorithmException 
	 */  
	public static void genKeyPair() throws NoSuchAlgorithmException {  
		// KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象  
		KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");  
		// 初始化密钥对生成器，密钥大小为96-1024位  
		keyPairGen.initialize(1024,new SecureRandom());  
		// 生成一个密钥对，保存在keyPair中  
		KeyPair keyPair = keyPairGen.generateKeyPair();  
		RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();   // 得到私钥  
		RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();  // 得到公钥  
		String publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));  
		// 得到私钥字符串  
		String privateKeyString = new String(Base64.encodeBase64((privateKey.getEncoded())));  
		// 将公钥和私钥保存到Map
		keyMap.put(0,publicKeyString);  //0表示公钥
		keyMap.put(1,privateKeyString);  //1表示私钥
	}  
	/** 
	 * RSA公钥加密 
	 *  
	 * @param str 
	 *            加密字符串
	 * @param publicKey 
	 *            公钥 
	 * @return 密文 
	 * @throws Exception 
	 *             加密过程中的异常信息 
	 */  
	public static String encrypt( String str, String publicKey ) throws Exception{
		//base64编码的公钥
		byte[] decoded = Base64.decodeBase64(publicKey.getBytes());
		RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
		//RSA加密
		Cipher cipher = Cipher.getInstance("RSA");
		cipher.init(Cipher.ENCRYPT_MODE, pubKey);
		String outStr = new String( Base64.encodeBase64(cipher.doFinal(str.getBytes("UTF-8"))));
		return outStr;
	}

	/** 
	 * RSA私钥解密
	 *  
	 * @param str 
	 *            加密字符串
	 * @param privateKey 
	 *            私钥 
	 * @return 铭文
	 * @throws Exception 
	 *             解密过程中的异常信息 
	 */  
	@Bizlet("RSA解密")
	public static String decrypt(String str, String privateKey) throws Exception{
		log.info("解密前字符串 ==>" + str);
		//64位解码加密后的字符串
		byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
		//base64编码的私钥
		byte[] decoded = Base64.decodeBase64(privateKey.getBytes());  
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));  
		//RSA解密
		Cipher cipher = Cipher.getInstance("RSA");
		cipher.init(Cipher.DECRYPT_MODE, priKey);
		String outStr = new String(cipher.doFinal(inputByte));
		log.info("解密后字符串 ==>" + str);
		return outStr;
	}
	@Bizlet("getRSAkey")
	public static String getRSAkey(){
		try {
			String rsakey = RSAUtil.getsessionParm("RSAKEY");
			log.info("rsakey ==>" + rsakey);
			return rsakey;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public static String getsessionParm(String name) {
		ISessionMap sessionMap = DataContextManager.current().getSessionCtx();
		if (sessionMap == null) {
			sessionMap = MUODataContextHelper.getMapContextFactory().getSessionMap();
		}
		if (sessionMap != null) {
			Object rootObject = sessionMap.getRootObject();
			if ((rootObject != null) && (rootObject instanceof HttpSession)) {
				HttpSession session = (HttpSession) rootObject;
				if (((Map<Integer, String>) session.getAttribute(name)) != null) {
					return ((Map<Integer, String>) session.getAttribute(name)).get(1).toString();
				}
			}
		}
		return null;
	}
}



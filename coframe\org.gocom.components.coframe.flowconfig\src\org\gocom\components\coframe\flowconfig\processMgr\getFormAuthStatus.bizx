<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getFormAuthStatus" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="添加和编辑规则时，获取规则关联表单权限信息" title="wanghl&#x9;13-3-13 下午6:46">
    <location x="93" y="240"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" description="" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NULLOREMPTY">
          <process:leftOperand type="query">ruleId</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="link4" displayName="连接线" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NOTNULLANDEMPTY">
          <process:leftOperand type="query">ruleId</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="91" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link3</targetConnections>
    <location x="825" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="java.util.List" name="formStatus" type="query" valueType="Java">formStatus</process:return>
      <process:return id="1" language="java.util.List" name="fields" type="query" valueType="Java">fields</process:return>
      <process:return id="2" language="java.util.List" name="actions" type="query" valueType="Java">actions</process:return>
      <process:return id="3" language="commonj.sdo.DataObject" name="retProc" type="query" valueType="Java">retProc</process:return>
      <process:return id="4" language="String" name="retRuleId" type="query" valueType="Primitive">retRuleId</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="93" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="827" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="获取随机规则ID" displayName="getRandomRuleId" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>invokeSpring2</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="205" y="75"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.FlowConfigService.getRandomRuleId</process:partner>
      <process:instance instanceName="FlowConfigServiceBean"/>
    </process:spring>
    <process:inputVariables/>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">ruleId</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="176" y="111"/>
    <figSize height="17" width="86"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="获取规则表单权限" displayName="getFormStatusAuth" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="510" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.FlowConfigService.getFormStatusAuth</process:partner>
      <process:instance instanceName="FlowConfigServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="formId" type="query" value="java.lang.String" valueType="Java" pattern="reference">proc/formId</process:inputVariable>
      <process:inputVariable id="1" name="stateId" type="query" value="java.lang.String" valueType="Java" pattern="reference">proc/stateId</process:inputVariable>
      <process:inputVariable id="2" name="moduleId" type="query" value="java.lang.String" valueType="Java" pattern="reference">proc/moduleId</process:inputVariable>
      <process:inputVariable id="3" name="ruleId" type="query" value="java.lang.String" valueType="Java" pattern="reference">ruleId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.Object[]" valueType="Java">object</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="476" y="186"/>
    <figSize height="17" width="97"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="675" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">object[1]</process:from>
      <process:to type="query">formStatus</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">object[2]</process:from>
      <process:to type="query">actions</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">object[3]</process:from>
      <process:to type="query">fields</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">proc</process:from>
      <process:to type="query">retProc</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">ruleId</process:from>
      <process:to type="query">retRuleId</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="677" y="186"/>
    <figSize height="17" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="解析流程配置文件" displayName="parseForm" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <targetConnections>link4</targetConnections>
    <location x="345" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.ProcessConfigServiceBean.parseForm</process:partner>
      <process:instance instanceName="ProcessConfigServiceBeanBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="proc" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">proc</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="311" y="186"/>
    <figSize height="17" width="97"/>
    <node>invokeSpring2</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="wanghl" createTime="2013-03-12 11:15:58" date="2013-03-12Z" description="" name="addRule" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input anyType="commonj.sdo.DataObject" description="" isArray="false" name="proc"/>
    <process:input description="" isArray="false" name="ruleId" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="java.util.List" description="" isArray="false" name="formStatus"/>
    <process:output anyType="java.util.List" description="" isArray="false" name="fields"/>
    <process:output anyType="java.util.List" description="" isArray="false" name="actions"/>
    <process:output anyType="commonj.sdo.DataObject" description="" isArray="false" name="retProc"/>
    <process:output description="" isArray="false" name="retRuleId" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

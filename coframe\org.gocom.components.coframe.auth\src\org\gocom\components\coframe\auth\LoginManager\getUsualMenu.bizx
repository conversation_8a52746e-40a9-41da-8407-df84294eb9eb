<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="getUsualMenu" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link1</targetConnections>
    <location x="525" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="commonj.sdo.DataObject[]" name="result" type="query" valueType="Java">result</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="527" y="186"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="queryEntitiesByCriteriaEntity" displayName="queryEntitiesByCriteriaEntity" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="345" y="150"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.eos.foundation.database.DatabaseUtil.queryEntitiesByCriteriaEntity</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="dsName" type="literal" value="java.lang.String" valueType="Java" pattern="reference">default</process:inputVariable>
      <process:inputVariable id="1" name="criteriaEntity" type="query" value="commonj.sdo.DataObject" valueType="Java" pattern="reference">btn</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="commonj.sdo.DataObject[]" valueType="Java">result</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="269" y="186"/>
    <figSize height="12" width="175"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="225" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">org.gocom.components.coframe.auth.queryentity.AppUsualmenu</process:from>
      <process:to type="query">btn/_entity</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">=</process:from>
      <process:to type="query">btn/_expr[1]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">empid</process:from>
      <process:to type="query">btn/_expr[1]/_property</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/userId</process:from>
      <process:to type="query">btn/_expr[1]/_value</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">=</process:from>
      <process:to type="query">btn/_expr[2]/_op</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">depttype</process:from>
      <process:to type="query">btn/_expr[2]/_property</process:to>
    </process:copy>
    <process:copy>
      <process:from type="query" pattern="reference">m:userObject/attributes/EmpObject/LoginUserInfo/S_DEPTAPPS</process:from>
      <process:to type="query">btn/_expr[2]/_value</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="227" y="186"/>
    <figSize height="12" width="25"/>
    <node>assign0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="gkm" createTime="2015-08-09 15:44:31" date="2015-08-09Z" description="" name="getBtnData" version="*******"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="btn"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="sendData" primitiveType="String"/>
    <process:input description="" isArray="false" name="dbName" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="commonj.sdo.DataObject" description="" isArray="true" name="result"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

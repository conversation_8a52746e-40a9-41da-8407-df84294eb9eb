<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="queryAuthbiz.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link3</targetConnections>
    <location x="390" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="java.util.HashMap" name="result" type="query" valueType="Java">result</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="392" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="queryAuthInfo" displayName="getAuthInfo" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="ISNULL">
          <process:leftOperand type="query">mac</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="242" y="150"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="static" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.license.AuthorizationService.getAuthInfo</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables/>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.HashMap&lt;java.lang.String,java.lang.String>" valueType="Java">result</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="212" y="186"/>
    <figSize height="17" width="83"/>
    <node>invokePojo1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2019-07-24 11:30:53" date="2019-07-24Z" description="" name="queryAuthbiz" version="*******"/>
  <process:variables/>
  <process:inputs varArgs="false"/>
  <process:outputs>
    <process:output anyType="java.util.HashMap" description="" isArray="false" name="result"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

<?xml version="1.0" encoding="UTF-8"?>
<handlers>
	<!--
	Handlders that are added to entities. The execution order of these handlers 
	are the same as the order they are defined in this file.
	
	class：the class name of the Handler, must implement com.primeton.das.entity.impl.handler.IEntityHandler
	there can be multiple <match> elements, the attribute matchName of match element is the full name of an entity.
	-->
	<!--
	<handler id="handler1"
		class="com.primeton.server.das.persistententity.handler.MyHandler">
		<match matchName="Address"/>
	</handler>
	-->
	<handler id="orgHandler"
		class="org.gocom.components.coframe.tools.syslog.syslog.SysLogHandler">
		<match matchName="org.gocom.components.coframe.org*" isRegex="true"/>
	</handler>
	
	<handler id="rightHandler"
		class="org.gocom.components.coframe.tools.syslog.syslog.SysLogHandler">
		<match matchName="org.gocom.components.coframe.rights*" isRegex="true"/>
	</handler>
	
	<handler id="frameworkHandler"
		class="org.gocom.components.coframe.tools.syslog.syslog.SysLogHandler">
		<match matchName="org.gocom.components.coframe.framework*" isRegex="true"/>
	</handler>
	
</handlers>

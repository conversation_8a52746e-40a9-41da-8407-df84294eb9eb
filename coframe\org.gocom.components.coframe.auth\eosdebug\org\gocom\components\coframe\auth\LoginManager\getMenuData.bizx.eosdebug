<?xml version="1.0" encoding="UTF-8"?><root>
    <case1>
        <root>
            <data>
                <appCode __isNullOrEmpty="null" __parameterDataType="String" __type="java:java.lang.String"/>
            </data>
            <MUOContext>
                <userObject __parameterDataType="java:com.eos.data.datacontext.UserObject" __type="java:com.eos.data.datacontext.UserObject">
                    <userId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userMail __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userName __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userOrgId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userOrgName __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userRealName __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <uniqueId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <userRemoteIP __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                    <sessionId __parameterDataType="java:java.lang.String" __type="java:java.lang.String"/>
                </userObject>
            </MUOContext>
        </root>
    </case1>
</root>

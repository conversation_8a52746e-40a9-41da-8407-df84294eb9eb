<?xml version="1.0" encoding="UTF-8"?>
<process:tPageFlow xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="jumpToSPD" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******" state="stateless">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="HIS超链接跳转SPD系统（赣州市人民医院耗材）" title="Kyire6&#x9;22-9-19 上午9:24">
    <location x="60" y="300"/>
    <size height="100" width="148"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tActionLink" description="" id="link0" name="link0" lineType="reference" isDefault="true" type="action" actionName="action0" dataConvertClass="">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:validateRules errorPage="" onError="default"/>
      <process:inputParameters>
        <process:parameter description="医生工号（登录名）" historyStateLocation="client" isArray="false" name="userId" primitiveType="String"/>
        <process:parameter description="手术单申请单号" historyStateLocation="client" isArray="false" name="operno" primitiveType="String"/>
      </process:inputParameters>
    </sourceConnections>
    <location x="15" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end0" name="登录界面" displayName="结束" type="end" contextPath="" method="forward" uri="/coframe/auth/login/login.jsp" variableUri="false">
    <targetConnections>link4</targetConnections>
    <location x="347" y="69"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:inputVariables/>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="17" y="186"/>
    <figSize height="16" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="337" y="105"/>
    <figSize height="16" width="49"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tEnd" description="" id="end1" name="主页" displayName="结束" type="end" contextPath="" method="forward" uri="/coframe/auth/login/redirect.jsp">
    <targetConnections>link3</targetConnections>
    <location x="346" y="195"/>
    <size height="28" width="28"/>
    <nodeLabel>end1label</nodeLabel>
    <process:inputVariables/>
    <process:returns>
      <process:return id="0" name="operno" type="query">operno</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end1label" name="label" nodeType="label">
    <location x="348" y="231"/>
    <figSize height="16" width="25"/>
    <node>end1</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="验证登陆" displayName="loginById" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal"/>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="link3" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>end1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand>result</process:leftOperand>
          <process:rightOperand>1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="195" y="115"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="0" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginManager.loginByUserId</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="retCode" type="query" value="Int" valueType="Primitive">result</process:outputVariable>
        <process:outputVariable id="1" name="msg" type="query" value="String" valueType="Primitive"/>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="184" y="150"/>
    <figSize height="16" width="49"/>
    <node>subprocess1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo0" name="运算逻辑" displayName="decrypt" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>subprocess1</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="123" y="88"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true">
      <process:partner type="literal">org.gocom.components.coframe.auth.DESHexUtil.decrypt</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="pToDecrypt" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
      <process:inputVariable id="1" name="key" type="literal" value="java.lang.String" valueType="Java" pattern="reference">X!xO@kA)</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">userId</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="110" y="124"/>
    <figSize height="16" width="49"/>
    <node>invokePojo0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="cuizhichao" createTime="2014-07-23 13:47:13" date="2014-07-23Z" description="" name="login" version="*******"/>
  <process:variables>
    <process:variable description="" historyStateLocation="client" isArray="false" name="result" primitiveType="Int"/>
  </process:variables>
  <process:inputParameters/>
</process:tPageFlow>

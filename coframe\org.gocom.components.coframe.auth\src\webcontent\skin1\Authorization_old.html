<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title>授权</title>
    <meta charset="utf-8" http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- link css -->
    <link rel="stylesheet" href="./css/bootstrap.min.css">

</head>

<body class="skin-blue">
    <div class="content">
        <div class="content" style="text-align: center;padding-top:10%;padding:10px">
            <h5 style="line-height:30px;text-align:center;">
                您的系统未被授权,请提供MAC地址并联系</br>
                <span style="font-weight:bold">万序健康&nbsp;021-55155381</span>获取许可证书。
                </br>
                MAC地址:<span id="mac"></span>
            </h5>
            <h5 style="line-height:30px;">
                <button type="button" class="btn btn-info" onclick="shouquan()">去授权</button>
            </h5>
        </div>
        <div class="modal fade oder-info" id="alert-proof">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="tab-pane active" id="tab-2">

                            <div class="box box-solid box-default input-mg">
                                <div class="box-header">
                                    <h4 class="box-title">系统授权</h4>
                                </div>
                                <div id="proofForm">
                                    <input type="text" name="yy" id="yy" placeholder="项目名">
                                    <input type="text" name="scode" id="scode" placeholder="授权码">&nbsp;&nbsp;&nbsp;
                                    <button type="button" class="btn btn-info" onclick="checkcode()">确认</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="./js/jquery.min.js"></script>
    <script src="./js/bootstrap.min.js"></script>

    <script>
        // 
        init();

        function init() {
            SetData(GetQueryString("mac"));
        }

        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        var mac = "";

        function SetData(data) {
            mac = data;
            $("#mac").html(mac);
        }

        function shouquan() {
            $("#alert-proof").modal("show");
        }

        function checkcode() {
            var yyname = $("#yy").val();
            var scode = $("#scode").val();
            if (yyname == "" || yyname == null || scode == "" || scode == null) {
                alert("输入有误！");
                return;
            }

            var sendData = {
                "projectname": yyname,
                "authcode": scode
            };
            $.ajax({
                url: "org.gocom.components.coframe.auth.LoginManager.eoslockinsertbiz.biz.ext",
                data: sendData,
                type: "post",
                cache: false,
                async: false,
                success: function (result) {
                    if (result.rs == false) {
                        alert("授权失败!");

                    } else if (result.rs == true) {
                        alert("授权成功，请重新登录!");
                        window.location.href = "../login/logout.jsp";

                    }
                }
            });

        }
    </script>
</body>

</html>
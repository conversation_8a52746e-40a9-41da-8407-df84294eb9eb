<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="importDict.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" collapsed="false" nodeType="common" type="end">
    <targetConnections>link2</targetConnections>
    <targetConnections>link0</targetConnections>
    <location x="800" y="134"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="String" name="reCode" type="query" valueType="Primitive">reCode</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <location x="802" y="170"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" collapsed="false" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="link4" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>transactionbegin0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="60" y="134"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <location x="62" y="170"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="失败" displayName="link3" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"></process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="652" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-1</process:from>
      <process:to type="query">reCode</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="654" y="244"/>
    <figSize height="12" width="25"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="导入业务字典" displayName="dictImport" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link3" name="link3" displayName="link0" lineType="note" type="exception">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>transactionrollback0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="link1" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>transactioncommit0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="356" y="134"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.dict.impl.DictService.dictImport</process:partner>
      <process:instance instanceName="DictServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="excelFile" type="query" value="java.lang.String" valueType="Java" pattern="reference">excelFile</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java"></process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="334" y="170"/>
    <figSize height="12" width="73"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionBegin" id="transactionbegin0" name="事务开始" displayName="事务开始" type="transactionbegin">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactionbegin0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="208" y="134"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionbegin0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionbegin0label" name="label" nodeType="label">
    <location x="198" y="170"/>
    <figSize height="12" width="49"/>
    <node>transactionbegin0</node>
  </nodes>
  <nodes xsi:type="process:tTransactionCommit" id="transactioncommit0" name="事务提交" displayName="事务提交" type="transactioncommit">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactioncommit0</sourceNode>
      <targetNode>assign1</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <location x="504" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>transactioncommit0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactioncommit0label" name="label" nodeType="label">
    <location x="494" y="96"/>
    <figSize height="12" width="49"/>
    <node>transactioncommit0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link0" name="成功" displayName="link2" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="652" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">1</process:from>
      <process:to type="query">reCode</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="654" y="96"/>
    <figSize height="12" width="25"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tTransactionRollback" description="" id="transactionrollback0" name="事务回滚" displayName="事务回滚" type="transactionrollback">
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>transactionrollback0</sourceNode>
      <targetNode>assign0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="504" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>transactionrollback0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="transactionrollback0label" name="label" nodeType="label">
    <location x="494" y="244"/>
    <figSize height="12" width="49"/>
    <node>transactionrollback0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="陈鹏" createTime="2013-12-17 16:39:30" date="2013-12-17Z" description="" name="导入业务字典" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="上传后的Excel文件名" isArray="false" name="excelFile" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="reCode" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

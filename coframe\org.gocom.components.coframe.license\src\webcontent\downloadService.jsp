<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="java.io.*"%>
<%@page import="java.net.URLEncoder"%>
<%@page import="java.net.URLDecoder"%>
<%@page import="java.io.File"%>
<%
    request.setCharacterEncoding("UTF-8");
    response.setCharacterEncoding("UTF-8");

    String filePath = (String) request.getAttribute("filePath");
    String fileName = (String) request.getAttribute("fileName");
    File file = new java.io.File(filePath);
    if (file.exists()) {
		long filesize = file.length();
		response.addHeader("content-type", "application/x-msdownload;");
		response.addHeader(
			"Content-Disposition",
			"attachment;filename="
				+ URLEncoder.encode(fileName, "UTF-8"));
		response.setContentType("application/msexcel;charset=UTF-8");
		response.addHeader("content-length", Long.toString(filesize));
		java.io.FileInputStream fin = new java.io.FileInputStream(file);

		out.clear();
		out = pageContext.pushBody();

		byte[] b = new byte[1];
		int j = 0;
		while ((j = fin.read(b)) > 0) {
		    response.getOutputStream().write(b);
		}
		fin.close();
    } else {
		out.println("文件不存在");
    }
%>

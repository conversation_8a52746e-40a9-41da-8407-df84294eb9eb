package org.gocom.components.coframe.auth.login;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import com.eos.system.annotation.Bizlet;

public class PatientApi {

	@Bizlet
	public static String getPatient(String patientid,String pattype){
		BufferedReader reader = null;
		String result = null;
		StringBuffer sbf = new StringBuffer();
		try {
			// 用java JDK自带的URL去请求
			URL url = new URL("http://192.168.99.81:9961/api/patientinfo/single?patienttype="+pattype+"&patientid="+patientid);
			HttpURLConnection connection = (HttpURLConnection) url
					.openConnection();
			// 设置该请求的消息头
			// 设置HTTP方法：POST
			connection.setRequestMethod("GET");
			connection.setDoOutput(true);
			connection.connect();
			InputStream is = connection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
				sbf.append("\r\n");
			}
			reader.close();
			result = sbf.toString();
			System.out.println(result);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	
	public static void main(String[] args) {
		getPatient("2021-05254987-0","1");
	}
}

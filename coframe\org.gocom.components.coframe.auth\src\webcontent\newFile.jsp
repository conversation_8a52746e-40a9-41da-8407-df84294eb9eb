<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- 
  - Author(s): Administrator
  - Date: 2020-08-11 15:01:34
  - Description:
-->
<head>
<title>Title</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="<%= request.getContextPath() %>/common/nui/nui.js" type="text/javascript"></script>
    
</head>
<body>

<img id="bigImage" src="data:image/png;base64,+返回的base64图片码" alt="抠图">
<img id="smallImage" src="data:image/png;base64,+返回的base64图片码" alt="带抠图阴影的原图">

<script type="text/javascript" src=""slider.js></script>
	<script type="text/javascript">
    	nui.parse();
    function rposition(fix,rel,options){
    console.log(fix);
    var rectLeft,rectTop,rectH=fix.outerHeight(),rectW=fix.outerWidth(),wh=$(window).height(),ww=$(window).width(),
    sT=$(document).scrollTop(),sL=$(document).scrollLeft(),
    defaults={
        align:"left",
        vlign:"top",
        xleft:0,
        xtop:0,
        adjust:true,
        rwindow:false
    },
    options = $.extend(defaults, options);
    var rectRel={
        l:$(rel).offset().left,
        t:$(rel).offset().top,
        w:$(rel).outerWidth(),
        h:$(rel).outerHeight()
    };
    switch(options.align){
        case "left":
         rectLeft=rectRel.l;break;
        case "right":
         rectLeft=rectRel.l+rectRel.w;break;
        case "center":
         rectLeft=rectRel.l+rectRel.w/2;break;
        case "rleft":
         rectLeft=rectRel.l-rectW;break;
         default:
         rectLeft=rectRel.l;
    };
    switch(options.vlign){
        case "top":
         rectTop=rectRel.t;break;
        case "center":
         rectTop=rectRel.t+rectRel.h/2;break;
        case "vbottom":
        rectTop=rectRel.t-rectH; break;
        case "bottom":
        default:
        rectTop=rectRel.t+rectRel.h;
    };
    if(options.rwindow){
        if(options.align=="center")rectLeft=(ww-rectW)/2+sL;
        if(options.vlign=="center")rectTop=(wh-rectH)/2+sT;
    };
    if(options.adjust){
        if(rectLeft+rectW>ww+sL){rectLeft-=(rectLeft+rectW)-(ww+sL)}
        if(rectTop+rectH>wh+sT){rectTop=rectRel.t-rectH;}
    };
    $(fix).css({"left":rectLeft+options.xleft,"top":rectTop+options.xtop});
}	

        	getMenuData();
    		function getMenuData() {
			$.ajax({
				url: "org.gocom.components.coframe.auth.newLoginManager.newbiz.biz.ext",
				type: "POST",
				success: function (rs) {
                  $("#bigImage")[0].src="data:image/png;base64,"+rs.out1.bigImage; 
                  $("#smallImage")[0].src="data:image/png;base64,"+rs.out1.smallImage;
                  var defaults={
						align:"left",
						vlign:"top",
						xleft:0,
						xtop:rs.out1.yHeight,
						adjust:true,
						rwindow:false
					}
				//var win = new win();
				
                  rposition($("#smallImage")[0],$("#bigImage")[0],defaults); 
				}
			});
		}
		
		
		 

    </script>
</body>
</html>
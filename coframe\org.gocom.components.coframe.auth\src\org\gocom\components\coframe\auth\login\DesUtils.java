package org.gocom.components.coframe.auth.login;

import java.security.MessageDigest;
import java.util.Date;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public final class DesUtils {

	public final String encrypt(String plain) {
		if (plain == null || plain.length() == 0)
			return null;
		try {
			byte[] seed = getSeed();
			String seedStr = base64Encode(seed);
			byte[] key = generateKey(seed);
			return seedStr + encrypt(plain, key);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public final String encrypt(String plain, String key) throws Exception {
		return encrypt(plain, getKeyByString(key));
	}

	private final String encrypt(String plain, byte[] key) throws Exception {
		byte[] encrypted = encryptByJCE(plain.getBytes(), key);
		return base64Encode(encrypted);
	}

	private byte[] encryptByJCE(byte[] plainText, byte[] key) throws Exception {
		SecretKey securekey = new SecretKeySpec(key, "DESede");
		Cipher cipher = Cipher.getInstance("DESede");
		cipher.init(1, securekey);
		return cipher.doFinal(plainText);
	}

	public final String decrypt(String cryptograph) {
		if (cryptograph == null || cryptograph.length() == 0)
			return "";
		try {
			String seedStr = cryptograph.substring(0, 12);
			byte[] seed = base64Decode(seedStr);
			byte[] key = generateKey(seed);
			return decryptByJCE(cryptograph.substring(12), key);
		} catch (Exception e) {
			e.printStackTrace();
			return "";
		}
	}

	public final String decrypt(String cryptograph, String key)
			throws Exception {
		return decryptByJCE(cryptograph, getKeyByString(key));
	}

	private final String decryptByJCE(String cryptograph, byte[] key)
			throws Exception {
		byte[] encrypted = base64Decode(cryptograph);
		return new String(decrypt(encrypted, key));
	}

	private byte[] decrypt(byte[] cryptograph, byte[] key) throws Exception {
		SecretKey securekey = new SecretKeySpec(key, "DESede");
		Cipher cipher = Cipher.getInstance("DESede");
		cipher.init(2, securekey);
		return cipher.doFinal(cryptograph);
	}

	private byte[] generateKey(byte[] seed) throws Exception {
		byte[] key = { 36, 80, 114, 105, 109, 101, 116, 111, 110, 45, 69, 79, 83, 32, 87, 105, 108, 108, 95, 87, 105, 110, 33, 36 };
		for (int i = 0; i < seed.length; i++) {
			for (int j = 0; j < key.length; j++)
				key[j] = (byte) (key[j] ^ seed[i]);
		}
		return key;
	}

	private byte[] getSeed() {
		long seed = (new Date()).getTime();
		byte[] seedBytes = String.valueOf(seed).getBytes();
		try {
			MessageDigest digest = MessageDigest.getInstance("MD5");
			return base64Decode(base64Encode(digest.digest(seedBytes)).substring(0, 12));
		} catch (Exception e) {
			return seedBytes;
		}
	}

	public String base64Encode(byte[] bytes) {
		// return new String(bytes);
		return new String(new MyBase64().encodeBase64(bytes));
	}

	public byte[] base64Decode(String str) {
		return new MyBase64().decodeBase64(str.getBytes());
	}

	private byte[] getKeyByString(String key) {
		byte[] oldKeys = key.getBytes();
		byte[] newKeys = new byte[24];
		for (int i = 0; i < oldKeys.length && i != 24; i++)
			newKeys[i] = oldKeys[i];
		return newKeys;
	}

	public boolean equalsIgnoreCase(String str1, String str2) {
		return (str1 == null) ? ((str2 == null)) : str1.equalsIgnoreCase(str2);
	}

	public static void main(String[] args) throws Exception {
		System.out.println(new DesUtils().decrypt("m0OXDSi7g7U6AHS7FEFGtA==","cap_user"));
	}
}
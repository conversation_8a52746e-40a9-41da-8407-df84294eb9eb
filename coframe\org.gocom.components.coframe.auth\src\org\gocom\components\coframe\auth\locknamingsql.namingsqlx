<?xml version="1.0" encoding="UTF-8"?>
<!-- author:Administrator -->
<sqlMap>
       <select id="selecteosdictlock" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
			  select a.macaddrs,
             a.authcode,
             a.isauth,
             a.projectname,
             to_char(a.authdate, 'yyyy-mm-dd') authdate,
             to_char(a.authexpdate, 'yyyy-mm-dd') authexpdate,
             a.lsjzs,
             trunc(authexpdate-sysdate)  as countdowndate
        from EOS_DICT_LOCK a  where 1=1 
          <isNotEmpty property="mac">
		     	 and a.macaddrs = '$mac$' 
			 </isNotEmpty>
          order by authexpdate desc
         
      </select>
       <insert id="inserteosdictlock" parameterClass="java.util.HashMap" >
				insert into EOS_DICT_LOCK
				  (macaddrs, 
				  authcode, 
				  projectname,
                 AUTHEXPDATE
				)
				values
				  ('$macaddrs$',
				   '$authcode$',
				   '$projectname$',
				  to_date( '$authexpdate$','yyyy-MM-dd')
				 )
      </insert>
      
          <delete id="del" parameterClass="java.util.HashMap" >
				     delete eos_dict_lock
          </delete>
</sqlMap>
<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="deleteMenu" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link1</targetConnections>
    <location x="650" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="12" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="652" y="96"/>
    <figSize height="12" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="删除菜单" displayName="deleteMenuById" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="502" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppMenuService.deleteMenuById</process:partner>
      <process:instance instanceName="AppMenuBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="id" type="query" value="java.lang.String" valueType="Java" pattern="reference">menuid</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="492" y="96"/>
    <figSize height="12" width="49"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="queryMenuTreeNode" displayName="queryMenuTreeNode" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>subprocess1</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="208" y="61"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="1" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.queryMenuTreeNode</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="nodeId" type="query" value="String" valueType="Primitive" pattern="reference">menuid</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="data" type="query" value="java.util.List" valueType="Java">menuList</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="170" y="96"/>
    <figSize height="12" width="103"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="deleteMenus" displayName="deleteMenuNodes" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="355" y="61"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="0" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">this.deleteMenuNodes</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="nodeList" type="query" value="java.util.List" valueType="Java" pattern="reference">menuList</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables/>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="335" y="96"/>
    <figSize height="12" width="67"/>
    <node>subprocess1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-04 16:16:56" date="2013-03-04Z" description="" name="deleteFuncGroup" version="6.3"/>
  <process:variables>
    <process:variable anyType="java.util.List" description="" historyStateLocation="client" isArray="false" name="menuList"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="menuid" primitiveType="String"/>
  </process:inputs>
  <process:outputs/>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

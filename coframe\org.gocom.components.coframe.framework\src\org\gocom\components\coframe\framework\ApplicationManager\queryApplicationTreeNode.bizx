<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="queryApplicationTreeNode.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>switch0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link6" name="link6" displayName="连接线" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NULLOREMPTY">
          <process:leftOperand type="query">nodeId</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="45" y="326"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link19</targetConnections>
    <targetConnections>link4</targetConnections>
    <targetConnections>link9</targetConnections>
    <targetConnections>link12</targetConnections>
    <targetConnections>link18</targetConnections>
    <location x="949" y="326"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="java.util.List" name="data" type="query" valueType="Java">data</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="47" y="362"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="951" y="362"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="查询所有应用" displayName="queryAllAppApplications" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>invokeSpring0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link6</targetConnections>
    <location x="357" y="105"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.queryAllAppApplications</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppApplication[]" valueType="Java">appapplicaitons</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="335" y="141"/>
    <figSize height="17" width="73"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="应用树根" displayName="getApplicationRoot" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <location x="643" y="105"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.getApplicationRoot</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="application" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appapplicaitons</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.List&lt;java.util.Map>" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="633" y="141"/>
    <figSize height="17" width="49"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="查询功能组" displayName="queryAppFuncgroups" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>invokeSpring6</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="505" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.queryAppFuncgroups</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppFuncgroup[]" valueType="Java">appfuncgroups</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="489" y="244"/>
    <figSize height="17" width="61"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="查询子功能组" displayName="queryAppFuncgroups" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link15" name="link15" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>invokeSpring4</targetNode>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <location x="492" y="362"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFuncgroupService.queryAppFuncgroups</process:partner>
      <process:instance instanceName="AppFuncgroupBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppFuncgroup[]" valueType="Java">appfuncgroups</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="470" y="398"/>
    <figSize height="17" width="73"/>
    <node>invokeSpring3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring4" name="查询功能" displayName="queryAppFunctions" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring4</sourceNode>
      <targetNode>invokeSpring5</targetNode>
    </sourceConnections>
    <targetConnections>link15</targetConnections>
    <location x="628" y="362"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring4label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppFunctionService.queryAppFunctions</process:partner>
      <process:instance instanceName="AppFunctionBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.framework.application.AppFunction[]" valueType="Java">appfunctions</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring4label" name="label" nodeType="label">
    <location x="618" y="398"/>
    <figSize height="17" width="49"/>
    <node>invokeSpring4</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring5" name="构造子功能组和功能的list" displayName="getSubFunctionGroupAndFunctionNode" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link12" name="link12" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring5</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link11</targetConnections>
    <location x="765" y="362"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring5label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.getSubFunctionGroupAndFunctionNode</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="functionGroup" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appfuncgroups</process:inputVariable>
      <process:inputVariable id="1" name="function" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appfunctions</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.List&lt;java.util.Map>" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring5label" name="label" nodeType="label">
    <location x="711" y="398"/>
    <figSize height="17" width="137"/>
    <node>invokeSpring5</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring6" name="构造功能组list" displayName="getFunctionGroupNode" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link9" name="link9" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring6</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="643" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring6label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.getFunctionGroupNode</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="functionGroup" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appfuncgroups</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.List&lt;java.util.Map>" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring6label" name="label" nodeType="label">
    <location x="619" y="244"/>
    <figSize height="17" width="77"/>
    <node>invokeSpring6</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="赋值1" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link5" name="link5" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>invokeSpring2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">type</process:leftOperand>
          <process:rightOperand type="literal">application</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="356" y="208"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">nodeId</process:from>
      <process:to type="query">criteria/_expr[1]/appApplication.appid</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">null</process:from>
      <process:to type="query">criteria/_expr[2]/appFuncgroup</process:to>
    </process:copy>
    <process:copy>
      <process:from type="literal" pattern="reference">null</process:from>
      <process:to type="query">criteria/_expr[2]/_op</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="354" y="244"/>
    <figSize height="17" width="32"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="赋值2" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" description="" id="link7" name="link7" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>invokeSpring3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">functiongroup</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link19" name="link19" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">function</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="358" y="326"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="query" pattern="reference">nodeId</process:from>
      <process:to type="query">criteria/_expr[1]/appFuncgroup.funcgroupid</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="356" y="362"/>
    <figSize height="17" width="32"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring9" name="查询所有应用1" displayName="queryAllAppApplications" collapsed="false" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="link3" isDefault="true" type="transition">
      <sourceNode>invokeSpring9</sourceNode>
      <targetNode>invokeSpring10</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link17</targetConnections>
    <location x="361" y="516"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring9label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.queryAllAppApplications</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="criteria" type="query" value="com.eos.das.entity.criteria.CriteriaType" valueType="Java" pattern="reference">criteria</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out" type="query" value="org.gocom.components.coframe.framework.application.AppApplication[]" valueType="Java">appapplicaitons</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring9label" name="label" nodeType="label">
    <location x="335" y="552"/>
    <figSize height="17" width="80"/>
    <node>invokeSpring9</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring10" name="构造应用list1" displayName="getApplicationNode" collapsed="false" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link18" name="link18" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring10</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link16</targetConnections>
    <location x="653" y="516"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring10label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.framework.AppApplicationService.getApplicationNode</process:partner>
      <process:instance instanceName="AppApplicationBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="application" type="query" value="commonj.sdo.DataObject[]" valueType="Java" pattern="reference">appapplicaitons</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.util.List&lt;java.util.Map>" valueType="Java">data</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring10label" name="label" nodeType="label">
    <location x="631" y="552"/>
    <figSize height="17" width="72"/>
    <node>invokeSpring10</node>
  </nodes>
  <nodes xsi:type="process:tSwitch" id="switch0" name="空操作" displayName="空操作" type="switch">
    <sourceConnections xsi:type="process:tLink" description="" id="link17" name="link17" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>invokeSpring9</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">root</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link14" name="link14" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="OBJEQ">
          <process:leftOperand type="query">nodeType</process:leftOperand>
          <process:rightOperand type="literal">application</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link13" name="link13" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="204" y="326"/>
    <size height="28" width="28"/>
    <nodeLabel>switch0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="switch0label" name="label" nodeType="label">
    <location x="200" y="362"/>
    <figSize height="17" width="37"/>
    <node>switch0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="Administrator" createTime="2013-03-04 19:25:55" date="2013-03-04Z" description="" name="queryApplicationTreeNode" version="6.3"/>
  <process:variables>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppApplication" description="" historyStateLocation="client" isArray="true" name="appapplicaitons"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="com.primeton.das.criteria.criteriaType" name="criteria"/>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppFuncgroup" description="" historyStateLocation="client" isArray="true" name="appfuncgroups"/>
    <process:variable anyType="org.gocom.components.coframe.framework.application.AppFunction" description="" historyStateLocation="client" isArray="true" name="appfunctions"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="nodeType" primitiveType="String"/>
    <process:input description="" isArray="false" name="nodeId" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output anyType="java.util.List" description="" isArray="false" name="data"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="saveRule" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="保存、更新规则" title="wanghl&#x9;13-3-13 下午6:55">
    <location x="102" y="315"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link0" name="link0" displayName="连接线" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">createFlag</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link3</targetConnections>
    <targetConnections>link2</targetConnections>
    <location x="375" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Int" name="ret" type="query" valueType="Primitive">ret</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="377" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="更新规则" displayName="updateRule" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <location x="236" y="75"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.FlowConfigService.updateRule</process:partner>
      <process:instance instanceName="FlowConfigServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="ruleId" type="query" value="java.lang.String" valueType="Java" pattern="reference">ruleId</process:inputVariable>
      <process:inputVariable id="1" name="ruleName" type="query" value="java.lang.String" valueType="Java" pattern="reference">ruleName</process:inputVariable>
      <process:inputVariable id="2" name="nameSpace" type="query" value="java.lang.String" valueType="Java" pattern="reference">namespace</process:inputVariable>
      <process:inputVariable id="3" name="fc" type="query" value="org.gocom.components.coframe.flowconfig.model.AuthFlowCondition[]" valueType="Java" pattern="reference">fc</process:inputVariable>
      <process:inputVariable id="4" name="authRes" type="query" value="com.primeton.cap.auth.AuthResource[]" valueType="Java" pattern="reference">authRes</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">ret</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="226" y="111"/>
    <figSize height="17" width="49"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="保存规则" displayName="saveRule" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="234" y="226"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.flowconfig.FlowConfigService.saveRule</process:partner>
      <process:instance instanceName="FlowConfigServiceBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="ruleId" type="query" value="java.lang.String" valueType="Java" pattern="reference">ruleId</process:inputVariable>
      <process:inputVariable id="1" name="ruleName" type="query" value="java.lang.String" valueType="Java" pattern="reference">ruleName</process:inputVariable>
      <process:inputVariable id="2" name="nameSpace" type="query" value="java.lang.String" valueType="Java" pattern="reference">namespace</process:inputVariable>
      <process:inputVariable id="3" name="fc" type="query" value="org.gocom.components.coframe.flowconfig.model.AuthFlowCondition[]" valueType="Java" pattern="reference">fc</process:inputVariable>
      <process:inputVariable id="4" name="authRes" type="query" value="com.primeton.cap.auth.AuthResource[]" valueType="Java" pattern="reference">authRes</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">ret</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="224" y="262"/>
    <figSize height="17" width="49"/>
    <node>invokeSpring1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="wanghl" createTime="2013-03-06 10:53:16" date="2013-03-06Z" description="" name="updateRule" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="ruleId" primitiveType="String"/>
    <process:input description="" isArray="false" name="ruleName" primitiveType="String"/>
    <process:input description="" isArray="false" name="namespace" primitiveType="String"/>
    <process:input anyType="com.primeton.cap.auth.AuthResource" description="" isArray="true" name="authRes"/>
    <process:input anyType="org.gocom.components.coframe.flowconfig.model.AuthFlowCondition" description="" isArray="true" name="fc"/>
    <process:input description="" isArray="false" name="createFlag" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="ret" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

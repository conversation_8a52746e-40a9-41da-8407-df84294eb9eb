<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="addEmpgroup" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="添加工作组员工" title="gouyl&#x9;13-10-11 下午3:11">
    <location x="27" y="225"/>
    <size height="100" width="204"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" nodeType="common" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <location x="100" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" nodeType="common" type="end">
    <targetConnections>link1</targetConnections>
    <location x="400" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="102" y="186"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="402" y="186"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="添加工作组员工" displayName="addOrgEmpgroup" nodeType="common" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="258" y="150"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.org.OrgEmpgroupService.addOrgEmpgroup</process:partner>
      <process:instance instanceName="OrgEmpgroupService"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="orgEmpgroup" type="query" value="org.gocom.components.coframe.org.groupdataset.OrgEmpgroup[]" valueType="Java" pattern="reference">empPositions</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="230" y="186"/>
    <figSize height="17" width="85"/>
    <node>invokeSpring0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="yangyong" createTime="2013-03-02 22:06:11" date="2013-03-02Z" description="" name="addOrg" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input anyType="org.gocom.components.coframe.org.groupdataset.OrgEmpgroup" description="" isArray="true" name="empPositions"/>
  </process:inputs>
  <process:outputs/>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="applogin_new" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="用户登录" title="shitf&#x9;13-3-12 下午4:05">
    <location x="8" y="420"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <location x="60" y="229"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link22</targetConnections>
    <location x="990" y="229"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Int" name="retCode" type="query" valueType="Primitive">retCode</process:return>
      <process:return id="1" language="String" name="msg" type="query" valueType="Primitive">msg</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="265"/>
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="992" y="265"/>
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="验证用户" displayName="authentication" type="subprocess">
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>assign4</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link5" name="link5" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>assign5</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">retCode</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="285" y="225"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" transactionType="join" varArgs="false">
      <process:partner type="literal">this.authentication</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
        <process:inputVariable id="1" name="password" type="query" value="String" valueType="Primitive" pattern="reference">password</process:inputVariable>
        <process:inputVariable id="2" name="verifyimgflag" type="literal" value="String" valueType="Primitive" pattern="reference">false</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="returnValue" type="query" value="Int" valueType="Primitive">retCode</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="274" y="260"/>
    <figSize height="17" width="49"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="saveLoginLog" displayName="saveLoginLog" collapsed="false" type="invoke" index="2" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link22" name="link22" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link18</targetConnections>
    <targetConnections>link19</targetConnections>
    <location x="723" y="229"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginLogService.saveLoginLog</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="loginflag" type="query" value="java.lang.String" valueType="Java" pattern="reference">loginflag</process:inputVariable>
      <process:inputVariable id="1" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="694" y="265"/>
    <figSize height="17" width="81"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="赋值4" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link19" name="link19" displayName="link18" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="465" y="260"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">1</process:from>
      <process:to type="query">loginflag</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="463" y="296"/>
    <figSize height="17" width="32"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign5" name="赋值5" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link18" name="link18" displayName="link19" isDefault="true" type="transition">
      <sourceNode>assign5</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <location x="720" y="124"/>
    <size height="28" width="28"/>
    <nodeLabel>assign5label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">0</process:from>
      <process:to type="query">loginflag</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign5label" name="label" nodeType="label">
    <location x="718" y="160"/>
    <figSize height="17" width="32"/>
    <node>assign5</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="shitf" createTime="2013-03-07 18:58:23" date="2013-03-07Z" description="" name="login" version="6.3"/>
  <process:variables>
    <process:variable anyType="com.eos.data.datacontext.UserObject" description="" historyStateLocation="client" isArray="false" name="userObject"/>
    <process:variable description="" historyStateLocation="client" isArray="false" modelType="org.gocom.components.coframe.auth.queryentity.QueryEmpObject" name="empObject"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="false" name="json_args"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="true" name="json_argsdetail"/>
    <process:variable anyType="java.util.HashMap" description="" historyStateLocation="client" isArray="false" name="LoginUserInfo"/>
    <process:variable description="" historyStateLocation="client" isArray="false" name="privatey" primitiveType="String"/>
  </process:variables>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="userId" primitiveType="String"/>
    <process:input description="" isArray="false" name="password" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="retCode" primitiveType="Int"/>
    <process:output description="" isArray="false" name="msg" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

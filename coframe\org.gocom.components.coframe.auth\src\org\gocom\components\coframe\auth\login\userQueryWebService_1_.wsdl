<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:ns1="http://schemas.xmlsoap.org/soap/http" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://tongji.com" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="tongji" targetNamespace="http://tongji.com">
  <wsdl:types>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="unqualified" targetNamespace="http://tongji.com" version="1.0">

  <xs:element name="initUserManagerWebService" type="tns:initUserManagerWebService"/>

  <xs:element name="initUserManagerWebServiceResponse" type="tns:initUserManagerWebServiceResponse"/>

  <xs:complexType name="initUserManagerWebService">
    <xs:sequence>
      <xs:element minOccurs="0" name="arg0" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="initUserManagerWebServiceResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="return" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
  </wsdl:types>
  <wsdl:message name="initUserManagerWebServiceResponse">
    <wsdl:part element="tns:initUserManagerWebServiceResponse" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="initUserManagerWebService">
    <wsdl:part element="tns:initUserManagerWebService" name="parameters">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="UserManagerWebService">
    <wsdl:operation name="initUserManagerWebService">
      <wsdl:input message="tns:initUserManagerWebService" name="initUserManagerWebService">
    </wsdl:input>
      <wsdl:output message="tns:initUserManagerWebServiceResponse" name="initUserManagerWebServiceResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="tongjiSoapBinding" type="tns:UserManagerWebService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="initUserManagerWebService">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="initUserManagerWebService">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="initUserManagerWebServiceResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="tongji">
    <wsdl:port binding="tns:tongjiSoapBinding" name="UserManagerWebServicePort">
      <soap:address location="http://**************:8080/uum-server/webService/userQueryWebService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>

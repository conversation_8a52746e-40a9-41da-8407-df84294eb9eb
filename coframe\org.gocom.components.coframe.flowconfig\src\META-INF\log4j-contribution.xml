<?xml version="1.0" encoding="UTF-8"?>
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/" debug="null" threshold="null">

   <appender class="org.apache.log4j.ConsoleAppender" name="CONSOLE">
   		<param name="Encoding" value="UTF-8"/>
      	<param name="Target" value="System.out"/>
     	<param name="Threshold" value="INFO"/>
      	<layout class="org.apache.log4j.PatternLayout">
         	<param name="ConversionPattern" value="%d{ABSOLUTE} %-5p [%c{1}] %m%n"/>
      	</layout>
   </appender>

   <appender class="com.primeton.ext.common.logging.AppRollingFileAppender" name="ROLLING_FILE">
       	<!--<param name="Threshold" value="INFO"/>-->
       	<param name="Encoding" value="UTF-8"/>
        <param name="File" value="logs/contribution.log"/>
        <param name="Append" value="true"/>
        <param name="MaxFileSize" value="10MB"/>
        <param name="MaxBackupIndex" value="10"/>
        <layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss,SSS}][%p][%C][Line:%L] %m%n"/>
		</layout>
   </appender>

   <root>
  		<level value="info"/>
   		<appender-ref ref="CONSOLE"/>
   		<appender-ref ref="ROLLING_FILE"/>
   </root>

</log4j:configuration>

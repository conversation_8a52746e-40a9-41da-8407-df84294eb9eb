<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="ssoLogin.bizx" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link2" name="link2" displayName="连接线" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>subprocess1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">isEncrypt</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <location x="60" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link1</targetConnections>
    <targetConnections>link3</targetConnections>
    <location x="355" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="String" name="retCode" type="query" valueType="Primitive">retCode</process:return>
      <process:return id="1" language="String" name="msg" type="query" valueType="Primitive">msg</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="62" y="96"/>
    <figSize height="16" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="357" y="96"/>
    <figSize height="16" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="LoginByUserId" displayName="loginByUserId" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="208" y="61"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="0" transactionType="join" varArgs="false">
      <process:partner type="literal">this.loginByUserId</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="retCode" type="query" value="Int" valueType="Primitive">retCode</process:outputVariable>
        <process:outputVariable id="1" name="msg" type="query" value="String" valueType="Primitive">msg</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="179" y="96"/>
    <figSize height="16" width="84"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess1" name="oldLoginByUserId" displayName="oldLoginByUserId" collapsed="false" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link2</targetConnections>
    <location x="210" y="150"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess1label</nodeLabel>
    <process:flow index="0" transactionType="join" varArgs="false">
      <process:partner type="literal">this.oldLoginByUserId</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="userId" type="query" value="String" valueType="Primitive" pattern="reference">userId</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="retCode" type="query" value="Int" valueType="Primitive">retCode</process:outputVariable>
        <process:outputVariable id="1" name="msg" type="query" value="String" valueType="Primitive">msg</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess1label" name="label" nodeType="label">
    <location x="172" y="185"/>
    <figSize height="16" width="102"/>
    <node>subprocess1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="admin" createTime="2024-08-01 11:51:27" date="2024-08-01Z" description="" name="ssoLogin" version="*******"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="isEncrypt" primitiveType="Boolean"/>
    <process:input description="" isArray="false" name="userId" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="retCode" primitiveType="String"/>
    <process:output description="" isArray="false" name="msg" primitiveType="String"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>

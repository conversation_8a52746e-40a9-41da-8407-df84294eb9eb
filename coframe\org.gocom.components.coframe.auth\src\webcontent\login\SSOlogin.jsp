﻿<%@page pageEncoding="UTF-8"%>
<%@page import="com.primeton.cap.AppUserManager"%>
<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge"><!-- 使IE浏览器使用 edge 内核渲染页面 -->
<meta name="renderer" content="webkit"><!-- 使360等国产浏览器使用 webkit 内核渲染页面 -->
<meta name="viewport" content="width=device-width, initial-scale=1"><!-- 响应式布局初始化 -->
<!-- 以上标签必须在最顶端 -->
<html>
 <body>
    <title>
        <%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue("WsLocation", "Property", "appname")%>-登录
    </title>
    <%
   String contextPath = request.getContextPath();
%>

<%
	String original_url=null;
	Object objAttr=request.getAttribute("original_url");
	if(objAttr != null){
		original_url=(String)objAttr;
	}
 %>
    <script type="text/javascript" src="<%=contextPath%>/common/nui/nui.js"></script>
    <script type="text/javascript" src="<%=contextPath%>/coframe/auth/login/js/jquery.min.js"></script>
    <script type="text/javascript">
        nui.parse();
		
		function GetQueryString(name)
		{
		     var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		     var r = window.location.search.substr(1).match(reg);
		     if(r!=null)return  unescape(r[2]); return null;
		}
      
      
     function signIN(cipher) {
        //alert(cipher);
            var json = {
                "cipher": cipher
            };
            nui.ajax({
                url: "com.wx.sso.newcomponent.SSOloginhanchuan.biz.ext",
                type: 'post',
                data: json,
                success: function (text) {
                    var o = nui.decode(text);
                    if (o.exception == null) {
                        var ret = o.retCode;
                        var times = new Date().getTime();
                        if (ret == 1) {
                            location.href =
                                "<%=request.getContextPath() %>/coframe/auth/skin1/index.jsp?time=" +
                                times;
                        } else if (ret == 0) {						
						   nui.alert("用户名错误！");
                        } else if (ret == -2) {
						    nui.alert("用户名错误！");
                        } else if (ret == -3) {
                            nui.alert(o.msg);
                        } else {
						   nui.alert("用户名错误！"); 
                        }
                    } else {
                        nui.alert("登录系统出错");
                    }
                }
            });

        };
        
        
              
     function ssologin(){
        var cipher = GetQueryString("cipher");
        if(cipher!=null&&cipher.length>0){
			signIN(cipher);
        }
     }
        ssologin();
</script>
</body>

</html>
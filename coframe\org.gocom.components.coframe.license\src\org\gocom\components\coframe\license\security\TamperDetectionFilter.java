package org.gocom.components.coframe.license.security;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Collectors;

@WebFilter(urlPatterns = "/index.jsp") // 只拦截 /index.jsp 路径
public class TamperDetectionFilter implements Filter {
	// 预设的、经过“压缩”处理的原始 index.jsp 中特定代码块的内容
	// 这是根据您提供的代码块，经过 normalizeCode 方法处理后得到的结果
	private static final String ORIGINAL_CODE_BLOCK_NORMALIZED = "<%Stringparam=(String)request.getSession().getServletContext().getAttribute(\"ISAUTH\");%>varauthorg={};varlastDays=\"<%=com.eos.foundation.eoscommon.ConfigurationUtil.getUserConfigSingleValue(\"WsLocation\",\"Property\",\"licRemindDays\")%>\";authorg='<%=param%>';authorg=mini.decode(authorg,false);console.info(authorg);if(authorg!=null&&authorg.ISAUTH=='TRUE'){if(Number(authorg.COUNTDOWNDATE)!=null&&Number(authorg.COUNTDOWNDATE)>-1&&Number(authorg.COUNTDOWNDATE)<Number(lastDays)){mini.alert(\"距离授权到期时间还有\"+authorg.COUNTDOWNDATE+\"天，请提醒管理员及时更新授权！\");}}elseif(authorg.ISAUTH=='FALSE'){checkSessionjava(authorg);}elseif(authorg.ISAUTH=='EXPIRE'){checkSessionjava(authorg);mini.alert(\"授权已到期，请提醒管理员及时更新授权！\");}functioncheckSessionjava(f){mini.open({targetWindow:window,//页面对象。默认是顶级页面。url:'Authorization.html',//页面地址title:'授权',//标题iconCls:'',//标题图标width:400,//宽度height:340,//高度allowResize:false,//允许尺寸调节allowDrag:false,//允许拖拽位置showCloseButton:false,//显示关闭按钮showMaxButton:false,//显示最大化按钮showModal:false,//显示遮罩loadOnRefresh:false,//true每次刷新都激发onload事件onload:function(){variframe=this.getIFrameEl();vardata=f;//调用弹出页面方法进行初始化iframe.contentWindow.SetData(data);},ondestroy:function(action){//弹出页面关闭前if(action==\"1\"){window.location.href=\"../login/logout.jsp\";}else{setTimeout('checkSessionjava()',1800000);}}});}";
	// 假设要从 index.jsp 中提取的代码块的起始和结束标记
	private static final String START_MARKER = "<%-- 这是一段需要校验的代码块的开始标记 --%>";
	private static final String END_MARKER = "<%-- 这是一段需要校验的代码块的结束标记 --%>";

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		System.out.println("TamperDetectionFilter initialized.");
		// 在这里可以加载 ORIGINAL_CODE_BLOCK_NORMALIZED 的内容，如果它来自外部文件
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;
		String requestURI = httpRequest.getRequestURI();
		String contextPath = httpRequest.getContextPath();
		String servletPath = requestURI.substring(contextPath.length()); // 获取相对于Web应用的路径
		System.out.println("Intercepting request for: " + servletPath);
		// 1. 只在用户访问 /index.jsp 页面时触发
		if (servletPath.contains("index.jsp")) {
			System.out.println("Processing index.jsp for tamper detection...");
			boolean isTampered = false;
			try {
				// 获取服务器上 index.jsp 文件的真实路径
				// 注意：在某些部署环境下，特别是打包成JAR的Spring Boot应用，
				// getRealPath可能返回null或指向临时目录。
				// 此时需要考虑其他方式获取资源，例如 ClassLoader.getResourceAsStream
				// 但对于传统的WAR部署到Tomcat，getRealPath通常有效。
				String realPath = httpRequest.getSession().getServletContext().getRealPath("/coframe/auth/skin1/index.jsp");
				if (realPath == null) {
					System.err.println(
							"Error: Could not get real path for /index.jsp. This might indicate an issue with deployment or server configuration.");
					isTampered = true; // 无法获取路径，视为异常
				} else {
					Path indexPath = Paths.get(realPath);
					if (!Files.exists(indexPath) || !Files.isReadable(indexPath)) {
						System.err.println("Error: /index.jsp file not found or not readable at " + realPath);
						isTampered = true; // 文件不存在或不可读，视为异常
					} else {
						// 读取 index.jsp 的内容
						// 使用UTF-8编码读取，确保中文和特殊字符正确处理
						String fileContent = Files.lines(indexPath, java.nio.charset.StandardCharsets.UTF_8)
								.collect(Collectors.joining("\n"));
						// 提取需要校验的代码块
						String extractedCodeBlock = extractCodeBlock(fileContent, START_MARKER, END_MARKER);
						if (extractedCodeBlock == null) {
							System.err.println(
									"Error: Could not find code block markers in index.jsp. This could be a tamper or an unexpected file format.");
							isTampered = true; // 标记未找到，视为篡改
						} else {
							// 对提取的代码块进行“字符压缩”（规范化处理）
							String currentCodeBlockNormalized = normalizeCode(extractedCodeBlock);
							// 2. 检测 index.jsp 页面中某块代码是否被篡改
							if (!ORIGINAL_CODE_BLOCK_NORMALIZED.equals(currentCodeBlockNormalized)) {
								System.out.println(
										"Tamper detected! Original (normalized): " + ORIGINAL_CODE_BLOCK_NORMALIZED);
								System.out.println("Current (normalized): " + currentCodeBlockNormalized);
								isTampered = true;
							} else {
								System.out.println("Index.jsp code block integrity check passed.");
							}
						}
					}
				}
			} catch (IOException e) {
				System.err.println("Error reading index.jsp file: " + e.getMessage());
				e.printStackTrace();
				isTampered = true; // 读取文件异常，视为篡改
			}
			// 3. 判断是否被篡改，若是则直接跳转到异常页面
			if (isTampered) {
				// 解决response乱码问题
				httpResponse.setContentType("text/html,charset:UTF-8");
				httpRequest.getRequestDispatcher("/common/noImprimatur.jsp?module=本站(非法篡改)").forward(httpRequest, httpResponse);
				return; // 阻止请求继续向下传递
			}
		}
		// 如果不是 /index.jsp 或者校验通过，则继续执行后续的过滤器或Servlet
		chain.doFilter(request, response);
	}

	@Override
	public void destroy() {
		System.out.println("TamperDetectionFilter destroyed.");
	}

	/**
	 * 规范化代码：移除空白符（空格、制表符、换行符）和HTML/JSP注释。 这是一种简单的“字符压缩”方式，旨在消除格式差异。 更复杂的压缩可能需要特定的库。
	 *
	 * @param code 原始代码字符串
	 * @return 规范化后的代码字符串
	 */
	private String normalizeCode(String code) {
		if (code == null) {
			return "";
		}
		// 移除所有空白符（包括空格、制表符、换页符、回车符、换行符）
		String noWhitespace = code.replaceAll("\\s", "");
		// 移除HTML/JSP注释 <!-- ... --> 和 <%-- ... --%>
		String noComments = noWhitespace.replaceAll("<!--.*?-->", "").replaceAll("<%--.*?--%>", "");
		return noComments;
	}

	/**
	 * 从给定的内容中提取位于起始和结束标记之间的代码块。
	 *
	 * @param fullContent 完整的JSP文件内容
	 * @param startMarker 代码块的起始标记
	 * @param endMarker   代码块的结束标记
	 * @return 提取到的代码块内容，如果未找到标记则返回null
	 */
	private String extractCodeBlock(String fullContent, String startMarker, String endMarker) {
		int startIndex = fullContent.indexOf(startMarker);
		if (startIndex == -1) {
			return null;
		}
		startIndex += startMarker.length(); // 调整到标记之后
		int endIndex = fullContent.indexOf(endMarker, startIndex);
		if (endIndex == -1) {
			return null;
		}
		return fullContent.substring(startIndex, endIndex);
	}
}
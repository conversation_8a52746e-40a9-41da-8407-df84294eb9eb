<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="authentication" detailDescription="" demonstration="" urls="" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="6.3">
  <nodes xsi:type="model:Note" id="node0" name="注释" displayName="注释" nodeType="note" type="note" content="验证用户" title="shitf&#x9;13-3-12 下午2:17">
    <location x="510" y="285"/>
    <size height="100" width="149"/>
  </nodes>
  <nodes xsi:type="process:tStart" id="start0" name="开始" displayName="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <location x="4" y="124"/>
    <size height="28" width="28"/>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" displayName="结束" type="end">
    <targetConnections>link2</targetConnections>
    <targetConnections>link10</targetConnections>
    <targetConnections>link3</targetConnections>
    <targetConnections>link4</targetConnections>
    <targetConnections>link6</targetConnections>
    <targetConnections>link19</targetConnections>
    <targetConnections>link27</targetConnections>
    <targetConnections>link33</targetConnections>
    <location x="945" y="202"/>
    <size height="28" width="28"/>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="Int" name="returnValue" type="query" valueType="Primitive">returnValue</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" displayName="label" nodeType="label">
    <location x="6" y="160"/>
    <figSize height="16" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" displayName="label" nodeType="label">
    <location x="947" y="238"/>
    <figSize height="16" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring0" name="根据用户Id获得用户信息" displayName="getCapUserByUserId" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link5" name="link5" displayName="连接线" isDefault="false" type="transition">
      <bendPoint heightToEnd="1" heightToStart="99" widthToEnd="-224" widthToStart="2"/>
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>assign2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="ISNULL">
          <process:leftOperand type="query">capUser</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link8" name="link8" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>switch0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link11" name="link11" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokeSpring0</sourceNode>
      <targetNode>assign2</targetNode>
    </sourceConnections>
    <targetConnections>link20</targetConnections>
    <location x="180" y="321"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring0label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.rights.user.CapUserService.getCapUserByUserId</process:partner>
      <process:instance instanceName="CapUserService"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userId" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="org.gocom.components.coframe.rights.dataset.CapUser" valueType="Java">capUser</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring0label" name="label" nodeType="label">
    <location x="128" y="357"/>
    <figSize height="16" width="132"/>
    <node>invokeSpring0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign0" name="认证失败" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link14</targetConnections>
    <location x="628" y="223"/>
    <size height="28" width="28"/>
    <nodeLabel>assign0label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">0</process:from>
      <process:to type="query">returnValue</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign0label" name="label" nodeType="label">
    <location x="618" y="259"/>
    <figSize height="16" width="49"/>
    <node>assign0</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign1" name="认证成功" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link15</targetConnections>
    <location x="570" y="176"/>
    <size height="28" width="28"/>
    <nodeLabel>assign1label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">1</process:from>
      <process:to type="query">returnValue</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign1label" name="label" nodeType="label">
    <location x="560" y="212"/>
    <figSize height="16" width="49"/>
    <node>assign1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign2" name="用户不存在" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <targetConnections>link11</targetConnections>
    <location x="374" y="390"/>
    <size height="28" width="28"/>
    <nodeLabel>assign2label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-1</process:from>
      <process:to type="query">returnValue</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign2label" name="label" nodeType="label">
    <location x="358" y="426"/>
    <figSize height="16" width="61"/>
    <node>assign2</node>
  </nodes>
  <nodes xsi:type="process:tSwitch" id="switch0" name="空操作" displayName="空操作" type="switch">
    <sourceConnections xsi:type="process:tLink" description="" id="link9" name="link9" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>invokeSpring1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">capUser/authmode</process:leftOperand>
          <process:rightOperand type="literal">ldap</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link12" name="link12" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>invokeSpring2</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link1" name="link1" displayName="连接线" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="NE">
          <process:leftOperand type="query">capUser/status</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <location x="243" y="285"/>
    <size height="28" width="28"/>
    <nodeLabel>switch0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="switch0label" name="label" nodeType="label">
    <location x="239" y="321"/>
    <figSize height="16" width="37"/>
    <node>switch0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring1" name="ldap认证" displayName="authenricate" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link10" name="link10" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring1</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link9</targetConnections>
    <location x="374" y="299"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring1label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.ldap.LDAPAuthenticator.authenricate</process:partner>
      <process:instance instanceName="LDAPAuthenticatorBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userId" type="query" value="java.lang.String" valueType="Java" pattern="reference">user/userId</process:inputVariable>
      <process:inputVariable id="1" name="password" type="query" value="java.lang.String" valueType="Java" pattern="reference">user/password</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java">returnValue</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring1label" name="label" nodeType="label">
    <location x="363" y="335"/>
    <figSize height="16" width="50"/>
    <node>invokeSpring1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring2" name="密码加密" displayName="encodePassword" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link13" name="link13" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring2</sourceNode>
      <targetNode>invokeSpring3</targetNode>
    </sourceConnections>
    <targetConnections>link12</targetConnections>
    <location x="390" y="254"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring2label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.rights.user.CapUserService.encodePassword</process:partner>
      <process:instance instanceName="CapUserBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="password" type="query" value="java.lang.String" valueType="Java" pattern="reference">password</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">password</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring2label" name="label" nodeType="label">
    <location x="380" y="290"/>
    <figSize height="16" width="49"/>
    <node>invokeSpring2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokeSpring3" name="验证用户" displayName="authentication" type="invoke" index="0" invokeType="invoke_spring" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link14" name="link14" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>assign0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link15" name="link15" displayName="连接线" type="transition">
      <sourceNode>invokeSpring3</sourceNode>
      <targetNode>assign1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">bool</process:leftOperand>
          <process:rightOperand type="literal">true</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link13</targetConnections>
    <location x="476" y="223"/>
    <size height="28" width="28"/>
    <nodeLabel>invokeSpring3label</nodeLabel>
    <process:spring methodType="beanID" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.rights.user.CapUserService.authentication</process:partner>
      <process:instance instanceName="CapUserBean"/>
    </process:spring>
    <process:inputVariables>
      <process:inputVariable id="0" name="userId" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
      <process:inputVariable id="1" name="password" type="query" value="java.lang.String" valueType="Java" pattern="reference">password</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="boolean" valueType="Java">bool</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokeSpring3label" name="label" nodeType="label">
    <location x="466" y="259"/>
    <figSize height="16" width="49"/>
    <node>invokeSpring3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign3" name="非法修改授权页面" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link2" name="link2" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign3</sourceNode>
      <targetNode>end0</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <targetConnections>link1</targetConnections>
    <targetConnections>link21</targetConnections>
    <targetConnections>link22</targetConnections>
    <location x="445" y="176"/>
    <size height="28" width="28"/>
    <nodeLabel>assign3label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-2</process:from>
      <process:to type="query">returnValue</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign3label" name="label" nodeType="label">
    <location x="411" y="212"/>
    <figSize height="16" width="97"/>
    <node>assign3</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo0" name="检查session" displayName="checkandSetsession" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" description="" id="link7" name="link7" displayName="连接线" isDefault="false" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">verifyimgflag</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link23" name="link23" displayName="连接线" lineType="note" isDefault="false" type="exception">
      <bendPoint heightToEnd="-89" heightToStart="-67" widthToEnd="-85" widthToStart="97"/>
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>invokePojo3</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link28" name="link28" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>subprocess0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">verifyimgflag</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <location x="60" y="54"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo synchronization="true">
      <process:partner>org.gocom.components.coframe.auth.login.EOSlock.checkandSetsession</process:partner>
      <process:instance/>
    </process:pojo>
    <process:inputVariables/>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <location x="38" y="90"/>
    <figSize height="16" width="67"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo1" name="检查登陆错误次数" displayName="checklog" type="invoke" index="1" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link17" name="link17" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>invokePojo4</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link18" name="link18" displayName="连接线" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>assign4</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">loginflag</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link25" name="link25" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>assign4</targetNode>
    </sourceConnections>
    <targetConnections>link16</targetConnections>
    <targetConnections>link29</targetConnections>
    <targetConnections>link7</targetConnections>
    <location x="105" y="133"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginLogService.checklog</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="boolean" valueType="Java">loginflag</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <location x="68" y="169"/>
    <figSize height="16" width="97"/>
    <node>invokePojo1</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign4" name="限制登陆" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link19" name="link19" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign4</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link18</targetConnections>
    <targetConnections>link25</targetConnections>
    <location x="455" y="60"/>
    <size height="28" width="28"/>
    <nodeLabel>assign4label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-5</process:from>
      <process:to type="query">returnValue</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign4label" name="label" nodeType="label">
    <location x="445" y="96"/>
    <figSize height="16" width="49"/>
    <node>assign4</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo2" name="检查是否破坏授权页面" displayName="ishtmlupdate" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link20" name="link20" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>invokeSpring0</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link21" name="link21" displayName="连接线" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>assign3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">flag</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link22" name="link22" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>assign3</targetNode>
    </sourceConnections>
    <targetConnections>link31</targetConnections>
    <location x="128" y="299"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginLogService.ishtmlupdate</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables/>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="boolean" valueType="Java">flag</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <location x="79" y="335"/>
    <figSize height="16" width="121"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo3" name="图片验证" displayName="verifyimgflag" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link16" name="link16" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo3</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link26" name="link26" displayName="连接线" type="transition">
      <sourceNode>invokePojo3</sourceNode>
      <targetNode>assign5</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">flag</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link24" name="link24" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo3</sourceNode>
      <targetNode>assign5</targetNode>
    </sourceConnections>
    <targetConnections>link23</targetConnections>
    <targetConnections>link30</targetConnections>
    <location x="256" y="54"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo3label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.login.PictureController.verifyimgflag</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables/>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="boolean" valueType="Java">flag</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo3label" name="label" nodeType="label">
    <location x="243" y="90"/>
    <figSize height="16" width="49"/>
    <node>invokePojo3</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign5" name="验证码错误" displayName="赋值" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link27" name="link27" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign5</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link26</targetConnections>
    <targetConnections>link24</targetConnections>
    <location x="510" y="3"/>
    <size height="28" width="28"/>
    <nodeLabel>assign5label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-6</process:from>
      <process:to type="query">returnValue</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign5label" name="label" nodeType="label">
    <location x="494" y="39"/>
    <figSize height="16" width="61"/>
    <node>assign5</node>
  </nodes>
  <nodes xsi:type="process:tSubprocess" description="" id="subprocess0" name="判断是否已经去掉验证" displayName="getConfigurationUtil" type="subprocess">
    <sourceConnections xsi:type="process:tLink" id="link29" name="link29" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition/>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link30" name="link30" displayName="连接线" type="transition">
      <sourceNode>subprocess0</sourceNode>
      <targetNode>invokePojo3</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">isverifyimg</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link28</targetConnections>
    <location x="149" y="30"/>
    <size height="27" width="27"/>
    <nodeLabel>subprocess0label</nodeLabel>
    <process:flow index="2" synchronization="true" transactionType="join" varArgs="false">
      <process:partner type="literal">org.gocom.components.coframe.auth.newLoginManager.getConfigurationUtil</process:partner>
      <process:inputVariables>
        <process:inputVariable id="0" name="configname" type="literal" value="String" valueType="Primitive" pattern="reference">REMIMGVAL</process:inputVariable>
        <process:inputVariable id="1" name="deptid" type="literal" value="String" valueType="Primitive" pattern="reference">0</process:inputVariable>
      </process:inputVariables>
      <process:outputVariables>
        <process:outputVariable id="0" name="falg" type="query" value="Boolean" valueType="Primitive">isverifyimg</process:outputVariable>
      </process:outputVariables>
    </process:flow>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="subprocess0label" name="label" nodeType="label">
    <location x="102" y="65"/>
    <figSize height="16" width="121"/>
    <node>subprocess0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" description="" id="invokePojo4" name="检查密码是否已过期" displayName="判断密码是否已过期" collapsed="false" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link31" name="link31" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>invokePojo4</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" description="" id="link32" name="link32" displayName="连接线" type="transition">
      <sourceNode>invokePojo4</sourceNode>
      <targetNode>assign6</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">loginflag</process:leftOperand>
          <process:rightOperand type="literal">false</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link34" name="link34" displayName="连接线" lineType="note" type="exception">
      <sourceNode>invokePojo4</sourceNode>
      <targetNode>assign6</targetNode>
    </sourceConnections>
    <targetConnections>link17</targetConnections>
    <location x="102" y="202"/>
    <size height="28" width="22"/>
    <nodeLabel>invokePojo4label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">org.gocom.components.coframe.auth.LoginLogService.checkPasswordInvaldate</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="userid" type="query" value="java.lang.String" valueType="Java" pattern="reference">userId</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="boolean" valueType="Java">loginflag</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo4label" name="label" nodeType="label">
    <location x="59" y="238"/>
    <figSize height="16" width="109"/>
    <node>invokePojo4</node>
  </nodes>
  <nodes xsi:type="process:tAssign" description="" id="assign6" name="密码已过期，限制登录" displayName="赋值" collapsed="false" type="assign">
    <sourceConnections xsi:type="process:tLink" id="link33" name="link33" displayName="连接线" isDefault="true" type="transition">
      <sourceNode>assign6</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link32</targetConnections>
    <targetConnections>link34</targetConnections>
    <location x="402" y="124"/>
    <size height="28" width="28"/>
    <nodeLabel>assign6label</nodeLabel>
    <process:copy>
      <process:from type="literal" pattern="reference">-7</process:from>
      <process:to type="query">returnValue</process:to>
    </process:copy>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="assign6label" name="label" nodeType="label">
    <location x="356" y="160"/>
    <figSize height="16" width="121"/>
    <node>assign6</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info author="shitf" createTime="2013-03-07 19:50:32" date="2013-03-07Z" description="" name="authentication" version="6.3"/>
  <process:variables/>
  <process:inputs varArgs="false">
    <process:input description="" isArray="false" name="userId" primitiveType="String"/>
    <process:input description="" isArray="false" name="password" primitiveType="String"/>
    <process:input description="" isArray="false" name="verifyimgflag" primitiveType="String"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" name="returnValue" primitiveType="Int"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
  <process:transactionType>true</process:transactionType>
</process:tBusinessLogic>
